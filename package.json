{"name": "ats", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --mode development", "build": "run-p type-check \"build-only:prod {@}\" --", "build:dev": "run-p type-check \"build-only:dev {@}\" --", "preview": "vite preview", "build-only:prod": "vite build --mode production", "build-only:dev": "vite build --mode development", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.2.1", "@codemirror/lint": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.7", "codemirror6-bootstrap-theme": "^0.0.1", "echarts": "^5.5.0", "element-plus": "^2.9.10", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jsonlint-mod": "^1.7.6", "less-loader": "^12.2.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.4.21", "vue-captcha": "^1.0.2", "vue-codemirror6": "^1.2.5", "vue-echarts": "^6.7.3", "vue-router": "^4.3.0", "vue3-json-editor": "^1.1.5", "vue3-ts-jsoneditor": "^2.11.2", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.11.26", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.1.2", "typescript": "~5.4.2", "vite": "^5.1.6", "vue-tsc": "^2.0.6"}}