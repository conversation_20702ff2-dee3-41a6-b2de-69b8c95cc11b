import service from ".";
import { ElMessage } from 'element-plus';

// 登录接口函数
export const Logout = (data: { refresh_token: string }) => {
    return service({
        method: 'POST',
        url: '/logout/',
        data: data
    }).then(response => {
        // ElMessage.success('退出登录成功！')
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}