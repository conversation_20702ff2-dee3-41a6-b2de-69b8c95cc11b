import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all data source
export const getAllDataSource:any = (project_id: number, database_type: string, search_name: string, is_active: boolean | string, page: number, page_size: number) => {
  try {
    // 构建URL参数，只包含有效值
    const params = new URLSearchParams()
    params.append('project_id', project_id.toString())
    if (database_type) params.append('database_type', database_type)
    if (search_name) params.append('search_name', search_name)
    if (is_active !== null && is_active !== '' && is_active !== undefined) {
        params.append('is_active', is_active.toString())
    }
    params.append('page', page.toString())
    params.append('page_size', page_size.toString())
    
    const response = service({
      method: 'GET',
      url: `/project/datasources/?${params.toString()}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}