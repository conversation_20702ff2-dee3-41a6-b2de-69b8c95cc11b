import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllOpenaiConfig:any = (id: number) => {
  try {
    const response = service({
      method: 'GET',
      url: `/project/openai-configs/?project_id=${id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}