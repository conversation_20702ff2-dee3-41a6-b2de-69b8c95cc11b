import service from "../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllProjectInfo = async (pageSize: number, page: number, groupId: any[], userId: number, queryName: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/?page_size=${pageSize}&page=${page}&group_id=${groupId}&user_id=${userId}&project_name=${queryName}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}