import service from "../../index";
import { ElMessage } from 'element-plus';

// 登录接口函数
export const postDefaultProject = (data: Object) => {
    return service({
        method: 'POST',
        url: '/project/defaultProject/',
        data: data
    }).then((response: any) => {
        // ElMessage.success('退出登录成功！')
        return response;
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.msg || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}