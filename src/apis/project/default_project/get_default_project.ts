import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getDefaultProject = async (user_name: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/defaultProject/${user_name}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}