import service from "../../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getEnvironmentData = async (environment_id: number, pageSize: number, page: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/environmentData/?environment_id=${environment_id}&page_size=${pageSize}&page_number=${page}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}