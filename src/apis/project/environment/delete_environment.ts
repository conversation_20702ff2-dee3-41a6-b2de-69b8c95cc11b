import service from "../../index"
import { ElMessage } from 'element-plus'

// 获取用户信息 
export const deleteEnvironmentInfo:any = (id: number) => {
    return service({
        method: 'DELETE',
        url: `/project/environment/${id}/`,
        // get,delete方法data可不传
        data: {}
    }).then(response => {
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        console.log(`获取失败了: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}