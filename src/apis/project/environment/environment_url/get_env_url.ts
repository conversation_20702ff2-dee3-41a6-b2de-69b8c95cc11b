import service from "../../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getEnvironmentUrl = async (environment_id: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/environmentUrl/?environment_id=${environment_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}