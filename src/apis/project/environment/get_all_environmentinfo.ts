import service from "../../index"
import { ElMessage } from 'element-plus'

// 根据项目查看
export const getAllEnvironmentInfo = async (projectId: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/environment/?project_id=${projectId}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}