import service from "../../index"
import { ElMessage } from 'element-plus'

// 获取用户信息 
export const deleteMemberInfo:any = (project_id: number, members_ids: []) => {
    return service({
        method: 'DELETE',
        url: '/project/members/',
        // get,delete方法data可不传
        data: {"project_id":project_id, "members_ids":[members_ids]}
    }).then(response => {
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}