import service from "../../index"
import { ElMessage } from 'element-plus'

// 获取可以添加的成员
export const getCanAddMembers = async (pageSize: number, page: number, exist_members: string, query_name: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/projectMembers/?page_size=${pageSize}&page=${page}&exist_members=${exist_members}&query_name=${query_name}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}