// import service from "../index"
// import { ElMessage } from 'element-plus'

// // 获取用户信息 
// export const getAllUserInfo:any = (page_size: number, page: number) => {
//     return service({
//         method: 'GET',
//         url: `/userInfo/?page_size=${page_size}&page=${page}`,
//         // get方法data可不传
//         data: {}
//     }).then(response => {
//         return response;
//     }).catch(error => {
//         const errorMessage = error.response?.data?.msg || error.message
//         // ElMessage.error(`登录失败: ${errorMessage}`);
//         ElMessage.error(errorMessage);
//         throw error; // 继续抛出错误，以便于调用者处理
//     });
// }



import service from "../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllUserInfo = async (pageSize: number, page: number, userName: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/userInfo/?page_size=${pageSize}&page=${page}&userNameQuery=${userName}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}