import service from "../../index"
import { ElMessage } from 'element-plus'

// 删除目录-同步删除目录下的任务
export const deleteUiTestTree:any = (id: number) => {
    return service({
        method: 'DELETE',
        url: `/ui_test/ui_test_tree_folder/${id}/`,
        // get,delete方法data可不传
        data: {}
    }).then(response => {
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}