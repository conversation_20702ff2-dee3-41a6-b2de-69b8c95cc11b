import service from "../index";
import { ElMessage } from 'element-plus';

// 导入任务接口函数
export const postImportTask = (formData: FormData) => {
    return service({
        method: 'POST',
        url: '/ui_test/task_import/',
        data: formData
        // 注意：使用FormData时不要手动设置Content-Type，让浏览器自动设置
    }).then((response: any) => {
        ElMessage.success('任务导入成功！')
        return response;
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}

// 导出任务接口函数
export const postExportTask = (data: { task_ids: number[] }) => {
    return service({
        method: 'POST',
        url: '/ui_test/task_export/',
        data: {
            task_ids: data.task_ids
        },
        responseType: 'blob' // 用于下载文件
    }).then((response: any) => {
        return response;
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
} 