import service from "../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllTask = async (pageSize: number, page: number, search: string, project_id: number, folder_id?: number, status?: string) => {
  try {
    // 构建URL，如果folder_id是0或未定义，则不传递该参数
    let url = `/ui_test/task/?page_size=${pageSize}&page=${page}&search=${search}&project_id=${project_id}`;
    
    // 只有当folder_id有值且不为0时，才添加到URL中
    if (folder_id !== undefined && folder_id !== 0) {
      url += `&folder_id=${folder_id}`;
    }
    
    // 添加状态筛选参数
    if (status !== undefined && status !== '') {
      url += `&status=${status}`;
    }
    
    const response = await service({
      method: 'GET',
      url,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}