import service from ".";
import { ElMessage } from 'element-plus';

// 这种封装在开发中非常常用。这是一个典型的API请求函数，它使用了Promise来处理异步操作。这种封装方式有以下几个优点：

// 1. 代码复用：你可以在多个地方调用这个函数，而不需要每次都写一遍请求代码。
// 2. 错误处理：在函数中使用.catch来处理可能出现的错误，这样调用者就不需要关心错误处理，使得代码更简洁。
// 3. 可读性：这种封装方式使得代码更易读，因为它清楚地分离了成功和失败的处理逻辑。

// 在 loginApi 函数中，你使用了 .then 和 .catch 来处理 Promise，这是异步操作的一种方式。
// 在 onSubmit 函数中，你使用了 async/await 来处理异步操作，这也是异步操作的一种方式。
// 在JavaScript中，async/await和Promise的.then/.catch都是处理异步操作的方式，它们可以互相配合使用。

// 登录接口函数
export const loginApi = (data: { username: string, password: string }) => {
    return service({
        method: 'POST',
        url: '/login/',
        data: data
    }).then(response => {
        // 这里是登录成功后的操作
        // 例如，你可以在这里显示一个成功的消息
        // ElMessage.success(response.data.msg)
        // 然后返回响应以便于后续处理
        // console.log(response)
        // 务必要返回
        return response;

    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}