import service from "../index"
import { ElMessage } from 'element-plus'

// 获取所有父级菜单
export const getMenuTree = async () => {
  try {
    const response = await service({
      method: 'GET',
      url: '/menuTree/',
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.results.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}