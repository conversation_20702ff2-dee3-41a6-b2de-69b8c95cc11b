import service from "../index"
import { ElMessage } from 'element-plus'

// 获取所有父级菜单
export const getButtons = async () => {
  try {
    const response = await service({
      method: 'GET',
      url: '/button/',
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.results.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}