import service from "../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllGroupInfo = async (pageSize: number, page: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/groups/?page_size=${pageSize}&page=${page}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}