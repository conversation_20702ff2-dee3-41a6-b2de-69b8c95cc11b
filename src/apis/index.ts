import axios from 'axios'
import { useStore } from '@/stores'
import Cookies from 'js-cookie'
import { ElMessage } from 'element-plus'
import { tokenErrorStore } from '@/stores/is_token_error_shown'

const tokenErrStore = tokenErrorStore()
const mainStore = useStore()
// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/', // 使用环境变量或默认值
  timeout: 150000 // 请求超时时间
});

// request拦截器
service.interceptors.request.use(
  config => {
    // Add token to header
    const token = mainStore.token
    // console.log('请求拦截器'+token)
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token
    }

    // Set content type only if it's not FormData
    // FormData会自动设置正确的Content-Type，包括boundary
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json'
    }

    // Handle request timeout
    config.timeout = 150000
    return config
  },
  error => {
    console.error(error) // for debug
    Promise.reject(error)
  }
)

// response拦截器
service.interceptors.response.use(
  response => {
    // // Check if the response status is 200
    // if (response.status === 200) {
    //   // console.log('接口返回成功！')
    // } else {
    //   // console.error('接口返回了错误的状态码: ' + response.status)
    // }

    // // Check if the response data exists
    // if (response.data) {
    //   // console.log('响应数据存在')
    // } else {
    //   // console.error('没有拿到响应数据')
    // }
    return response
  },
  error => {
    // 检查错误响应并判断是否因为令牌无效
    if (error.response && error.response.data && error.response.data.code === 'token_not_valid') {
      if (!tokenErrStore.is_token_error_shown) {
        ElMessage.error('令牌无效或已过期，请重新登录。')
        tokenErrStore.setIsTokenErrorShown(true)
        setTimeout(() => {
        // 清除缓存
        mainStore.setToken('')
        mainStore.setRefreshToken('')
        mainStore.setMenus([])
        mainStore.setRoutes([])
        mainStore.setUserInfo({})
        mainStore.setProjectInfo({})

        localStorage.clear()
        Cookies.remove('token')
        window.location.href = '/ats/login'; // 延迟重定向
        }, 1000); // 延迟1000毫秒后执行重定向
      }
    }
    // 对于其他类型的错误，继续传递错误
    return Promise.reject(error);
  }
)

export default service