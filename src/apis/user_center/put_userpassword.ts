import service from "../index";
import { ElMessage } from 'element-plus';

// 登录接口函数
export const putUserCenterPassword = (id: number, data: { old_password: string, password: string }) => {
    return service({
        method: 'PUT',
        url: `/userCenterPasswordModify/${id}/`,
        data: data
    }).then((response: any) => {
        // ElMessage.success('退出登录成功！')
        return response;
        // console.log("response", response)
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.data || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        // console.log("errorMessage", errorMessage)
        ElMessage.error(errorMessage);

        throw error; // 继续抛出错误，以便于调用者处理
    });
}