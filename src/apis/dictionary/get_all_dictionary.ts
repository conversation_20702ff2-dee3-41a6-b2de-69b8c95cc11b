import service from "../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllDictionary = async (pageSize: number, page: number, dic_name?: string, dic_key?: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/dictionary/?page_size=${pageSize}&page=${page}&dic_name=${dic_name}&dic_key=${dic_key}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}