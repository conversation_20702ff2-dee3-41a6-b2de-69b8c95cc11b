import service from "../index"
import { ElMessage } from 'element-plus'

// 获取用户信息 
export const deleteDictionary:any = (id: number) => {
    return service({
        method: 'DELETE',
        url: `/dictionary/${id}/`,
        // get,delete方法data可不传
        data: {}
    }).then(response => {
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}