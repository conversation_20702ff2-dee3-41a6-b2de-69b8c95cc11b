import service from "../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getAllJobInfo = async (pageSize: number, page: number, userName: string, allBoolen: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/jobs/?page_size=${pageSize}&page=${page}&jobNameQuery=${userName}&all=${allBoolen}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}