import service from "../../index"
import { ElMessage } from 'element-plus'

export const getAllSmartFile = async (page_size: number, page: number, project_id: number, id?: number) => {
  try {
    // 构建基本URL
    let url = `/api_test/smart_api_upload_file_list/?page_size=${page_size}&page=${page}&project_id=${project_id}`
    
    // 只有当id存在且不为undefined时才添加到URL
    if (id !== undefined && id !== null) {
      url += `&id=${id}`
    }
    
    const response = await service({
      method: 'GET',
      url,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}