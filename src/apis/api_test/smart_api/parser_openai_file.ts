import service from "../../index";
import { ElMessage } from 'element-plus';

export const parserOpenaiFile = (data: Object) => {
    return service({
        method: 'POST',
        url: '/api_test/parser/',
        data: data
    }).then((response: any) => {
        return response;
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.msg || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}