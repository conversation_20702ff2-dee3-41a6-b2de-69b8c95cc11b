import service from "../../index"
import { ElMessage } from 'element-plus'

export const getResponse = async (folder_id: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/api_response/?folder_id=${folder_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}