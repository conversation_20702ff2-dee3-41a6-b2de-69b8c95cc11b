import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getQuickApiSubData = async (api_name: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/api_data_detail/?api_name=${api_name}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}