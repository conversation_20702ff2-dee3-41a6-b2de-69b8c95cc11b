import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getEnvDropList = async (project_id: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/project/environment/?project_id=${project_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}