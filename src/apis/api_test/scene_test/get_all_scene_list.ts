import service from "../../index"
import { ElMessage } from 'element-plus'

export const getAllSceneList = async (pageSize: number, page: number, project_id: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/scene_info_list/?page_size=${pageSize}&page=${page}&project_id=${project_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}