import service from "../../index"
import { ElMessage } from 'element-plus'

export const getSceneListFromTree = async (tree_id: number, project_id: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/scene_info_list_from_tree/?tree_id=${tree_id}&project_id=${project_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}