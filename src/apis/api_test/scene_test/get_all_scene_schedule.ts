import service from "../../index"
import { ElMessage } from 'element-plus'

export const getAllSceneSchedule = async () => {
  try {
    const response = await service({
      method: 'GET',
      url: `/celery/periodic_task/`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}