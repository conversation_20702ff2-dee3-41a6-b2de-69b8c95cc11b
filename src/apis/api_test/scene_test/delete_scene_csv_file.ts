import service from "../../index"
import { ElMessage } from 'element-plus'

// 获取用户信息 
export const deleteSceneCsvFile:any = (parent_scene_code: string, object_name: string) => {
    return service({
        method: 'DELETE',
        url: `/api_test/api_test_data_file_upload/?parent_scene_code=${parent_scene_code}&object_name=${object_name}`,
        // get,delete方法data可不传
        data: {}
    }).then(response => {
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}