import service from "../../index"
import { ElMessage } from 'element-plus'

// 获取用户信息 
export const deleteSceneList:any = (scene_code: string, if_tmp_scene: string = "false") => {
    return service({
        method: 'DELETE',
        url: `/api_test/scene_info_list/?scene_code=${scene_code}&if_tmp_scene=${if_tmp_scene}`,
        // get,delete方法data可不传
        data: {}
    }).then(response => {
        return response;
    }).catch(error => {
        const errorMessage = error.response?.data?.msg || error.message
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}