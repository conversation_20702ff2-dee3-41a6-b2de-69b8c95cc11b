import service from "../../index";
import { ElMessage } from 'element-plus';

// 生成断言
export const genAssert = (data: Object) => {
    return service({
        method: 'POST',
        url: '/api_test/test_case_builder/',
        data: data
    }).then((response: any) => {
        // ElMessage.success('退出登录成功！')
        return response;
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.msg || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}