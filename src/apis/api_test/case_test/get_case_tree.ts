import service from "../../index"
import { ElMessage } from 'element-plus'
import {putCaseTree} from "@/apis/api_test/case_test/put_case_tree";

// Get all user information
export const getCaseTree = async (project_id: number) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/case_tree_folder/?project_id=${project_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}