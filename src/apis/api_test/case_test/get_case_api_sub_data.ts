import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getCaseApiSubData = async (tree_id: string, type: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/case_data_detail/?tree_id=${tree_id}&query_type=${type}`,

      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}