import service from "../../index"
import { ElMessage } from 'element-plus'

export const getCaseTestResult = async (folder_id: number, query_type: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/case_tests_result/?folder_id=${folder_id}&query_type=${query_type}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}