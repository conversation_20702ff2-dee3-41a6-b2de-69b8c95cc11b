import service from "../../../index"
import { ElMessage } from 'element-plus'

export const getSceneResult = async (scene_code: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/scene_result/?scene_code=${scene_code}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}