import service from "../../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getSaveAsSceneTree = async (project_id: number) => {
    try {
        const response = await service({
            method: 'GET',
            url: `/api_test/scene_api_save_as_folder/?project_id=${project_id}`,
            data: {},
        });
        return response;
    } catch (error: any) {
        const errorMessage = error.response?.data?.msg || error.message;
        ElMessage.error(errorMessage);
        // throw error;
    }
}