import service from "../../index"
import { ElMessage } from 'element-plus'

// Get all user information
export const getCaseOverview = async (folder_id: string) => {
  try {
    const response = await service({
      method: 'GET',
      url: `/api_test/case_folder_overview/?folder_id=${folder_id}`,
      data: {},
    });
    return response;
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message;
    ElMessage.error(errorMessage);
    // throw error;
  }
}