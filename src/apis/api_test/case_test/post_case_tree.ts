import service from "../../index";
import { ElMessage } from 'element-plus';
import {putCaseTree} from "@/apis/api_test/case_test/put_case_tree";

// 登录接口函数
export const postCaseTree = (data: Object) => {
    return service({
        method: 'POST',
        url: '/api_test/case_tree_folder/',
        data: data
    }).then((response: any) => {
        // ElMessage.success('退出登录成功！')
        return response;
    }).catch((error: any) => {
        const errorMessage = error.response?.data?.msg || error.message
        // ElMessage.error(`登录失败: ${errorMessage}`);
        ElMessage.error(errorMessage);
        throw error; // 继续抛出错误，以便于调用者处理
    });
}