// store.ts
import { defineStore } from 'pinia'

interface UiTestState {
    ui_test_data: {
        selectedModel: number | null;
    }
}

export const useUiTestStore = defineStore('ui_test_store', {
    state: (): UiTestState => ({
        ui_test_data: {
            selectedModel: null
        }
    }),
    actions: {
        setUiTestData(newUiTestData: Partial<UiTestState['ui_test_data']>) {
            this.ui_test_data = {
                ...this.ui_test_data,
                ...newUiTestData
            }
        }
    },
    persist: {
        key: 'ui-test-store',
        storage: window.localStorage
    }
})