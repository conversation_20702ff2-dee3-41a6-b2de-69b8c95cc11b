// store.ts
import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import Cookies from 'js-cookie'

export const useStore = defineStore({
  id: 'main_store',
  state: (): { token: string; refresh_token: string; menus: any[]; routes: RouteRecordRaw[]; user_info: object; project_info: object; dic_list: any} => ({
    token: Cookies.get('token') || '',
    refresh_token: localStorage.getItem('refresh_token') || '',
    menus: JSON.parse(localStorage.getItem('menus') || '[]'),
    routes: JSON.parse(localStorage.getItem('routes') || '[]'),
    user_info: JSON.parse(localStorage.getItem('user_info') || '{}'),
    project_info: JSON.parse(localStorage.getItem('project_info') || '{}'),
    dic_list: JSON.parse(localStorage.getItem('dic_list') || '[]')
  }),
  actions: {
    setToken(newToken: string) {
      this.token = newToken
      Cookies.set('token', newToken)
    },
    setRefreshToken(refreshToken: string) {
      this.refresh_token = refreshToken
      localStorage.setItem('refresh_token', refreshToken)
    },
    setMenus(newMenus: []) {
      this.menus = newMenus
      localStorage.setItem('menus', JSON.stringify(newMenus))
    },
    setRoutes(newRoutes: RouteRecordRaw[]) {
      this.routes = newRoutes
      localStorage.setItem('routes', JSON.stringify(newRoutes))
    },
    setUserInfo(newUserInfo: object) {
      this.user_info = newUserInfo
      localStorage.setItem('user_info', JSON.stringify(newUserInfo))
    },
    setProjectInfo(ProjectInfo: object) {
      this.project_info = ProjectInfo
      localStorage.setItem('project_info', JSON.stringify(ProjectInfo))
    },
    setDicList(dicList: any) {
      this.dic_list = dicList
      localStorage.setItem('dic_list', JSON.stringify(dicList))
    }
  }
})

