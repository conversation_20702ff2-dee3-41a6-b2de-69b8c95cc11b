// src/stores/permission.ts

import { defineStore } from 'pinia';

interface PermissionMenu {
    code: string;
    buttons: string[];
    children?: PermissionMenu[];
}

interface PermissionState {
    permissionMenus: PermissionMenu[];
}

export const usePermissionStore = defineStore('permission', {
    state: (): PermissionState => ({
        permissionMenus: JSON.parse(localStorage.getItem('permissionMenus') || '[]')
    }),
    actions: {
        setPermissionMenus(menus: PermissionMenu[]) {
            this.permissionMenus = menus;
            localStorage.setItem('permissionMenus', JSON.stringify(menus))
        },
        getPermissionButtons(menuCode: string): string[] {
            const findMenu = (menus: PermissionMenu[], code: string): PermissionMenu | undefined => {
                for (const menu of menus) {
                    if (menu.code === code) {
                        return menu;
                    }
                    if (menu.children) {
                        const found = findMenu(menu.children, code);
                        if (found) return found;
                    }
                }
                return undefined;
            };

            const menu = findMenu(this.permissionMenus, menuCode);
            // console.log('Searching for menu:', menuCode, 'Found:', menu); // 打印查找过程和结果
            return menu ? menu.buttons : [];
        }
    }
});