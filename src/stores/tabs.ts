import { defineStore } from 'pinia'

interface Tab {
    path: string;
    meta: {
      title: string;
    };
  }

export const useTabsStore = defineStore({
    id: 'tabs',
    state: () => ({
        editableTabsValue: '/ats/index',
        editableTabs: [
            {
                title: '首页',
                name: '/ats/index',
                // content: 'Tab 1 content',
            }
        ],
    }),
    actions: {
        addTab(tab: Tab) {
            // console.log('1111tab.path:', tab.path);
            // console.log('1111editableTabs:', this.editableTabs);
            if (this.editableTabs.findIndex(e => e.name === tab.path) === -1) {
                this.editableTabs.push({
                    title: tab.meta.title,
                    name: tab.path,
                })
                this.editableTabsValue = tab.path
            }
            // console.log('ADDtab后', this.editableTabsValue, this.editableTabs)
        },


        removeTab(targetName: string) {
            let activeName = this.editableTabsValue
            if (activeName === targetName) {
                this.editableTabs.forEach((tab, index) => {
                    if (tab.name === targetName) {
                        const nextTab = this.editableTabs[index + 1] || this.editableTabs[index - 1]
                        if (nextTab) {
                            activeName = nextTab.name
                        }
                    }
                })
            }
            this.editableTabsValue = activeName
            this.editableTabs = this.editableTabs.filter((tab) => tab.name !== targetName)
        },
    },
})