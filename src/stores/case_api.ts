// store.ts
import { defineStore } from 'pinia'

export const useCaseApiStore = defineStore({
    id: 'case_api_store',
    state: (): { case_level: string, case_tree_data: any} => ({
        case_level: '0',
        case_tree_data: {}
    }),
    actions: {
        setCaseLevel(newCaseLevel: string) {
            this.case_level = newCaseLevel
        },
        setCaseTreeData(newCaseTreeData: any) {
            this.case_tree_data = newCaseTreeData
        }
    }
})