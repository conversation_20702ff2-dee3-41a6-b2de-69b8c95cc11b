<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-col :span="7">
                <el-input placeholder="请输入岗位名..." v-model="queryParams.jobNameQuery" clearable
                    @clear="getJobList"></el-input>
            </el-col>
            <el-button type="primary" v-permission="'JOB_MANAGE:查询'" :icon="Search" @click="getJobList">搜索</el-button>
            <el-button type="primary" v-permission="'JOB_MANAGE:新增'" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="job_title" label="岗位名" />
            <el-table-column prop="sort_order" label="排序" />
            <el-table-column prop="creator" label="创建人" />
            <el-table-column v-permission="'JOB_MANAGE:编辑'" prop="if_enable" label="禁用" >
                <template v-slot="scope">
                    <el-switch v-model="scope.row.if_enable" style="--el-switch-on-color: #F56C6C; " @click="switchHandle(scope.row.id, scope.row.if_enable)" inline-prompt active-text="是" inactive-text="否" :active-value=0 :inactive-value=1 />
                </template>
            </el-table-column>
            <el-table-column prop="create_time" label="创建时间">
                <template v-slot="scope">
                    {{ formatDate(scope.row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button type="primary" v-permission="'JOB_MANAGE:编辑'" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <span style="margin-left: 10px;" v-permission="'JOB_MANAGE:删除'">
                        <job_del :jobId="scope.row.id" @deleteConfirmed="updateJobList"></job_del>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    <job_add_dialog v-model="dialogVisible" @updateJobList="updateJobList"></job_add_dialog>
    <job_modify_dialog v-model="modifyDialogVisible" :modifyJobId='modifyJobId' :jobInfo="currentJobInfo"
        @updateJobList="updateJobList"></job_modify_dialog>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getAllJobInfo } from '@/apis/job/get_all_jobinfo'
import { Search, Edit, Plus } from '@element-plus/icons-vue';
import job_add_dialog from './components/job_add_dialog.vue';
import job_del from './components/job_del.vue';
import job_modify_dialog from './components/job_modify_dialog.vue';
import { putJob } from '@/apis/job/put_job'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/util/format_date';

const loading = ref(true)

const queryParams = reactive({
    jobNameQuery: '',
    page_size: 10,
    page: 1
})

const total = ref(0)
const tableData = ref([])

const dialogVisible = ref(false)
// console.log('dialogAddValue1',jobAddDialogVisible)
const modifyDialogVisible = ref(false)

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
    // console.log('dialogAddValue2',jobAddDialogVisible)
}

const modifyJobId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentJobInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyJobId.value = id
    const job = tableData.value.find((job: any) => job.id === id) // 在 tableData 中查找用户的详细信息
    if (job) { currentJobInfo.value = job } // 将找到的用户信息存储在 currentJobInfo 中
}


const getJobList = async () => {
    const res = await getAllJobInfo(queryParams.page_size, queryParams.page, queryParams.jobNameQuery, "")
    if (res) {
        let jobList = res.data.data.results
        // 排序
        let sortedJobList = jobList.sort((a:any, b:any) => a.sort_order - b.sort_order)
        tableData.value = sortedJobList
        total.value = res.data.data.count
        loading.value = false
    }
}

getJobList()

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getJobList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getJobList()
}

// 定义一个事件函数，让子组件触发，需要传递的数据作为事件的参数，这里是子组件直接传整个响应，父组件监听，并处理
// 监听 update 事件只要在上方的子组件引入中加入 @updateJobList="updateJobList"
const updateJobList = (newJobs: any) => {
    if (newJobs) {
        tableData.value = newJobs.data.data.results
        total.value = newJobs.data.data.count
    }
}


const switchHandle = async (id:number, ifEnable: number) => {
    const data = { if_enable: ifEnable };
    const response = await putJob(id, data)
    // console.log(id)
    if (response && response.data.code === 2000) {
        getJobList()
        ElMessage.success('操作成功！')
    }
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}


</style>