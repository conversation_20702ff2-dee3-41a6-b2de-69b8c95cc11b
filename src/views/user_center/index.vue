<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="6">
                <el-card class="box-card">
                    <template #header>
                        <div class="card-header">
                            <span>个人信息</span>
                        </div>
                    </template>
                    <div>
                        <div class="user-center-avatar">
                            <user_avatar_modify :userInfo="userInfo"></user_avatar_modify>
                        </div>
                        <ul class="list-group list-group-striped">
                            <li class="list-group-item">
                                <el-icon>
                                    <UserFilled />
                                </el-icon>&nbsp;&nbsp;用户名称
                                <div class="pull-right">{{ userInfo.username }}({{ userInfo.name }})</div>
                            </li>
                            <li class="list-group-item">
                                <el-icon>
                                    <PhoneFilled />
                                </el-icon>&nbsp;&nbsp;手机号码
                                <div class="pull-right">{{ userInfo.phone }}</div>
                            </li>
                            <li class="list-group-item">
                                <el-icon>
                                    <Comment />
                                </el-icon>&nbsp;&nbsp;用户邮箱
                                <div class="pull-right">{{ userInfo.email }}</div>
                            </li>
                            <li class="list-group-item">
                                <el-icon>
                                    <Management />
                                </el-icon>&nbsp;&nbsp;所属部门
                                <div class="pull-right">{{ userInfo.department }}</div>
                            </li>
                            <li class="list-group-item">
                                <el-icon>
                                    <Avatar />
                                </el-icon>&nbsp;&nbsp;岗位名称
                                <div class="pull-right">{{ userInfo.job_title }}</div>
                            </li>
                            <li class="list-group-item">
                                <el-icon>
                                    <Platform />
                                </el-icon>&nbsp;&nbsp;创建时间
                                <div class="pull-right">{{ formatDate(userInfo.date_joined) }}</div>
                            </li>
                        </ul>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="18">
                <el-card class="box-card">
                    <el-tabs v-model="activeName" class="user-center-tabs">
                        <el-tab-pane label="用户资料" name="first">
                            <user_profile_modify :prop="userInfo"></user_profile_modify>
                        </el-tab-pane>
                        <el-tab-pane label="密码修改" name="second">
                            <user_password_modify :userId="userInfo.id"></user_password_modify>
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useStore } from '@/stores'
import user_profile_modify from './user_profile_modify.vue'
import user_password_modify from './user_password_modify.vue';
import user_avatar_modify from './user_avatar_modify.vue';

const mainStore = useStore()
const activeName = ref('first')

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
}

interface UserInfo {
    avatar: string,
    username: string,
    name: string,
    phone: string,
    department: string,
    date_joined: string,
    job_title: string,
    email: string
    id: number
}

// 每当 user_info 发生变化时，Vue 都会自动更新这些元素
// 用于当依赖的数据发生变化时，自动计算新的值。它是基于其依赖项的缓存的，只有当依赖项发生变化时才会重新计算。

// watch：用于响应数据的变化，并执行一些自定义的逻辑。与 computed 不同，watch 不会计算新的值，而是在依赖项发生变化时执行一个函数。
const userInfo = computed(() => mainStore.user_info as UserInfo)
const squareUrl = (mainStore.user_info as UserInfo).avatar
</script>

<style  lang='less' scoped>
.list-group-striped>.list-group-item {
    border-left: 0;
    border-right: 0;
    border-radius: 0;
    padding-left: 0;
    padding-right: 0;
}

.list-group-item {
    border-bottom: 1px solid #e7eaec;
    border-top: 1px solid #e7eaec;
    margin-bottom: -1px;
    padding: 11px 0;
    font-size: 13px;
}

.pull-right {
    float: right !important;
}

:v-deep .el-card__body {
    height: 230px;
}

:v-deep .box-card {
    height: 450px;
}

.user-center-avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
}
</style>