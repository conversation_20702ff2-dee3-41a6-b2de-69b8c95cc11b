<template>
  <el-form ref="ruleFormRef" :model="form" status-icon :rules="rules" label-width="80px" class="demo-ruleForm">
    <el-form-item label="手机号码" prop="phone">
      <el-input class='user-input' v-model="form.phone" type="text" autocomplete="off" />
    </el-form-item>
    <el-form-item label="用户邮箱" prop="email">
      <el-input class='user-input' v-model="form.email" type="text" autocomplete="off" />
    </el-form-item>
    <el-form-item label="用户姓名" prop="name">
      <el-input class='user-input' v-model="form.name" type="text" autocomplete="off" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { type FormInstance, ElMessage } from 'element-plus'
import { onMounted } from 'vue'
import { putUserCenterInfo } from '@/apis/user_center/put_userinfo'
import { getUserInfo } from '@/apis/user/get_userinfo'
import { useStore } from '@/stores'

const mainStore = useStore()
// 保证defineProps里面的对象和传过来的名称一致
const props = defineProps({
  prop: Object
})



// console.log('props'+props.prop?.username)


const form = ref({
  phone: '',
  email: '',
  name: ''
})

onMounted(() => {
  if (props.prop) {
    form.value = {
      phone: props.prop?.phone || '',
      email: props.prop?.email || '',
      name: props.prop?.name || ''
    }
  }
})
// console.log(form)

const rules = ref({
  email: [{ required: true, message: "邮箱地址不能为空", trigger: "blur" }, { type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
  phone: [{ required: true, message: "手机号码不能为空", trigger: "blur" }, { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }],
});


const ruleFormRef = ref<FormInstance>() as any


const submitForm = async (formEl: FormInstance) => {
  formEl.validate(async (valid) => {
    if (valid) {
      const response = await putUserCenterInfo(props.prop?.id, form.value)
      if (response.data.code === 2000) {
        const userInfoResponse = await getUserInfo(props.prop?.id)
        if (userInfoResponse.data.code === 2000) {
          mainStore.setUserInfo({
            ...mainStore.user_info,
            email: form.value.email,
            phone: form.value.phone,
            name: form.value.name
          })
          ElMessage.success('保存成功！')
        }
      }
    } else {
    }
  })
}

</script>

<style lang='less' scoped>
.user-input{
  width: 50%;
}
</style>