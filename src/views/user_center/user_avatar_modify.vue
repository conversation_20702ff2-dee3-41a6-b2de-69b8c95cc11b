<template>
  <!-- 多次上传，取消显示上传列表，指定上传字段名为avatar -->
  <el-upload ref="uploadRef" :headers="token" method="PUT" class="avatar-uploader" :action="url" :show-file-list="true"
    :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :limit=1 name="avatar">
    <img v-if="imageUrl" :src="imageUrl" class="avatar" />
    <el-icon v-else class="avatar-uploader-icon">
      <Plus />
    </el-icon>
  </el-upload>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useStore } from '@/stores'

import type { UploadProps } from 'element-plus'

const uploadRef = ref<any>(null)

const props = defineProps({
  userInfo: Object
})

const mainStore = useStore()
const token = { Authorization: "Bearer " + mainStore.token }

type UserInfo = {
  avatar: string,
}
const userAvatarUrl = (mainStore.user_info as UserInfo).avatar

const url = userAvatarUrl

const imageUrl = ref(props.userInfo?.avatar)


const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile,
) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!)
  uploadRef.value.clearFiles() // 清除文件列表
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
    ElMessage.error('上传的头像格式必须为jpeg或png！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('上传的头像请控制在2mb以内！')
    return false
  }
  return true
}

</script>

<style scoped>
.default-avatar {
  background-color: #fff;
  width: 178px;
  height: 178px;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>