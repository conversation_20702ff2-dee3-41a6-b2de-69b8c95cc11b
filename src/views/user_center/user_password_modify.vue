<template>
  <el-form ref="ruleFormRef" :model="form" status-icon :rules="rules" label-width="80px" class="demo-ruleForm">
    <el-form-item label="旧密码" prop="old_password">
      <el-input class='user-input' v-model="form.old_password" type="text" autocomplete="off" />
    </el-form-item>
    <el-form-item label="新密码" prop="password">
      <el-input class='user-input' v-model="form.password" type="text" autocomplete="off" />
    </el-form-item>
    <el-form-item label="确认密码" prop="confirm_password">
      <el-input class='user-input' v-model="form.confirm_password" type="text" autocomplete="off" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
    </el-form-item>
  </el-form>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { type FormInstance, ElMessage } from 'element-plus'
import { putUserCenterPassword } from '@/apis/user_center/put_userpassword'

const props = defineProps({
  userId: Number,
})

const form = reactive({
  old_password: '',
  password: '',
  confirm_password: ''
})


const rules = ref({
  old_password: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  password: [{ required: true, message: "新密码不能为空", trigger: "blur" }],
  confirm_password: [{ required: true, message: "确认密码不能为空", trigger: "blur" }],
});

const ruleFormRef = ref<FormInstance>() as any

const submitForm = async (formEl: FormInstance) => {
  formEl.validate(async (valid) => {
    if (valid) {
      if (form.password !== form.confirm_password) {
        ElMessage.warning('新密码和确认密码不一致！');
      }

      const response = await putUserCenterPassword(props.userId as any, { old_password: form.old_password, password: form.password})
      if (response.data.code === 2000) {
        ElMessage.success('密码修改成功！')
      }
    }
    else {
    }
  })
}

</script>
  
<style lang='less' scoped>
.user-input {
  width: 50%;
}
</style>