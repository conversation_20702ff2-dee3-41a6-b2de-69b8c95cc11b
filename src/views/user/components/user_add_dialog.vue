<template>
  <el-dialog v-model="dialogVisible" title="新增用户" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="65px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="账号" prop="username">
        <el-input v-model="ruleForm.username" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="ruleForm.email" />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input v-model="ruleForm.phone" />
      </el-form-item>
      <el-form-item label="用户组" prop="groupId">
        <el-select v-model="ruleForm.groupId" placeholder="请选择用户组">
          <el-option v-for="group in allGroups" :label="group.name" :value="group.id" :key="group.id"/>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="部门" prop="department">
        <el-select v-model="ruleForm.department" placeholder="Activity zone">
          <el-option label="技术部" value="shanghai" />
          <el-option label="销售部" value="beijing" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="岗位" prop="job_title">
        <el-select v-model="ruleForm.job_title" placeholder="请选择岗位">
          <el-option v-for="job in allJobs" :label="job.job_title" :value="job.job_title" :key="job.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="禁用" prop="is_active">
        <el-switch v-model="ruleForm.is_active" :active-value="0" :inactive-value="1"/>
      </el-form-item>
    </el-form>
    <el-alert v-if="true" title="默认初始密码：Xyhj@123" :closable="true" style="line-height: 3px;" type="success">
    </el-alert>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postUser } from '@/apis/user/post_user'
import { getAllUserInfo } from '@/apis/user/get_all_userinfo'


// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  allGroups: Object,
  allJobs: Object
})

const dialogVisible = ref(false)

const allGroups:any = ref({})
const allJobs:any = ref({})

watchEffect(() => {
  dialogVisible.value = props.modelValue
  allGroups.value = props.allGroups
  allJobs.value = props.allJobs
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.username = ''
    ruleForm.password = 'Xyhj@123'
    ruleForm.email = ''
    ruleForm.name = ''
    ruleForm.phone = ''
    ruleForm.groupId = ''
    // ruleForm.department = ''
    ruleForm.job_title = ''
    ruleForm.is_active = 1
  }
})

const emit = defineEmits(['update:modelValue', 'updateUserList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

// const handleClose = (done: () => void) => {
//   ElMessageBox.confirm('Are you sure to close this dialog?')
//     .then(() => {
//       done()
//       emit('update:modelValue', false) // 更新modelValue的值
//     })
//     .catch(() => {
//       // catch error
//     })
// }


// ruleFormRef 和 ruleForm 的关系是，ruleFormRef 通过 ref 属性绑定到 el-form 组件上，这样 ruleFormRef 就可以引用到这个表单实例，然后我们就可以通过 ruleFormRef.value 来访问到这个表单实例，进而调用表单的方法，例如 validate。

// 而 ruleForm 则是用来存储表单的数据的。在 el-form 组件中，我们通过 v-model 指令将 ruleForm 绑定到表单的数据模型上，这样当用户在表单中输入数据时，这些数据就会自动存储到 ruleForm 中。

// 所以，ruleForm 能拿到用户填写的信息，是因为它被绑定到了表单的数据模型上，而 ruleFormRef 则是用来引用表单实例的，我们可以通过它来调用表单的方法。


interface RuleForm {
  username: string
  password: string
  email: string
  name: string
  phone: string
  groupId: string
  // department: string
  job_title: string
  is_active: number
}

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  username: '',
  password: '123456',
  name: '',
  email: '',
  phone: '',
  groupId: '',
  // department: '',
  job_title: '',
  is_active: 1
})

const rules = reactive<FormRules<RuleForm>>({
  username: [
    { required: true, message: '请输入账号名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度大于3小于20', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9]*$/,
      message: '仅支持英文和数字，不允许中文',
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { min: 11, max: 11, message: '请输入有效的手机号', trigger: 'blur' },
    {
      pattern: /0?(13|14|15|17|18|19)[0-9]{9}/,
      message: '请输入有效的手机号',
      trigger: 'blur'
    }
  ],
  groupId: [
    { required: true, message: '请选择用户组', trigger: 'blur' },
  ],
  // department: [
  //   { required: true, message: '请选择部门', trigger: 'blur' },
  // ],
  job_title: [
    { required: true, message: '请选择岗位', trigger: 'blur' },
  ],
  // delivery: [
  //   { required: true, message: '请选择岗位', trigger: 'blur' },
  // ]
})


// 在前端开发中，表单验证通常在两个地方进行：一是在用户输入时（即前端），二是在提交表单时（即后端）。这两个阶段的验证都是必要的，各有其目的：

// 1. 用户输入时的验证：这是为了提供即时反馈，让用户知道他们的输入是否符合要求。这可以提高用户体验，因为用户可以在提交表单之前就知道是否有错误，而不需要等待服务器的响应。

// 2. 提交表单时的验证：这是为了防止因为某些原因（例如用户绕过前端验证，或者前端验证代码出现错误等）导致的无效或恶意的数据提交到服务器。这是一种安全措施，确保只有符合要求的数据才能被处理和存储。
const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await postUser(ruleForm)
      if (response && response.data.code === 2000) {
        const userInfoResponse = await getAllUserInfo(10, 1, "")
        if (userInfoResponse && userInfoResponse.data.results.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateUserList', userInfoResponse)  // 触发 update 事件
        }
      } else {
        ElMessage.error(response.data.msg)
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}



</script>
<style scoped></style>