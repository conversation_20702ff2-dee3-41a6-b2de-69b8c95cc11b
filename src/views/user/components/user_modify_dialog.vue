<template>
  <el-dialog v-model="modifyDialogVisible" title="编辑用户" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="65px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="账号" prop="username">
        <el-input disabled v-model="ruleForm.username" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="ruleForm.email" />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input v-model="ruleForm.phone" />
      </el-form-item>
      <el-form-item label="用户组" prop="groupId">
        <el-select :disabled="props.modifyUserID === 1" v-model="ruleForm.groupId" placeholder="请选择用户组">
          <el-option v-for="group in allGroups" :label="group.name" :value="group.id" :key="group.id"/>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="部门" prop="department">
        <el-select v-model="ruleForm.department" placeholder="Activity zone">
          <el-option label="技术部" value="shanghai" />
          <el-option label="销售部" value="beijing" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="岗位" prop="job_title">
        <el-select v-model="ruleForm.job_title" placeholder="请选择岗位">
          <el-option v-for="job in allJobs" :label="job.job_title" :value="job.job_title" :key="job.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="禁用" prop="is_active">
        <el-switch :disabled="props.modifyUserID === 1" v-model="ruleForm.is_active" style="--el-switch-on-color: #F56C6C; " :active-value=false
          :inactive-value=true />
      </el-form-item>
    </el-form>
    <!-- <el-alert v-if="true" title="默认初始密码：Xyhj@123" :closable="true" style="line-height: 3px;" type="success">
      </el-alert> -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { putUser } from '@/apis/user/put_user'
import { getAllUserInfo } from '@/apis/user/get_all_userinfo'


// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  modifyUserID: Number,
  userInfo: Object,
  allJobs: Object,
  allGroups: Object
})

const modifyDialogVisible = ref(false)

const allJobs: any = ref({})

const allGroups: any = ref({})

const emit = defineEmits(['update:modelValue', 'updateUserList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

const getGroupName = (groupId: number) => {
    const group = allGroups.value.find((group: any) => group.id === groupId)
    return group ? { id: group.id, name: group.name } : { id: '', name: '' }
}

interface RuleForm {
  username: string
  email: string
  name: string
  phone: string
  groupId: string
  // department: string
  job_title: string
  is_active: number
}



const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  username: props.userInfo?.username,
  email: props.userInfo?.emali,
  name: props.userInfo?.name,
  phone: props.userInfo?.phone,
  groupId: props.userInfo?.group && props.userInfo.group.length > 0 ? getGroupName(props.userInfo.group[0]).id : '',
  // department: props.userInfo?.department,
  job_title: props.userInfo?.job_title,
  is_active: props.userInfo?.is_active
})

watchEffect(() => {
  modifyDialogVisible.value = props.modelValue
  Object.assign(ruleForm, props.userInfo)
  ruleForm.groupId = props.userInfo?.group && props.userInfo.group.length > 0 ? getGroupName(props.userInfo.group[0]).id : '';
  allJobs.value = props.allJobs
  allGroups.value = props.allGroups
})

const rules = reactive<FormRules<RuleForm>>({
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { min: 11, max: 11, message: '请输入有效的手机号', trigger: 'blur' },
    {
      pattern: /0?(13|14|15|17|18|19)[0-9]{9}/,
      message: '请输入有效的手机号',
      trigger: 'blur'
    }
  ],
  groupId: [
    { required: true, message: '请选择用户组', trigger: 'blur' },
  ],
  // department: [
  //   { required: true, message: '请选择部门', trigger: 'blur' },
  // ],
  job_title: [
    { required: true, message: '请选择岗位', trigger: 'blur' },
  ],
  // delivery: [
  //   { required: true, message: '请选择岗位', trigger: 'blur' },
  // ]
})


// 在前端开发中，表单验证通常在两个地方进行：一是在用户输入时（即前端），二是在提交表单时（即后端）。这两个阶段的验证都是必要的，各有其目的：

// 1. 用户输入时的验证：这是为了提供即时反馈，让用户知道他们的输入是否符合要求。这可以提高用户体验，因为用户可以在提交表单之前就知道是否有错误，而不需要等待服务器的响应。

// 2. 提交表单时的验证：这是为了防止因为某些原因（例如用户绕过前端验证，或者前端验证代码出现错误等）导致的无效或恶意的数据提交到服务器。这是一种安全措施，确保只有符合要求的数据才能被处理和存储。
const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await putUser(props.modifyUserID as any, ruleForm)
      if (response && response.data.code === 2000) {
        const userInfoResponse = await getAllUserInfo(10, 1, "")
        if (userInfoResponse && userInfoResponse.data.results.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateUserList', userInfoResponse)  // 触发 update 事件
        }
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}



</script>
<style scoped></style>