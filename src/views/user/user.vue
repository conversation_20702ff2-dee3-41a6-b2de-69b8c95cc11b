<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-col :span="7">
                <el-input placeholder="请输入用户名..." v-model="queryParams.userNameQuery" clearable
                    @clear="getUserList"></el-input>
            </el-col>
            <el-button type="primary" v-permission="'USERS:查询'" :icon="Search" @click="getUserList">搜索</el-button>
            <el-button type="primary" v-permission="'USERS:新增'" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="username" label="用户名" width="auto" />
            <el-table-column prop="email" label="电子邮箱" width="auto" />
            <!-- <el-table-column prop="name" label="姓名" width="auto" /> -->
            <el-table-column prop="phone" label="手机号" width="auto" />
            <!-- <el-table-column prop="department" label="部门" width="180" /> -->
            <el-table-column prop="job_title" label="岗位" width="auto" />
            <el-table-column prop="group" label="用户组" width="auto">
                <template v-slot="scope">
                    <el-tag v-for="(item, index) in scope.row.group" :key="index">
                        {{ getGroupName(item) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-permission="'USERS:禁用'" prop="is_active" label="禁用" width="80px">
                <template v-slot="scope">
                    <el-switch v-model="scope.row.is_active" style="--el-switch-on-color: #F56C6C; "
                        @click="switchHandle(scope.row.id, scope.row.is_active)" inline-prompt active-text="是"
                        inactive-text="否" :active-value=false :inactive-value=true :disabled="scope.row.username === 'admin'"/>
                </template>
            </el-table-column>
            <el-table-column prop="date_joined" label="创建时间" width="auto">
                <template v-slot="scope">
                    {{ formatDate(scope.row.date_joined) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" width="auto" align="center">
                <template v-slot="scope">
                    <el-button type="primary" v-permission="'USERS:编辑'" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <span style="margin-left: 10px;" v-permission="'USERS:删除'">
                        <user_del :userId="scope.row.id" @deleteConfirmed="updateUserList"></user_del>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    <user_add_dialog v-model="dialogVisible" :allJobs="allJobs" :allGroups="allGroups" @updateUserList="updateUserList">
    </user_add_dialog>
    <user_modify_dialog v-model="modifyDialogVisible" :allJobs="allJobs" :allGroups="allGroups" :modifyUserID='modifyUserID'
        :userInfo="currentUserInfo" @updateUserList="updateUserList"></user_modify_dialog>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getAllUserInfo } from '@/apis/user/get_all_userinfo'
import { Search, Edit, Plus } from '@element-plus/icons-vue';
import user_add_dialog from './components/user_add_dialog.vue';
import user_del from './components/user_del.vue';
import user_modify_dialog from './components/user_modify_dialog.vue';
import { putUser } from '@/apis/user/put_user'
import { ElMessage } from 'element-plus'
import { getAllJobInfo } from '@/apis/job/get_all_jobinfo'
import { getAllGroupInfo } from '@/apis/group/get_all_groupinfo';
import { formatDate } from '@/util/format_date';

const loading = ref(true)

const queryParams = reactive({
    userNameQuery: '',
    page_size: 10,
    page: 1
})

const total = ref(0)
const tableData = ref([])

const dialogVisible = ref(false)
// console.log('dialogAddValue1',userAddDialogVisible)
const modifyDialogVisible = ref(false)

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
    // console.log('dialogAddValue2',userAddDialogVisible)
}

const modifyUserID = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentUserInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息

const allJobs: any = ref({})

const allGroups: any = ref([])


const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyUserID.value = id
    const user = tableData.value.find((user: any) => user.id === id) // 在 tableData 中查找用户的详细信息
    if (user) { currentUserInfo.value = user } // 将找到的用户信息存储在 currentUserInfo 中
}


const getUserList = async () => {
    const res = await getAllUserInfo(queryParams.page_size, queryParams.page, queryParams.userNameQuery)
    if (res) {
        tableData.value = res.data.results.data
        total.value = res.data.count
        loading.value = false
    }
}

getUserList()

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getUserList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getUserList()
}

// 定义一个事件函数，让子组件触发，需要传递的数据作为事件的参数，这里是子组件直接传整个响应，父组件监听，并处理
// 监听 update 事件只要在上方的子组件引入中加入 @updateUserList="updateUserList"
const updateUserList = (newUsers: any) => {
    if (newUsers) {
        tableData.value = newUsers.data.results.data
        total.value = newUsers.data.count
    }
}


const switchHandle = async (id: number, isActive: boolean) => {
    const user:any = tableData.value.find((user: any) => user.id === id)
    if (user && user.username === 'admin') {
        return
    }
    const data = { is_active: isActive };
    const response = await putUser(id, data)
    if (response && response.data.code === 2000) {
        getUserList()
        ElMessage.success('操作成功！')
    }
}

const getJobList = async () => {
    const res = await getAllJobInfo(NaN, NaN, '', "true")
    if (res) {
        allJobs.value = res.data.data
    }
}

getJobList()

const getGroupList = async () => {
    const res = await getAllGroupInfo(30, 1)
    if (res) {
        allGroups.value = res.data.results.data
    }
}

getGroupList()

const getGroupName = (groupId: number) => {
    const group = allGroups.value.find((group: any) => group.id === groupId)
    return group ? group.name : ''
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}

.app-container {
    width: 100%;
    height: 100vh;
}
</style>