<template>
  <div class="main">
    <code-mirror v-model="code_val" wrap basic :lang="lang" style="height: 400px;" :extensions="extensions" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRaw } from 'vue';
import CodeMirror from 'vue-codemirror6'
import { oneDark } from '@codemirror/theme-one-dark'
import { python } from '@codemirror/lang-python';
import { indentUnit } from '@codemirror/language'

const emit = defineEmits(['updatePostData'])

const props = defineProps({
  PostScriptData: String
})

// Python函数模板
const python_template = `def ats_script(res_json_data, res_url, res_headers, res_status_code, context):
    """
    后置脚本：在接口响应后执行，用于生成变量（不做断言和提取）

    参数说明：
    - res_json_data: 接口响应 JSON（类型为 dict）
    - res_url: 响应 URL（str）
    - res_headers: 响应头（dict）
    - res_status_code: 响应状态码（int）
    - context: 当前变量池，包括项目全局变量、环境变量、前置接口的返回变量等

    内置包与方法（直接使用无需import、其它包不支持，如需扩展请联系管理员）：
    - time、random、re、requests、json、datetime、math
    - print、len、str、int、float、bool、dict、list、tuple、type、isinstance、range、enumerate

    返回值：
    - dict：
        {
            "project": { ... },  # 当前项目作用域变量，可跨接口使用
            "env": { ... }       # 当前环境作用域变量，用于登录态、请求头等
        }
    """

    # 从响应数据提取字段
    post_user_id = res_json_data.get("user_id", 1)
    post_token = res_json_data.get("token", "post_token")

    # 响应头信息
    post_content_type = res_headers.get("Content-Type", "application/json")

    # 构造认证字段
    post_auth = f"Bearer {post_token}" if post_token else ""

    # 从 context 中获取变量
    post_request_id = context.get("request_id", "123")

    # 生成变量到项目全局变量或环境变量，后续接口中可以用{{变量名}}引用
    return {
        "env": {
            "post_auth": post_auth,
            "post_token": post_token,
            "post_content_type": post_content_type,
            "post_status_code": res_status_code
        },
        "project": {
            "post_user_id": post_user_id,
            "post_request_id": post_request_id
        }
    }`;

// 初始化代码值
const code_val = ref(python_template);

const lang = python();
const extensions = [oneDark, indentUnit.of("    ")];

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
  emit('updatePostData', toRaw(code_val.value))
}

// 监听代码变化，发送给父组件
watch(code_val, () => {
  updateData()
}, { deep: true })

// 监听从父组件传入的数据
watch(() => props.PostScriptData, (newValue) => {
  if (newValue && newValue.trim() !== '') {
    code_val.value = newValue
  } else {
    code_val.value = python_template
  }
}, { immediate: true })
</script>

<style>
/* required! */
.cm-editor {
  height: 100%;
}
</style>
