<template>
  <el-radio v-model="radio" label="1" @change="updateContentType">none</el-radio>
  <el-radio v-model="radio" label="2" @change="updateContentType">form-data</el-radio>
  <el-radio v-model="radio" label="3" @change="updateContentType">x-www-form-urlencoded</el-radio>
  <el-radio v-model="radio" label="4" @change="updateContentType">json</el-radio>
  <div class="main" v-if="radio === '4'">
    <json_codemirror :BodyData="bodyData" @updateJsonData="updateJsonData"></json_codemirror>
  </div>
  <div class="main" v-if="radio === '2'">
    <form_data :FormData="bodyData" @updateFormData="updateFormData"></form_data>
  </div>
  <div class="main" v-if="radio === '3'">
    <form_urlencoded :FormUrlencodedData="bodyData" @updateFormUrlencoded="updateFormUrlencoded"></form_urlencoded>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import form_data from './request_body/form_data.vue'
import form_urlencoded from './request_body/form_urlencoded.vue'
import json_codemirror from './request_body/json_codemirror.vue'
const radio = ref('4')

const props = defineProps({
  BodyData: Array
})

const emit = defineEmits(['updatePostData'])

// 更新请求体类型
const updateContentType = (data: any) => {
  if (data === '1') {
    emit('updatePostData', '', 'none')
  } else if (data === '2') {
    emit('updatePostData', '', 'form-data')
  } else if (data === '3') {
    emit('updatePostData', '', 'x-www-form-urlencoded')
  } else if (data === '4') {
    emit('updatePostData', '', 'json')
  }
}

const updateJsonData = (data: any) => {
  emit('updatePostData', data, 'json')
};

const updateFormData = (data: any) => {
  emit('updatePostData', data, 'form-data')
};

const updateFormUrlencoded = (data: any) => {
  emit('updatePostData', data, 'x-www-form-urlencoded')
};

const bodyData: any = ref([])
watch(() => props.BodyData, () => {
  if (props.BodyData) {
    bodyData.value = props.BodyData;
    // 使用可选链和空值合并运算符进行安全访问和赋值
    const bodyType = (props.BodyData[0] as any)?.body_type ?? '0';
    if (bodyType === '0') {
      radio.value = '1';
    } else if(bodyType === '1') {
      radio.value = '4';
    } else if(bodyType === '2') {
      radio.value = '2';
    } else if(bodyType === '3') {
      radio.value = '3';
    }
  }
}, { deep: true, immediate: true });

</script>

<style>
/* required! */
.cm-editor {
  height: 100%;
}
</style>
