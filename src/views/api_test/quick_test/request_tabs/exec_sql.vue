<template>
    <div class="exec-sql-container">
        <!-- 功能介绍 -->
        <el-alert 
            description="选择数据源 → SQL查询 → 存储为变量 → 运行接口。查询结果可保存为变量供接口测试使用，支持动态获取最新数据。" 
            type="info" 
            :closable="false" 
            :show-icon="false"
            class="feature-intro" />
        <!-- 表单部分 -->
        <el-form ref="ruleFormRef" style="max-width: 100%" :model="ruleForm" :rules="rules" label-width="auto">
            <el-form-item label="数据源" prop="id">
                <el-select v-model="ruleForm.id" placeholder="请选择数据源" @change="handleDataSourceChange">
                    <el-option v-for="item in options" :key="item.id"
                        :label="item.name + ' (' + item.database_type + ':' + item.host + ':' + item.port + '/' + item.database_name + ') '"
                        :value="item.id" />
                </el-select>
            </el-form-item>
        </el-form>

        <!-- 数据源信息展示 -->
        <el-card class="datasource-info" shadow="never" v-if="selectedDataSource">
            <div class="info-header">
                <h5>数据源信息</h5>
            </div>
            <div class="info-content">
                <el-descriptions :column="4" size="small">
                    <el-descriptions-item label="数据源名称">
                        <el-tag type="primary">{{ selectedDataSource.name || '-' }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="数据库类型">
                        <el-tag :type="getDatabaseTypeColor(selectedDataSource.database_type || '')">
                            {{ getDatabaseTypeName(selectedDataSource.database_type || '') }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="主机地址">
                        {{ selectedDataSource.host || '-' }}:{{ selectedDataSource.port || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="数据库名">
                        {{ selectedDataSource.database_name || '-' }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-card>

        <!-- SQL编辑器 -->
        <el-card class="sql-editor" shadow="never" v-if="selectedDataSource">
            <div class="editor-header">
                <h4>SQL查询</h4>
                <div class="editor-actions">
                    <el-button type="primary" @click="submitForm(ruleFormRef)" :loading="executing" 
                        :disabled="!ruleForm.sql.trim()">
                        <el-icon><VideoPlay /></el-icon>
                        执行查询
                    </el-button>
                    <el-button @click="clearSql">清空</el-button>
                </div>
            </div>
            <el-input v-model="ruleForm.sql" type="textarea" :rows="6"
                placeholder="请输入SQL查询语句，例如：SELECT * FROM table_name LIMIT 10" class="sql-textarea" />
        </el-card>
        
        <!-- 查询结果 -->
        <el-card class="query-result" shadow="never" v-if="showResult">
            <div class="result-header">
                <h4>查询结果</h4>
                <div class="result-info">
                    <el-tag v-if="resultData.length > 0" type="success">
                        共 {{ resultData.length }} 条记录
                    </el-tag>
                    <el-tag v-else type="info">无数据</el-tag>
                    <span class="execution-time" v-if="executionTime">
                        执行时间: {{ executionTime }}ms
                    </span>
                </div>
            </div>
            
            <!-- 结果表格 -->
            <el-table v-if="resultData.length > 0" :data="resultData" 
                stripe border height="300" size="small">
                <el-table-column v-for="column in resultColumns" :key="column"
                    :prop="column" :label="column" min-width="120" show-overflow-tooltip />
            </el-table>
            
            <!-- 无数据提示 -->
            <div v-else class="no-data">
                <el-empty description="查询结果为空" :image-size="100" />
            </div>
            
            <!-- 设置为变量功能 -->
            <div class="set-variable-section" v-if="resultData.length > 0">
                <el-divider content-position="left">设置为变量</el-divider>
                <el-form :model="variableForm" :rules="variableRules" ref="variableFormRef" label-width="100px" class="variable-form" inline>
                    <div class="variable-row">
                        <el-form-item label="变量名" prop="key">
                            <el-input v-model="variableForm.key" placeholder="请输入变量名" style="width: 110px;" />
                        </el-form-item>
                        <el-form-item label="值类型" prop="variable_type">
                            <el-select v-model="variableForm.variable_type" placeholder="请选择值类型" style="width: 100px;" :disabled="variableForm.is_dynamic">
                                <el-option v-for="type in variable_types" :label="type.name" :value="type.id" :key="type.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="是否动态获取" prop="is_dynamic">
                            <el-switch v-model="variableForm.is_dynamic" @change="handleDynamicChange" />
                        </el-form-item>
                    </div>
                    <div class="variable-row">
                        <el-form-item label="动态SQL" prop="dynamic_sql" v-if="variableForm.is_dynamic">
                            <el-input v-model="variableForm.dynamic_sql" type="textarea" :rows="3" style="width: 420px;" placeholder="请输入动态SQL语句" />
                        </el-form-item>
                    </div>
                    <div class="variable-row">
                        <el-form-item label="描述" prop="desc">
                            <el-input v-model="variableForm.desc" placeholder="请输入描述" style="width: 110px;" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="success" @click="saveAsVariable" :loading="savingVariable" style="margin-left: 42px;">
                                <el-icon><Plus /></el-icon>
                                保存为项目变量
                            </el-button>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
        </el-card>
        
        <!-- 错误信息 -->
        <el-card class="error-info" shadow="never" v-if="errorMessage">
            <div class="error-header">
                <h4>执行错误</h4>
            </div>
            <el-alert :title="errorMessage" type="error" show-icon :closable="false" />
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { VideoPlay, Plus } from '@element-plus/icons-vue'
import { getAllDataSource } from '@/apis/project/data_source/get_all_data_source'
import { execSql } from '@/apis/project/data_source/exec_sql'
import { postPublicData } from '@/apis/project/public_data/post_public_data'
import { useStore } from '@/stores'

const mainStore = useStore()
const userName: string = (mainStore.user_info as any).username

interface RuleForm {
    id: number | null
    name: string
    sql: string
}

interface VariableForm {
    key: string
    type: string
    value: string
    desc: string
    creator: string
    project: number
    variable_type?: string | null
    is_dynamic?: boolean | null
    dynamic_sql?: string | null
}

const props = defineProps({
    projectId: {
        type: Number,
        default: 0
    }
})

const ruleFormRef = ref<FormInstance>()
const variableFormRef = ref<FormInstance>()
const options = ref<any[]>([])
const executing = ref(false)
const savingVariable = ref(false)
const showResult = ref(false)
const resultData = ref<any[]>([])
const resultColumns = ref<string[]>([])
const errorMessage = ref('')
const executionTime = ref(0)

// 根据库里写死的类型
const allTypes = [
    { id: "1", name: "变量" }, 
    { id: "2", name: "BASIC_AUTH" }, 
    { id: "3", name: "BEARER_TOKEN" }, 
    { id: "4", name: "BASE_URL" }, 
    { id: "5", name: "UI_TEST" }
]
const variable_types = [
    { id: "1", name: "string" }, 
    { id: "2", name: "int" }, 
    { id: "3", name: "array" }, 
    { id: "4", name: "boolean" }, 
    { id: "5", name: "object" }, 
    { id: "6", name: "number" },
    { id: "7", name: "sql" }
]

const ruleForm = reactive<RuleForm>({
    id: null,
    name: '',
    sql: '',
})

const variableForm = reactive<VariableForm>({
    key: '',
    type: '1', // 固定为变量类型
    value: '',
    desc: '',
    creator: userName,
    project: props.projectId,
    variable_type: null,
    is_dynamic: false,
    dynamic_sql: ''
})

const rules = reactive<FormRules<RuleForm>>({
    id: [
        {
            required: true,
            message: '请选择数据源',
            trigger: 'change',
        }],
    sql: [
        {
            required: true,
            message: '请输入SQL查询语句',
            trigger: 'blur',
        }],
})

const variableRules = reactive<FormRules<VariableForm>>({
    key: [
        { required: true, message: '请输入变量名', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
    variable_type: [
        { required: true, message: '请选择变量类型', trigger: 'blur' },
    ]
})

// 计算选中的数据源信息
const selectedDataSource = computed(() => {
    if (!ruleForm.id) return null
    return options.value.find(item => item.id === ruleForm.id)
})

// 数据源选择变化处理
const handleDataSourceChange = (value: number) => {
    const dataSource = options.value.find(item => item.id === value)
    if (dataSource) {
        ruleForm.name = dataSource.name
    }
    // 重置结果
    resetResult()
}

// 重置结果数据
const resetResult = () => {
    resultData.value = []
    resultColumns.value = []
    errorMessage.value = ''
    showResult.value = false
    executionTime.value = 0
    resetVariableForm()
}

// 重置变量表单
const resetVariableForm = () => {
    variableForm.key = ''
    variableForm.type = '1'
    variableForm.value = ''
    variableForm.desc = ''
    variableForm.variable_type = null
    variableForm.is_dynamic = false
    variableForm.dynamic_sql = ''
    variableForm.creator = userName
    variableForm.project = props.projectId
}

// 执行SQL查询
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            await executeSqlQuery()
        } else {
            // console.log('error submit!', fields)
        }
    })
}

// 执行SQL查询的具体实现
const executeSqlQuery = async () => {
    if (!ruleForm.sql.trim()) {
        ElMessage.warning('请输入SQL查询语句')
        return
    }
    
    if (!ruleForm.id) {
        ElMessage.error('请选择数据源')
        return
    }
    
    executing.value = true
    errorMessage.value = ''
    showResult.value = false
    
    const startTime = Date.now()
    
    try {
        const res = await execSql(ruleForm.id, { sql: ruleForm.sql.trim() })
        
        executionTime.value = Date.now() - startTime
        
        if (res && res.data.code === 2000) {
            const data = res.data.data
            
            // 根据API文档，查询结果在data.result中
            const queryResult = data?.result || data
            
            if (queryResult && Array.isArray(queryResult) && queryResult.length > 0) {
                resultData.value = queryResult
                resultColumns.value = Object.keys(queryResult[0])
                showResult.value = true
                ElMessage.success('查询执行成功')
                
                // 自动推断变量类型
                autoDetectVariableType(queryResult)
            } else {
                resultData.value = []
                resultColumns.value = []
                showResult.value = true
                ElMessage.info('查询执行成功，但无返回数据')
            }
        } else {
            errorMessage.value = res?.data?.msg || 'SQL执行失败'
            showResult.value = false
        }
    } catch (error: any) {
        errorMessage.value = error?.response?.data?.msg || error?.message || 'SQL执行失败'
        showResult.value = false
    } finally {
        executing.value = false
    }
}

// 自动推断变量类型
const autoDetectVariableType = (data: any[]) => {
    if (data.length === 0) return
    
    // 如果开启动态获取，不自动设置值类型（固定为string）
    if (variableForm.is_dynamic) {
        return
    }
    
    const firstRow = data[0]
    const fieldNames = Object.keys(firstRow)
    
    // 如果只有一个字段，根据该字段的值类型来判断
    if (fieldNames.length === 1) {
        const fieldName = fieldNames[0]
        const fieldValue = firstRow[fieldName]
        
        // 根据数据类型推断变量类型
        if (Array.isArray(fieldValue)) {
            variableForm.variable_type = '3' // array
        } else if (typeof fieldValue === 'boolean') {
            variableForm.variable_type = '4' // boolean
        } else if (typeof fieldValue === 'number') {
            variableForm.variable_type = '6' // number
        } else if (typeof fieldValue === 'object' && fieldValue !== null) {
            variableForm.variable_type = '5' // object
        } else {
            variableForm.variable_type = '1' // string
        }
    } else {
        // 多个字段时，整个结果作为数组
        variableForm.variable_type = '3' // array
    }
}

// 处理动态获取开关变化
const handleDynamicChange = (value: boolean) => {
    if (value) {
        // 开启动态获取时，设置值类型为sql，并填充当前SQL
        variableForm.variable_type = '7'
        variableForm.dynamic_sql = ruleForm.sql
    } else {
        // 关闭动态获取时，重置值类型和动态SQL
        variableForm.variable_type = null
        variableForm.dynamic_sql = ''
        // 重新推断值类型
        if (resultData.value.length > 0) {
            autoDetectVariableType(resultData.value)
        }
    }
}

// 保存为变量
const saveAsVariable = async () => {
    if (!variableFormRef.value) return
    
    await variableFormRef.value.validate(async (valid) => {
        if (valid) {
            savingVariable.value = true
            
            try {
                let variableValue: any
                
                if (variableForm.is_dynamic) {
                    // 动态获取：保存动态SQL语句和数据源id
                    variableValue = {
                        sql: variableForm.dynamic_sql,
                        data_source_id: ruleForm.id
                    }
                    // 动态获取时，值类型固定为sql
                    variableForm.variable_type = '7'
                } else {
                    // 静态获取：根据字段数量决定保存的值
                    const firstRow = resultData.value[0]
                    const fieldNames = Object.keys(firstRow)
                    
                    if (fieldNames.length === 1) {
                        // 只有一个字段时，保存该字段的值
                        const fieldName = fieldNames[0]
                        variableValue = firstRow[fieldName]
                    } else {
                        // 多个字段时，保存整个数组
                        variableValue = resultData.value
                    }
                }
                
                // 直接保存原始值，不做任何转换
                variableForm.value = variableValue
                variableForm.creator = userName
                variableForm.project = props.projectId
                
                const response = await postPublicData(variableForm)
                if (response && response.data.code === 2000) {
                    ElMessage.success('变量保存成功')
                    resetVariableForm()
                } else {
                    ElMessage.error(response?.data?.msg || '变量保存失败')
                }
            } catch (error: any) {
                ElMessage.error(error?.message || '变量保存失败')
            } finally {
                savingVariable.value = false
            }
        }
    })
}

// 清空SQL
const clearSql = () => {
    ruleForm.sql = ''
    resetResult()
}

// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    resetResult()
}

// 获取数据源列表
const getDataSource = async () => {
    try {
        const res = await getAllDataSource(props.projectId, '', '', true, 1, 50)
        if (res.data.results.code === 2000) {
            options.value = res.data.results.data
        } else {
            ElMessage.error(res.message || '获取数据源列表失败')
        }
    } catch (error: any) {
        ElMessage.error(error?.message || '获取数据源列表失败')
    }
}

// 获取数据库类型显示名称
const getDatabaseTypeName = (type: string) => {
    const typeMap: { [key: string]: string } = {
        'mysql': 'MySQL',
        'postgresql': 'PostgreSQL',
        'oracle': 'Oracle',
        'sqlserver': 'SQL Server',
        'mongodb': 'MongoDB'
    }
    return typeMap[type] || type
}

// 获取数据库类型颜色
const getDatabaseTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
        'mysql': 'primary',
        'postgresql': 'success',
        'oracle': 'warning',
        'sqlserver': 'info',
        'mongodb': 'danger'
    }
    return colorMap[type] || 'default'
}



onMounted(() => {
    getDataSource()
})
</script>

<style lang="less" scoped>
.exec-sql-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.sql-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    
    :deep(.el-textarea__inner) {
        font-family: inherit;
    }
}

.datasource-info {
    .info-header {
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
    }
}

.sql-editor {
    .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
        
        .editor-actions {
            display: flex;
            gap: 12px;
        }
    }
}

.query-result {
    .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
        
        .result-info {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .execution-time {
                color: #909399;
                font-size: 12px;
            }
        }
    }
    
    .set-variable-section {
        margin: 16px 0;
        .variable-form {
            display: flex;
            flex-direction: column;
            gap: 0;
            .variable-row {
                display: flex;
                align-items: flex-start;
                gap: 24px;
                margin-bottom: 0;
            }
            .el-form-item {
                margin-bottom: 16px;
            }
        }
    }
    
    .no-data {
        text-align: center;
        padding: 40px 0;
    }
}

.error-info {
    .error-header {
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #F56C6C;
            font-size: 16px;
        }
    }
}

.feature-intro {
    margin-bottom: 16px;
}
</style>