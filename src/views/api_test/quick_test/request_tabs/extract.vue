<template>
  <el-form :model="form" label-width="100px">
    <el-form-item>
      <span style="font-weight: bold;margin-right: 10px;">提取方式</span>
      <el-select style="width: 200px;" v-model="form.type" placeholder="请选择提取方式">
        <el-option label="JSONPath提取" value="json_path" />
      </el-select>
    </el-form-item>
    <extract_json :ExtractData="JsonExtractData" v-if="form.type === 'json_path'" @updateExtractData="updateExtractData"></extract_json>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import extract_json from './extract/extract_json.vue';

const props = defineProps ({
  ExtractData: Array
})

const emit = defineEmits(['updatePostData'])

const form = ref({
  // 目前只有这一种，先写死
  type: 'json_path'
})

const updateExtractData = (data: any) => {
  emit('updatePostData', data, 'jsonpath')
}


const JsonExtractData = ref<unknown[]>([])

watch(()=>props.ExtractData, ()=>{
  if(props.ExtractData){
    JsonExtractData.value = props.ExtractData
  }
},{deep:true, immediate:true})

</script>