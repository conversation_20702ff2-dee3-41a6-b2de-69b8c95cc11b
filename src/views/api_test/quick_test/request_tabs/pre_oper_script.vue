<template>
  <div class="main">
    <code-mirror 
      v-model="code_val" 
      wrap 
      basic 
      :lang="lang" 
      style="height: 400px;" 
      :extensions="extensions" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRaw } from 'vue';
import CodeMirror from 'vue-codemirror6'
import { oneDark } from '@codemirror/theme-one-dark'
import { python } from '@codemirror/lang-python';
import { indentUnit } from '@codemirror/language'

const emit = defineEmits(['updatePostData'])

const props = defineProps({
  PreScriptData: String
})

// Python函数模板
const python_template = `def ats_script(context):
    """
    前置脚本：请求发送前执行，用于构造参数、生成变量等

    参数：
    - context: 当前变量池，包括项目全局变量、环境变量、前置接口的返回变量等

    内置包与方法（直接使用无需import、其它包不支持，如需扩展请联系管理员）：
    - time、random、re、requests、json、datetime、math
    - print、len、str、int、float、bool、dict、list、tuple、type、isinstance、range、enumerate

    返回：
    - dict：
        {
            "project": { ... },  # 当前项目作用域变量，可跨接口使用
            "env": { ... }       # 当前环境作用域变量，用于登录态、请求头等
        }
    """

    # 获取已有变量（建议提供默认值）
    pre_token = context.get("token", "default-token")
    pre_userid = context.get("userid", 0)
    pre_user_info = context.get("user_info", {})
    pre_user_members = context.get("user_members", [])

    # 生成新的变量
    pre_trace_id = f"{pre_userid}-{random.randint(1000, 9999)}"
    pre_timestamp = int(time.time())

    # 生成变量到项目全局变量或环境变量，后续接口中可以用{{变量名}}引用
    return {
        "env": {
            "pre_token": pre_token,
            "pre_trace_id": pre_trace_id,
            "pre_timestamp": pre_timestamp
        },
        "project": {
            "pre_userid": pre_userid,
            "pre_user_info": pre_user_info,
            "pre_user_members": pre_user_members
        }
    }`;

// 初始化代码值
const code_val = ref(python_template);

const lang = python();
const extensions = [oneDark, indentUnit.of("    ")];

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
  emit('updatePostData', toRaw(code_val.value))
}

// 监听代码变化，发送给父组件
watch(code_val, () => {
  updateData()
}, { deep: true })

// 监听从父组件传入的数据
watch(() => props.PreScriptData, (newValue) => {
  if (newValue && newValue.trim() !== '') {
    code_val.value = newValue
  } else {
    code_val.value = python_template
  }
}, { immediate: true })
</script>

<style>
/* required! */
.cm-editor {
  height: 100%;
}
</style>
  