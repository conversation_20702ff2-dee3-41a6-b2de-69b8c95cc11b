<template>
  <div class="main">
    <code-mirror v-model="codeVal" wrap basic :lang="lang" style="height: 400px; font-size: 13px;" :extensions="extensions" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRaw } from 'vue';
import CodeMirror from 'vue-codemirror6'
import { oneDark } from '@codemirror/theme-one-dark'
import { json, jsonParseLinter } from '@codemirror/lang-json';
import { linter } from '@codemirror/lint'; // 引入linter
import { EditorState } from '@codemirror/state'

const emit = defineEmits(['updateJsonData'])

const props = defineProps({
  BodyData: Array
})

// const initJson = {}
// 初始化
let codeVal = ref('');
// // 转成json字符串并格式化
// codeVal.value = JSON.stringify(initJson, null, '\t')

const lang = json();
const jsonLinter = linter(jsonParseLinter()); // 创建一个linter配置
const extensions = [oneDark, jsonLinter, EditorState.tabSize.of(2)]; // 添加到extensions数组中


// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
  emit('updateJsonData', toRaw(codeVal.value))
}

watch(codeVal, () => {
  updateData()
}, { deep: true })

watch(() => props.BodyData, () => {
  if (props.BodyData && props.BodyData.length > 0) {
    // 这里固定只返回一条
    codeVal.value = JSON.stringify((props.BodyData[0] as any).json_value, null, '\t')
  }
}, { deep: true, immediate: true })
</script>

<style>
/* required! */
.cm-editor {
  height: 100%;
}
</style>
