<template>
    <div class="scale-down">
        <div class="add-row-container" v-if="currentPageData.length === 0">
            <el-button @click="addRow(0)" type="primary" plain class="add-row-button">
                <el-icon><Plus /></el-icon>
                <span>添加一行</span>
            </el-button>
        </div>
        <el-table size="small" v-if="currentPageData.length != 0" :data="tableData" border style="width: 100%">
            <!-- 为每个属性定义一个带有表单输入的列 -->
            <el-table-column label="Key">
                <template #default="scope">
                    <el-form :model="scope.row" :rules="rules" ref="ruleFormRef">
                        <el-form-item prop="key">
                            <el-input v-model="scope.row.key"
                                :input-style="{ color: scope.row.key.includes('{{') && scope.row.key.includes('}}') ? '#F26B4C' : 'inherit' }" />
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column label="Type">
                <template #default="scope">
                    <el-form :model="scope.row" :rules="rules" ref="ruleFormRef">
                        <el-form-item prop="key_type">
                            <el-select v-model="scope.row.key_type" placeholder="请选择"
                                @change="handleTypeChange(scope.row)">
                                <el-option v-for="type in allTypes" :label="type.name" :value="type.id"
                                    :key="type.id" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column label="Value">
                <template #default="scope">
                    <el-form :model="scope.row">
                        <el-form-item prop="value">
                            <el-input v-if="!scope.row.showFile" v-model="scope.row.value"
                                :input-style="{ color: scope.row.value.includes('{{') && scope.row.value.includes('}}') ? '#F26B4C' : 'inherit' }" />
                        </el-form-item>
                        <el-form-item prop="file">
                            <el-upload style="max-width: 200px;" v-if="scope.row.showFile"
                                v-model:file-list="scope.row.file"
                                :action="uploadFileServerUrl + '/api/api_test/multipart_file_upload/'" multiple
                                :headers="{ 'Authorization': 'Bearer ' + RequestToken }"
                                :data="{ 'project_id': projectId, 'key': scope.row.key }" :limit="1" :on-exceed="onExceed"
                                :before-upload="beforeUpload" :on-success="onSuccess" :on-remove="onRemove(scope.row)">
                                <el-button size="small">上传</el-button>
                            </el-upload>
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column label="Description">
                <template #default="scope">
                    <el-form :model="scope.row">
                        <el-form-item prop="desc">
                            <el-input v-model="scope.row.desc" />
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <!-- 操作列，用于添加或删除行 -->
            <el-table-column label="操作" width="120">
                <template #default="scope">
                    <div class="action-buttons">
                        <el-button type="primary" size="small" circle :icon="Plus" @click="addRow(scope.$index)"
                            :disabled="scope.$index !== currentPageData.length - 1 || currentPage * pageSize < total"></el-button>
                        <el-button type="danger" size="small" circle :icon="Minus" @click="removeRow(scope.$index)"></el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination-container">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" @current-change="changePage"
                layout="prev, pager, next, total" :total="total" background />
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, reactive, toRaw, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus, Minus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus'
import type { UploadProps } from 'element-plus'
import Cookies from 'js-cookie'
import { useStore } from '@/stores';

const dicStore = useStore()
const dicKey = 'upload_file_server_url'
const uploadFileServerUrl = ref('http://127.0.0.1:8000')

watch(() => dicStore.dic_list, () => {
    const found = dicStore.dic_list.find((o: any) => o.dic_key === dicKey);
    if (found) {
        uploadFileServerUrl.value = found.dic_value
        // console.log('uploadFileServerUrl', uploadFileServerUrl.value)
    }
},{deep:true,immediate:true})


interface ProjectInfo {
    id: number
    // add other properties as needed
}

const projectStore = useStore()
const projectId = ref((projectStore.project_info as ProjectInfo).id)
const RequestToken = Cookies.get('token')

const handleTypeChange = (row: any) => {
    // console.log(row.key_type)
    if (row.key_type === '5') {
        row.showFile = true; // 使用行内属性来控制显示
    } else {
        row.showFile = false;
    }
    // console.log(row.showFile)
}

const onExceed = (files: any, fileList: any) => {
    if (fileList.length + files.length > 1) {
        ElMessage.error('最多上传1个文件');
        return false;
    }
    return true;
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.size / 1024 / 1024 > 5) {
        ElMessage.error('上传的文件请控制在5mb以内！')
        return false
    } else {
        return true
    }
}

const onSuccess = (res: any) => {
    // console.log(res.data)
    if (res.code === 2000) {
        ElMessage.success('上传成功')
    } else {
        ElMessage.error('上传失败')
    }
}

const onRemove = (row: any) => {
    row.value = ''
}

const currentPageData: any = ref([])
const currentPage = ref(1)
const pageSize = 6 // 每页显示的数据条数
const total = ref(0)
// modelValue是v-modle的默认属性名
const props = defineProps({
    FormData: Array
})

const emit = defineEmits(['updateFormData']) // 定义emit函数


interface RuleForm {
    id: number
    key: string
    key_type: string
    body_type: string
    value: string
    desc: string
    file: Array<any>
    showFile: boolean
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
    id: -1,
    key: '',
    key_type: '1',
    body_type: '2',
    value: '',
    desc: '',
    file: [],
    showFile: false
})


const rules = reactive<FormRules<RuleForm>>({
    key: [
        { required: true, message: '请输入变量名', trigger: 'blur' },
        { validator: (rule, value) => value.trim() !== '', message: '变量名不能只包含空格', trigger: 'blur' },
    ],
    key_type: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
    value: [
        { required: true, message: '请输入变量值', trigger: 'blur' },
        { validator: (rule, value) => value.trim() !== '', message: '变量值不能只包含空格', trigger: 'blur' },
    ]
})

// const allTypes = [{ id: "1", name: "变量" }, { id: "2", name: "STRING" }, { id: "3", name: "INT" }, { id: "4", name: "ARRAY" }, { id: "5", name: "FILE" }]
const allTypes = [{ id: "1", name: "变量" }, { id: "5", name: "FILE" }]

const ruleForms = ref<RuleForm[]>([])
const tableData = ref([])

const changePage = (newPage: number) => {
    currentPage.value = newPage
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    currentPageData.value = ruleForms.value.slice(start, end)
    tableData.value = currentPageData.value

}

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
    // 创建一个新数组，其中不包含 showFile 字段，但保留 file 和 key_type 字段，并根据 key_type 调整 value 字段
    const dataToSend = ruleForms.value.map(({ showFile, ...rest }) => {
        if (rest.key_type === '5' && rest.file[0]?.name) {
            rest.value = '#file#';
        }
        return rest;
    });
    // console.log(dataToSend)
    emit('updateFormData', toRaw(dataToSend));
}

watch(ruleForms, () => {
    updateData()
}, { deep: true })

watch(() => props.FormData, () => {
    if (props.FormData && (props.FormData[0] as any)?.body_type === '2') {
        ruleForms.value = props.FormData.map((data: any) => ({
            id: data.id || '',
            key: data.key || '',
            key_type: data.key_type || '1',
            body_type: data.body_type || '2',
            value: data.value || '',
            desc: data.desc || '',
            file: data.file || [],
            showFile: data.key_type === '5'
        }))
        total.value = props.FormData.length
        changePage(1)
    }
}, { deep: true, immediate: true })


const addRow = (index: number) => {
    // 当点击添加一行按钮，或者点击+号
    if ((index === 0) || (index === currentPageData.value.length - 1 && currentPage.value * pageSize >= total.value)) {
        const newRow: RuleForm = {
            id: -1,
            key: '',
            key_type: '1',
            body_type: '2',
            value: '',
            desc: '',
            file: [],
            showFile: false
        }
        ruleForms.value.push(newRow) // 先更新ruleForms
        total.value += 1
        if (total.value > currentPage.value * pageSize) {
            currentPage.value += 1
        }
        changePage(currentPage.value) // 然后更新currentPageData
    }
}

const removeRow = (index: number) => {
    const globalIndex = (currentPage.value - 1) * pageSize + index
    ruleForms.value.splice(globalIndex, 1) // 先更新ruleForms
    total.value -= 1
    if (currentPageData.value.length === 0 && currentPage.value > 1) {
        currentPage.value -= 1
    }
    changePage(currentPage.value) // 然后更新currentPageData
}

</script>

<style scoped>
/* 调整表格单元格的内边距 */
.el-table .el-table__body-wrapper .el-table__row .el-table__cell {
    padding-bottom: 0;
    /* 减少底部内边距 */
}

/* 调整表单元素的外边距 */
.el-table .el-form-item {
    margin-bottom: 0;
    /* 移除底部外边距 */
}

/* 添加一行按钮样式 */
.add-row-container {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    background-color: #f9fafc;
    border-radius: 6px;
    border: 1px dashed #dcdfe6;
    margin-bottom: 16px;
}

.add-row-button {
    width: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    padding: 10px 20px;
    transition: all 0.3s;
}

.add-row-button:hover {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #c6e2ff;
}

/* 表格内操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 分页容器样式 */
.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}
</style>