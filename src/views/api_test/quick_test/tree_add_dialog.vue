<template>
  <el-dialog v-model="dialogVisible" title="新建目录" width="25%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="85px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="目录名称" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postQuickTree } from '@/apis/api_test/quick_test/post_quick_tree'
import { getQuickTree } from '@/apis/api_test/quick_test/get_quick_tree'
import { useStore } from '@/stores';

type mainStore = {
    project_info: {
        id: number
        name: string
    }
}

const mainStore = useStore() as mainStore



// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  folderId: Number
})

const dialogVisible = ref(false)
const parent_id = ref(props.folderId || 0)
const project_id = ref()

watchEffect(() => {
  dialogVisible.value = props.modelValue
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.name = ''
  }
})

watchEffect(() => {
  project_id.value = mainStore.project_info.id
})

watchEffect(() => {
  parent_id.value = props.folderId || 0
})

const emit = defineEmits(['update:modelValue', 'updateTreeData']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  name: string
  parent: number
  type: string
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  name: '',
  parent: parent_id.value || 0,
  type: '1'
})

watchEffect(() => {
  ruleForm.name = ''
  ruleForm.parent = parent_id.value || 0
  ruleForm.type = '1'
})

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入目录名', trigger: 'blur' },
  ]
})


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await postQuickTree(ruleForm)
      if (response && response.data.code === 2000) {
        const TreeDataResponse = await getQuickTree(project_id.value)
        if (TreeDataResponse && TreeDataResponse.data.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateTreeData', TreeDataResponse.data.data)  // 触发 update 事件
        }
      } else {
        ElMessage.error(response.data.msg)
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}

</script>
<style scoped></style>