<template>
  <el-tabs>
    <el-tab-pane label="概览">
      <el-descriptions title="" :column="1" :border="true">
        <el-descriptions-item label="名称">{{ folderName }}</el-descriptions-item>
        <el-descriptions-item label="描述">
        </el-descriptions-item>
      </el-descriptions>
    </el-tab-pane>
  </el-tabs>
</template>


<script setup lang='ts'>
import { ref, reactive, watchEffect } from 'vue'

const form = reactive({
  desc: ''
})

const props = defineProps({
  folderData: Object
})

const folderName = ref('')

watchEffect(() => {
  folderName.value = props.folderData?.label || ''
})

</script>

<style scoped></style>