<template>
    <el-table size="small" :data="tableData" border style="width: 100%">
        <el-table-column prop="name" label="Name" />
        <el-table-column prop="value" label="Value" />
        <el-table-column prop="domain" label="Domain" />
        <el-table-column prop="path" label="Path" />
        <el-table-column prop="expires" label="Expires" />
        <el-table-column prop="httpOnly" label="HttpOnly" />
        <el-table-column prop="secure" label="Secure" />
    </el-table>
</template>

<script lang="ts" setup>
const tableData = [
    {
        name: '',
        value: '',
        domain: '',
        path: '/',
        expires: 'Session',
        httpOnly: 'false',
        secure: 'false',
    }
]
</script>