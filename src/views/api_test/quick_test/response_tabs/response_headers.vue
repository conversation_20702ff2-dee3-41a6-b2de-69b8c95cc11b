<template>
  <el-table size="small" :data="tableData" border style="width: 100%">
    <el-table-column prop="key" label="Key" />
    <el-table-column prop="value" label="Value" />
  </el-table>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
const props = defineProps({
  ResponseHeadersData: Object
})

const tableData:any = ref([])
const responseHeadersData:any = ref(props.ResponseHeadersData)

watchEffect(()=>{
  responseHeadersData.value = props.ResponseHeadersData || {} // 添加了 || {} 以确保值不是undefined
  tableData.value = Object.entries(responseHeadersData.value).map(([key, value]) => ({ key, value }))
})



</script>

