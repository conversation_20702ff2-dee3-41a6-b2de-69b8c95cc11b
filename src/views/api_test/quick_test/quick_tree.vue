<template>
  <div class="tree-container">
    <div class="search-container">
      <el-input
        v-model="filterText"
        placeholder="输入名称查询..."
        class="search-input"
        clearable
      >
        <template #prefix>
          <el-icon class="search-icon"><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <el-tree 
      ref="treeRef" 
      draggable
      class="filter-tree" 
      :data="data" 
      :props="defaultProps" 
      default-expand-all
      :filter-node-method="filterNode" 
      :highlight-current="true" 
      :expand-on-click-node="false" 
      @node-click="showApi"
      v-loading="tree_loading"
    >
      <template #default="{ node, data }">
        <div class="tree-node">
          <span class="tree-icon">
            <el-icon v-if="data.type === '1'" class="folder-icon"><Folder /></el-icon>
            <el-icon v-else-if="data.if_stream" class="stream-icon"><img :src="STREAM" alt="Stream"/></el-icon>
            <img v-else :src="API" class="api-icon" alt="API"/>
          </span>
          <span class="node-label">{{ node.label }}</span>
          <div class="dropdown-container">
            <el-dropdown trigger="click">
              <span class="dropdown-trigger">
                <el-icon>
                  <More />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="data.type === '1'" @click="dialogFolderAdd(data.id)">
                    <el-icon class="dropdown-icon"><FolderAdd /></el-icon> 新建目录
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '1'" @click="dialogApiAdd(data.id)">
                    <el-icon class="dropdown-icon"><DocumentAdd /></el-icon> 新建接口
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="data.label === '顶级目录'" @click="dialogEdit(data.id, data.label, data.if_stream)">
                    <el-icon class="dropdown-icon"><Edit /></el-icon> 编辑
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="data.label === '顶级目录'" @click="dialogDel(data.id)">
                    <el-icon class="dropdown-icon"><Delete /></el-icon> 删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
  <tree_add_dialog v-model="dialogVisible" :folderId="folderId" @updateTreeData="updateTreeData"></tree_add_dialog>
  <tree_add_api v-model="dialogApiVisible" :folderId="folderId" @updateTreeData="updateTreeData"></tree_add_api>
  <tree_edit_dialog v-model="dialogVisibleEdit" :ifStream="ifStream" :folderId="folderId" :folderName="folderName" @updateTreeData="updateTreeData" @nodeNameUpdated="handleNodeNameUpdated"></tree_edit_dialog>
  <tree_del_dialog v-model="dialogVisibleDel" :folderId="folderId" @updateTreeData="updateTreeData"> </tree_del_dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElTree } from 'element-plus'
import { getQuickTree } from '@/apis/api_test/quick_test/get_quick_tree'
import { Folder, Edit, Delete, Search, DocumentAdd, FolderAdd } from '@element-plus/icons-vue'
import tree_add_dialog from './tree_add_dialog.vue'
import tree_edit_dialog from './tree_edit_dialog.vue'
import tree_del_dialog from './tree_del_dialog.vue'
import tree_add_api from './tree_add_api.vue'
import { getQuickApiSubData } from '@/apis/api_test/quick_test/get_quick_api_sub_data'
import { getResponse } from '@/apis/api_test/quick_test/get_response'
import { getTestResult } from '@/apis/api_test/quick_test/get_test_result'
import API from '@/assets/icon/API.png'
import STREAM from '@/assets/icon/STREAM.png'


const dialogVisible = ref(false)
const folderId = ref(0)
const folderName = ref('')
const ifStream = ref(false) // 确保这是一个响应式引用
const dialogApiVisible = ref(false)
const dialogVisibleEdit = ref(false)
const dialogVisibleDel = ref(false)

const tree_loading = ref(false)

const emit = defineEmits(['ifShowApi', 'getFolderOrApi', 'nodeNameUpdated', 'getApiSubData', 'getResponseData', 'getTestResultData', 'updateLoadingState']) // 定义emit函数
const showApiPage = ref('')
//节点id
const showApiId = ref(0)

// 添加请求状态管理
const currentRequestId = ref(0)
const isLoading = ref(false)

const apiSubData = ref({})
const getApiSubData = async (requestId: number, apiName: string) => {
  try {
    const res = await getQuickApiSubData(apiName)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      apiSubData.value = res.data.data
      emit('getApiSubData', apiSubData.value)
    } else if (res && res.data.data === '接口不存在') {
      apiSubData.value = {"content_type": "none", "body": []}
      emit('getApiSubData', apiSubData.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取接口数据失败:', error)
  }
}

const responseData = ref({})
const getResponseData = async (requestId: number, apiId: number) => {
  try {
    const res = await getResponse(apiId)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      responseData.value = res.data.data
      emit('getResponseData', responseData.value)
    } else {
      responseData.value = {}
      emit('getResponseData', responseData.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取响应数据失败:', error)
  }
}

const testResultData = ref([])
const getTestResultData = async (requestId: number, apiId: number) => {
  try {
    const res = await getTestResult(apiId)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      testResultData.value = res.data.data
      emit('getTestResultData', testResultData.value)
    } else {
      testResultData.value = []
      emit('getTestResultData', testResultData.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取测试结果失败:', error)
  }
}

const api_name = ref('')
const showApi = async (data: any) => {
  // 生成新的请求ID，用于标识这次点击
  const requestId = Date.now()
  currentRequestId.value = requestId
  isLoading.value = true
  emit('updateLoadingState', true)

  try {
    if (data.type === '0') {
      showApiPage.value = '0'
      showApiId.value = data.id
      api_name.value = data.label
      
      // 先发送基础信息
      emit('ifShowApi', showApiPage.value, showApiId.value, data.if_stream)
      emit('getFolderOrApi', data)
      
      // 清空旧数据，避免显示上次的内容
      emit('getApiSubData', {})
      emit('getResponseData', {})
      emit('getTestResultData', [])
      
      // 并行发起所有请求，但使用requestId确保数据一致性
      await Promise.allSettled([
        getApiSubData(requestId, data.label),
        getResponseData(requestId, data.id),
        getTestResultData(requestId, data.id)
      ])
    } else {
      showApiPage.value = '1'
      showApiId.value = data.id
      emit('ifShowApi', showApiPage.value, showApiId.value)
      emit('getFolderOrApi', data)
    }
  } catch (error) {
    // console.error('显示API数据失败:', error)
  } finally {
    // 只有当前请求完成时才清除loading状态
    if (requestId === currentRequestId.value) {
      isLoading.value = false
      emit('updateLoadingState', false)
    }
  }
}

const updateTreeData = (newTree: any, type= '') => {
  if (newTree) {
    data.value = renameKeys([newTree])
    if (type === 'del') {
      // 如果节点删除，就设置默认页面
      emit('ifShowApi', '999')
    }
  }
}

const handleNodeNameUpdated = (newName: string) => {
  emit('nodeNameUpdated', newName); // 将新的节点名称向上传递
}

const dialogFolderAdd = (id:number) => {
  folderId.value = id
  dialogVisible.value = true
}

const dialogApiAdd = (id:number) => {
  folderId.value = id
  dialogApiVisible.value = true
}

const dialogEdit = (id:number, name:string, if_stream:boolean) => {
  folderId.value = id
  folderName.value = name
  ifStream.value = if_stream // 确保if_stream是一个响应式引用
  dialogVisibleEdit.value = true
}

const dialogDel = (id:number) => {
  folderId.value = id
  dialogVisibleDel.value = true
}

interface Tree {
  [key: string]: any
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

const props = defineProps({
  projectId: Number
})

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}

interface TreeNode {
  name?: string;
  children?: TreeNode[] | null;
  [key: string]: any;
}

function renameKeys(data: TreeNode[]): TreeNode[] {
  return data.map(({ name: label, children, ...rest }) => ({
    label,
    ...rest,
    children: children ? renameKeys(children) : null,
  }));
}

// 找第一个
const findFirstApiNode = (nodes: any) => {
  for (const node of nodes) {
    if (node.type === '0') {
      return node;
    }
    if (node.children) {
      const found: any = findFirstApiNode(node.children);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

const data = ref<TreeNode[]>([])

const getTree = async () => {
  if (props.projectId) {
    tree_loading.value = true
    const res = await getQuickTree(props.projectId)
    if (res) {
      let treeData = res.data.data
      data.value = renameKeys([treeData])

      const firstApiNode = findFirstApiNode(data.value);
      if (firstApiNode) {
        showApi(firstApiNode);
      }
    }
    tree_loading.value = false
  }
}

getTree()

</script>

<style scoped>
.tree-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-container {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  height: 32px;
}

.search-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-icon {
  color: #909399;
  font-size: 16px;
  margin-right: 4px;
}

.filter-tree {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  scrollbar-width: thin;
  scrollbar-color: #C1C1C1 #f0f0f0;
}

.filter-tree::-webkit-scrollbar {
  width: 6px;
}

.filter-tree::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.filter-tree::-webkit-scrollbar-thumb {
  background-color: #C1C1C1;
  border-radius: 4px;
}

.filter-tree::-webkit-scrollbar-thumb:hover {
  background: #909090;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px 8px;
  position: relative;
  border-radius: 6px;
  transition: all 0.3s;
  box-sizing: border-box;
  height: 38px;
}

.tree-node:hover {
  background-color: #f0f7ff;
}

.tree-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.folder-icon {
  color: #e6a23c;
  font-size: 18px;
}

.api-icon, .stream-icon {
  width: 18px;
  height: 18px;
}

.node-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.dropdown-container {
  margin-left: auto;
  flex-shrink: 0;
  padding: 4px;
  position: absolute;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.dropdown-trigger {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  transition: all 0.2s;
}

.dropdown-trigger:hover {
  background-color: #e7f1fd;
  color: #409eff;
}

.tree-node:hover .dropdown-container {
  opacity: 1;
}

.dropdown-icon {
  margin-right: 5px;
  font-size: 16px;
}

:deep(.el-tree-node) {
  padding: 4px 0;
}

:deep(.el-tree-node__content) {
  height: 38px;
  padding: 0 !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  border-radius: 6px;
}

/* 增强树形结构的层级感 */
:deep(.el-tree-node__children) {
  padding-left: 16px;
}

:deep(.el-tree-node__children .el-tree-node__content) {
  position: relative;
}



/* 增强接口节点区分 */
.tree-node .tree-icon .api-icon,
.tree-node .tree-icon .stream-icon {
  opacity: 0.85;
  transition: opacity 0.3s;
}

.tree-node:hover .tree-icon .api-icon,
.tree-node:hover .tree-icon .stream-icon {
  opacity: 1;
}

/* 改进展开图标样式 */
:deep(.el-tree-node__expand-icon) {
  font-size: 14px;
  color: #909399;
  transition: all 0.2s;
  margin-right: 5px;
  border-radius: 2px;
  padding: 2px;
}

:deep(.el-tree-node__expand-icon:hover) {
  background-color: #f0f0f0;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree-node__content:hover) {
  background-color: transparent;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 8px 16px;
  transition: all 0.3s;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f0f7ff;
}

:deep(.el-dropdown-menu__item.is-disabled) {
  opacity: 0.6;
}

:deep(.el-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 6px 0;
}

:deep(.el-tree) {
  --el-tree-node-hover-bg-color: transparent;
}

/* 媒体查询适配小屏 */
@media (max-width: 768px) {
  .tree-container {
    padding: 12px;
  }
  
  .node-label {
    max-width: 70%;
    font-size: 13px;
  }
  
  .tree-icon {
    margin-right: 6px;
    width: 20px;
    height: 20px;
  }
  
  .folder-icon {
    font-size: 16px;
  }
  
  .api-icon, .stream-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
