<template>
  <div class="container" v-loading="props.dataLoading" element-loading-text="正在加载数据...">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="api-info">
        <div class="api-name">
          <span class="label">接口名：</span>
          <span class="value">{{ apiName }}</span>
        </div>
        <popover :environmentId="apiSubData.environment" :projectId="props.projectId"></popover>
      </div>
      <div class="environment-selector">
        <span class="label">环境：</span>
        <el-select size="small" v-model="apiSubData.environment" placeholder="选择环境" class="env-select">
          <el-option label="No Environment" :value=0></el-option>
          <el-option v-for="env in envDropList" :label="env.name" :value="env.id" :key="env.id"></el-option>
        </el-select>
      </div>
    </div>

    <!-- 请求区域 -->
    <div class="request-section">
      <div class="method-select">
        <el-select v-model="apiSubData.request_method" class="method-dropdown">
          <el-option class="method-option method-get" label="GET" value="GET" />
          <el-option class="method-option method-post" label="POST" value="POST" />
          <el-option class="method-option method-put" label="PUT" value="PUT" />
          <el-option class="method-option method-delete" label="DELETE" value="DELETE" />
          <el-option class="method-option method-patch" label="PATCH" value="PATCH" />
          <el-option class="method-option method-options" label="OPTIONS" value="OPTIONS" />
        </el-select>
      </div>
      <div class="url-input">
        <el-input v-model="apiSubData.request_url"
          :input-style="{ color: apiSubData.request_url.includes('{{') && apiSubData.request_url.includes('}}') ? '#F26B4C' : 'inherit' }"
          placeholder="请输入接口URL"></el-input>
      </div>
      <div class="action-buttons">
        <el-button-group>
          <el-button :loading="loading" type="primary" @click="onSend" class="send-button">发送</el-button>
          <el-button :loading="loading" type="primary" @click="onSubmit" class="save-button">保存</el-button>
          <el-dropdown @command="handleCommand">
            <el-button type="primary"><el-icon size="12">
                <ArrowDown />
              </el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="save_as" @click="open_save_as()">另存为</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-button-group>
      </div>
    </div>

    <!-- 主要标签页区域 -->
    <div class="tabs-section">
      <el-tabs v-model="activeName" class="request-tabs" @tab-click="handleClick">
        <el-tab-pane label="Params" name="first">
          <query_params :ParamsData="apiSubData.params" @updatePostData="updateParamsData">
          </query_params>
        </el-tab-pane>
        <el-tab-pane label="Authorization" name="second">
          <authorization :AuthorizationData="apiSubData.authorization" @updatePostData="updateAuthorizationData">
          </authorization>
        </el-tab-pane>
        <el-tab-pane label="Headers" name="third">
          <headers :HeadersData="apiSubData.headers" @updatePostData="updateHeadersData">
          </headers>
        </el-tab-pane>
        <el-tab-pane label="Body" name="fourth">
          <request_body :BodyData="apiSubData.body" @updatePostData="updateBodyData"></request_body>
        </el-tab-pane>
        <el-tab-pane name="fifth">
          <template #label>
            <span style="color: #f56c6c;">数据库操作 <span style="font-size: 8px; background: #f56c6c; color: white; padding: 1px 4px; border-radius: 2px;">NEW</span></span>
          </template>
          <exec_sql :projectId="props.projectId"></exec_sql>
        </el-tab-pane>
        <el-tab-pane name="sixth">
          <template #label>
            <span style="color: #f56c6c;">前置操作 <span style="font-size: 8px; background: #f56c6c; color: white; padding: 1px 4px; border-radius: 2px;">NEW</span></span>
          </template>
          <pre_oper_script :PreScriptData="apiSubData.pre_script" @updatePostData="updatePreScriptData">
          </pre_oper_script>
        </el-tab-pane>
        <el-tab-pane name="seventh">
          <template #label>
            <span style="color: #f56c6c;">后置操作 <span style="font-size: 8px; background: #f56c6c; color: white; padding: 1px 4px; border-radius: 2px;">NEW</span></span>
          </template>
          <after_oper_script :PostScriptData="apiSubData.post_script" @updatePostData="updatePostScriptData">
          </after_oper_script>
        </el-tab-pane>
        <el-tab-pane label="提取变量" name="eighth">
          <extract :ExtractData="apiSubData.postfix" @updatePostData="updateExtractData"></extract>
        </el-tab-pane>
        <el-tab-pane name="ninth">
          <template #label>
            <span style="color: #f56c6c;">断言 <span style="font-size: 8px; background: #f56c6c; color: white; padding: 1px 4px; border-radius: 2px;">NEW</span></span>
          </template>
          <assertion :AssertionData="apiSubData.tests" :projectId="props.projectId" :apiId="props.apiId"
            :environment="apiSubData.environment" :onRunApi="runApiForAssertion" @updatePostData="updateAssertionData">
          </assertion>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 响应区域 -->
    <div class="response-section">
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item title="Response" name="1">
          <template #title>
            <div class="response-header">
              <span class="response-title">Response</span>
              <div class="response-meta">
                <span class="response-status">
                  <el-icon>
                    <Compass />
                  </el-icon>
                  Status:
                  <span
                    :class="['status-code', responseData.analysis?.res_status_code >= 400 ? 'status-error' : 'status-success']">
                    {{ responseData.analysis?.res_status_code }}
                  </span>
                </span>
                <span class="response-time">
                  <el-icon>
                    <Timer />
                  </el-icon>
                  Time:
                  <span class="time-value">{{ responseData.analysis?.res_elapsed }} ms</span>
                </span>
                <span class="response-size">
                  <el-icon>
                    <DocumentCopy />
                  </el-icon>
                  Size:
                  <span class="size-value">{{ responseData.analysis?.response_size.total }} B</span>
                </span>
              </div>
            </div>
          </template>
          <div class="response-content">
            <el-tabs v-model="ResponseActiveName" class="response-tabs" @tab-click="ReshandleClick">
              <el-tab-pane label="Body" name="first1">
                <response_body :ResponseJsonData="responseData.analysis?.res_json_data"></response_body>
              </el-tab-pane>
              <el-tab-pane label="Cookies" name="second2">
                <response_cookies></response_cookies>
              </el-tab-pane>
              <el-tab-pane label="Headers" name="third3">
                <response_headers :ResponseHeadersData="responseData.analysis?.res_headers"></response_headers>
              </el-tab-pane>
              <el-tab-pane label="断言结果" name="fourth4">
                <assert_result :AssertResultData="testResultData"></assert_result>
              </el-tab-pane>
              <el-tab-pane label="格式化" name="fifth5">
                <format_json></format_json>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
  <save_as_case_api v-model="dialogSaveAs" :projectId="props.projectId" @saveAsCaseApi="saveAsCaseApi">
  </save_as_case_api>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { ElMessage } from 'element-plus'
import query_params from '@/views/api_test/quick_test/request_tabs/query_params.vue'
import authorization from './request_tabs/authorization.vue'
import pre_oper_script from './request_tabs/pre_oper_script.vue'
import after_oper_script from './request_tabs/after_oper_script.vue'
import exec_sql from './request_tabs/exec_sql.vue'
import headers from './request_tabs/headers.vue'
import request_body from './request_tabs/request_body.vue'
import extract from './request_tabs/extract.vue'
import response_body from './response_tabs/response_body.vue'
import response_headers from './response_tabs/response_headers.vue'
import response_cookies from './response_tabs/response_cookies.vue'
import format_json from './response_tabs/format_json.vue'
import assert_result from './response_tabs/assert_result.vue'
import assertion from './request_tabs/assertion.vue'
import { getEnvDropList } from '@/apis/api_test/quick_test/get_env_list'
import { generateRandomString } from '@/util/generate_random_string';
import { postQuickApi } from '@/apis/api_test/quick_test/post_quick_api'
import { postRunQuickApi } from '@/apis/api_test/quick_test/post_run_quick_api'
import { getQuickApiSubData } from '@/apis/api_test/quick_test/get_quick_api_sub_data'
import { getResponse } from '@/apis/api_test/quick_test/get_response'
import { getTestResult } from '@/apis/api_test/quick_test/get_test_result'
import save_as_case_api from './save_as_case_api.vue'
import { postCaseApi } from "@/apis/api_test/case_test/post_case_api"
import { postCaseTree } from '@/apis/api_test/case_test/post_case_tree'
import popover from '@/views/api_test/popover_data/popover.vue'

const props = defineProps({
  apiData: Object,
  apiSubData: Object,
  responseData: Object,
  testResultData: Array,
  projectId: Number,
  creatorId: String,
  apiId: Number,
  ifStream: Boolean,
  dataLoading: Boolean
})

const dialogSaveAs = ref(false)
const open_save_as = () => {
  dialogSaveAs.value = true
}

const apiName = ref('')
const apiSubData = ref({
  params: [],
  headers: [],
  body: [],
  authorization: {},
  postfix: [],
  pre_script: '',
  post_script: '',
  tests: [],
  request_url: '',
  request_method: '',
  environment: 0,
  content_type: 'none'
})

const responseData: any = ref({})
const testResultData: any = ref([])

watch(() => props.apiData, (newValue) => {
  apiName.value = newValue?.label || ''
}, { immediate: true })

// 监听apiId变化，强制刷新数据状态
watch(() => props.apiId, (newApiId, oldApiId) => {
  if (newApiId !== oldApiId && newApiId) {
    // 当API ID变化时，重置所有数据状态
    apiSubData.value = {
      params: [],
      headers: [],
      body: [],
      authorization: {},
      postfix: [],
      pre_script: '',
      post_script: '',
      tests: [],
      request_url: '',
      request_method: '',
      environment: 0,
      content_type: 'none'
    }
    responseData.value = {}
    testResultData.value = []
  }
}, { immediate: false })

const bodyData = ref()
const contentType = ref('')
// 获取到request_body组件的数据
const updateBodyData = (data: any, type: string) => {
  contentType.value = ''
  if (type === 'json') {
    contentType.value = 'application/json'
  } else if (type === 'form-data') {
    contentType.value = 'multipart/form-data'
  } else if (type === 'x-www-form-urlencoded') {
    contentType.value = 'application/x-www-form-urlencoded'
  } else {
    contentType.value = 'none'
  }
  bodyData.value = data
}

watch(() => props.apiSubData, (newValue) => {
  apiSubData.value = {
    params: [],
    headers: [],
    body: [],
    authorization: {},
    postfix: [],
    pre_script: '',
    post_script: '',
    tests: [],
    request_url: '',
    request_method: '',
    environment: 0,
    content_type: contentType.value,
    ...newValue
  }
  if (apiSubData.value.content_type === 'none') {
    updateBodyData(apiSubData.value.body, apiSubData.value.content_type)
  }
}, { immediate: true })

watch(() => props.responseData, (newValue) => {
  responseData.value = newValue || {}
}, { immediate: true, deep: true })

watch(() => props.testResultData, (newValue) => {
  testResultData.value = newValue || []
}, { immediate: true, deep: true })


const activeName = ref('first')
const ResponseActiveName = ref('first1')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  // console.log(tab, event)
}

const collapseActiveNames = ref(['1'])
const handleChange = (val: string[]) => {
  // console.log(val)
}

const handleCommand = (command: any) => {
  // console.log('执行命令:', command);
  // 根据 command 执行相应操作
};

const activeNames = ref(['1'])
const ReshandleClick = (val: string[]) => {
  // console.log(val)
}

const paramsData = ref([])
// 获取到query_params组件的数据
const updateParamsData = (data: any) => {
  paramsData.value = data
}

const headersData = ref([])
// 获取到headers组件的数据
const updateHeadersData = (data: any) => {
  headersData.value = data
}


const assertionData = ref([])
// 获取到断言组件的数据
const updateAssertionData = (data: any) => {
  assertionData.value = data
}

const authorizationData = ref({})
// 获取到authorization组件的数据
const updateAuthorizationData = (data: any) => {
  authorizationData.value = data
}

const extractData = ref([])
// 获取到提取变量组件的数据
const updateExtractData = (data: any, type: string) => {
  extractData.value = data
}

const preScriptData = ref('')
// 获取到前置脚本组件的数据
const updatePreScriptData = (data: string) => {
  // console.log('前置脚本数据更新:', data)
  preScriptData.value = data
}

const postScriptData = ref('')
// 获取到后置脚本组件的数据
const updatePostScriptData = (data: string) => {
  // console.log('后置脚本数据更新:', data)
  postScriptData.value = data
}

const loading = ref(false)

const pythonPreTemplate = `def ats_script(context):
    """
    前置脚本：请求发送前执行，用于构造参数、生成变量等

    参数：
    - context: 当前变量池，包括项目全局变量、环境变量、前置接口的返回变量等

    内置包与方法（直接使用无需import、其它包不支持，如需扩展请联系管理员）：
    - time、random、re、requests、json、datetime、math
    - print、len、str、int、float、bool、dict、list、tuple、type、isinstance、range、enumerate

    返回：
    - dict：
        {
            "project": { ... },  # 当前项目作用域变量，可跨接口使用
            "env": { ... }       # 当前环境作用域变量，用于登录态、请求头等
        }
    """



    # 获取已有变量（建议提供默认值）
    pre_token = context.get("token", "default-token")
    pre_userid = context.get("userid", 0)
    pre_user_info = context.get("user_info", {})
    pre_user_members = context.get("user_members", [])

    # 生成新的变量
    pre_trace_id = f"{pre_userid}-{random.randint(1000, 9999)}"
    pre_timestamp = int(time.time())

    # 生成变量到项目全局变量或环境变量，后续接口中可以用{{变量名}}引用
    return {
        "env": {
            "pre_token": pre_token,
            "pre_trace_id": pre_trace_id,
            "pre_timestamp": pre_timestamp
        },
        "project": {
            "pre_userid": pre_userid,
            "pre_user_info": pre_user_info,
            "pre_user_members": pre_user_members
        }
    }`;

const pythonPostTemplate = `def ats_script(res_json_data, res_url, res_headers, res_status_code, context):
    """
    后置脚本：在接口响应后执行，用于生成变量（不做断言和提取）

    参数说明：
    - res_json_data: 接口响应 JSON（类型为 dict）
    - res_url: 响应 URL（str）
    - res_headers: 响应头（dict）
    - res_status_code: 响应状态码（int）
    - context: 当前变量池，包括项目全局变量、环境变量、前置接口的返回变量等

    内置包与方法（直接使用无需import、其它包不支持，如需扩展请联系管理员）：
    - time、random、re、requests、json、datetime、math
    - print、len、str、int、float、bool、dict、list、tuple、type、isinstance、range、enumerate

    返回值：
    - dict：
        {
            "project": { ... },  # 当前项目作用域变量，可跨接口使用
            "env": { ... }       # 当前环境作用域变量，用于登录态、请求头等
        }
    """

    # 从响应数据提取字段
    post_user_id = res_json_data.get("user_id", 1)
    post_token = res_json_data.get("token", "post_token")

    # 响应头信息
    post_content_type = res_headers.get("Content-Type", "application/json")

    # 构造认证字段
    post_auth = f"Bearer {post_token}" if post_token else ""

    # 从 context 中获取变量
    post_request_id = context.get("request_id", "123")

    # 生成变量到项目全局变量或环境变量，后续接口中可以用{{变量名}}引用
    return {
        "env": {
            "post_auth": post_auth,
            "post_token": post_token,
            "post_content_type": post_content_type,
            "post_status_code": res_status_code
        },
        "project": {
            "post_user_id": post_user_id,
            "post_request_id": post_request_id
        }
    }`;

const onSubmit = async () => {
  // 动态构建表单数据，确保使用的是最新的 ref 值
  const formData = {
    case_code: 'quick_test_' + generateRandomString(10),
    api_name: apiName.value,
    module_name: 'quick_test',
    case_name: 'quick_test',
    request_path: '',
    environment: apiSubData.value.environment,
    request_url: apiSubData.value.request_url,
    request_method: apiSubData.value.request_method,
    params: paramsData.value,
    headers: headersData.value,
    content_type: contentType.value || 'none',
    body: bodyData.value || [],
    authorization: authorizationData.value,
    pre_script: preScriptData.value || pythonPreTemplate,
    post_script: postScriptData.value || pythonPostTemplate,
    postfix: extractData.value,
    tests: assertionData.value,
    creator: props.creatorId,
    execution_count: 1,
    timeout: 10,
    if_execution: 1,
    folder: props.apiId,
    project: props.projectId
  };

  // console.log('接口发送，保存所有数据', formData);

  if (formData.request_url === "" || formData.request_method === "") {
    ElMessage.error('请填写请求地址和请求方法')
    return
  }

  loading.value = true

  const res = await postQuickApi(formData)
  if (res && res.data.code === 2000) {
    ElMessage.success('操作成功')
  } else {
    ElMessage.error('操作失败')
  }
  loading.value = false
  getApiSubData()
}

// 单接口测试
const onSend = async () => {
  // 动态构建表单数据，确保使用的是最新的 ref 值
  loading.value = true
  const onSendFormData = {
    environment: apiSubData.value.environment,
    folder_id: props.apiId,
    project: props.projectId
  };

  // console.log('接口发送', onSendFormData);


  const res = await postRunQuickApi(onSendFormData)
  if (res && res.data.code === 2000) {
    ElMessage.success('操作成功')
    await new Promise(resolve => setTimeout(resolve, 500))
    getResponseData()
    getTestResultData()
  } else {
    ElMessage.error(res.data.msg)
  }
  loading.value = false
}

// 为断言组件提供的接口运行方法
const runApiForAssertion = async () => {
  const onSendFormData = {
    environment: apiSubData.value.environment,
    folder_id: props.apiId,
    project: props.projectId
  };

  const res = await postRunQuickApi(onSendFormData)
  if (res && res.data.code === 2000) {
    // 直接从postRunQuickApi的返回数据中获取需要的字段
    const responseData = res.data.data
    return {
      res_json_data: responseData.res_json_data,
      res_headers: responseData.res_headers,
      res_status_code: responseData.res_status_code
    }
  } else {
    throw new Error(res.data.msg || '接口运行失败')
  }
}

interface envDropList {
  id: number
  name: string
}

const envDropList = ref<envDropList[]>([])
const getEnvList = async () => {
  const res = await getEnvDropList(props.projectId as number)
  if (res && res.data.code === 2000) {
    envDropList.value = res.data.data
  }
}

getEnvList()

const emit = defineEmits(['getApiSubData', 'getResponseData'])
const apiSubData1 = ref({})
const getApiSubData = async () => {
  const res = await getQuickApiSubData(apiName.value)
  if (res && res.data.code === 2000) {
    apiSubData1.value = res.data.data
    emit('getApiSubData', apiSubData1.value)
  }
}

const getResponseData = async () => {
  const res = await getResponse(props.apiId as number)
  if (res && res.data.code === 2000) {
    responseData.value = res.data.data
    emit('getResponseData', responseData.value)
  }
}

const getTestResultData = async () => {
  const res = await getTestResult(props.apiId as number)
  if (res && res.data.code === 2000) {
    testResultData.value = res.data.data
  }
}

interface RuleForm {
  name: string
  parent: number
  type: string
  if_stream: boolean
}


const saveAsCaseApi = async (parent_folder_id: number) => {
  const ruleForm = reactive<RuleForm>({
    name: apiName.value || '未命名',
    parent: parent_folder_id || 0,
    type: '0',
    if_stream: props.ifStream
  })
  let save_as_folder_id = ref(0)
  // 先建立一个接口，用于保存用例
  const response = await postCaseTree(ruleForm)
  if (response && response.data.code === 2000) {
    save_as_folder_id.value = response.data.data.id
    const formData1 = {
      api_source: 'save_as_case_api',
      case_code: 'quick_test_' + generateRandomString(10),
      api_name: apiName.value,
      module_name: 'quick_test',
      case_name: 'quick_test',
      request_path: '',
      environment: apiSubData.value.environment,
      request_url: apiSubData.value.request_url,
      request_method: apiSubData.value.request_method,
      params: paramsData.value,
      headers: headersData.value,
      content_type: contentType.value || 'none',
      body: bodyData.value || [],
      authorization: authorizationData.value,
      pre_script: preScriptData.value || pythonPreTemplate,
      post_script: postScriptData.value || pythonPostTemplate,
      postfix: extractData.value,
      tests: assertionData.value,
      creator: props.creatorId,
      execution_count: 1,
      timeout: 10,
      if_execution: 1,
      folder: save_as_folder_id.value,
      project: props.projectId
    };

    if (!formData1.request_url || !formData1.request_method) {
      return ElMessage.error('请填写请求方法和请求地址')
    }

    const res = await postCaseApi(formData1)
    if (res && res.data.code === 2000) {
      ElMessage.success('操作成功')
    } else {
      ElMessage.error('操作失败')
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: auto;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-sizing: border-box;
}

/* 头部区域样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
  flex-wrap: wrap;
  gap: 10px;
}

.api-info {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  flex: 1;
  min-width: 200px;
}

.api-name {
  display: flex;
  align-items: center;
}

.api-name .label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
}

.api-name .value {
  color: #303133;
  font-weight: 500;
}

.environment-selector {
  display: flex;
  align-items: center;
}

.environment-selector .label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
}

.env-select {
  width: 200px;
}

/* 请求区域样式 */
.request-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
}

.method-select {
  width: 110px;
  flex-shrink: 0;
}

.method-dropdown {
  width: 100%;
}

.method-option {
  font-weight: 600;
}

.method-get {
  color: #67c23a;
}

.method-post {
  color: #e6a23c;
}

.method-put {
  color: #409eff;
}

.method-delete {
  color: #f56c6c;
}

.method-patch {
  color: #9254de;
}

.method-options {
  color: #909399;
}

.url-input {
  flex: 1;
  min-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.send-button,
.save-button {
  min-width: 80px;
}

/* 标签页区域样式 */
.tabs-section {
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
  min-height: 350px;
  max-height: 500px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: auto;
  /* 允许滚动 */
  position: relative;
  /* 建立定位上下文 */
}

/* 请求标签页样式调整 */
.request-tabs {
  width: 100%;
  position: relative;
  /* 定位上下文 */
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
  position: relative;
  overflow: hidden;
  /* 隐藏可能的溢出 */
}

:deep(.el-tabs__nav-wrap) {
  position: relative;
  overflow: hidden;
  /* 隐藏滚动条但保持布局 */
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap)::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: #e4e7ed;
  z-index: 1;
}

:deep(.el-tabs__nav) {
  position: relative;
  display: flex;
  white-space: nowrap;
  z-index: 2;
}

:deep(.el-tabs__item) {
  padding: 0 16px;
  height: 40px;
  line-height: 40px;
  position: relative;
  flex-shrink: 0;
  color: #606266;
  cursor: pointer;
  transition: color 0.3s;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

/* 响应标签页样式 */
.response-tabs {
  width: 100%;
  position: relative;
}

.response-tabs :deep(.el-tabs__header) {
  margin-bottom: 16px;
  overflow: hidden;
}

.response-tabs :deep(.el-tabs__nav-wrap) {
  overflow: hidden;
}

/* 响应区域样式 */
.response-section {
  background-color: #fff;
  border-radius: 6px;
  margin-top: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.response-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.response-meta {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.response-status,
.response-time,
.response-size {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.status-code {
  font-weight: 600;
}

.status-success {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.time-value,
.size-value {
  color: #67c23a;
  font-weight: 500;
}

.response-content {
  width: 100%;
  overflow: auto;
  position: relative;
  z-index: 3;
  min-height: 300px;
  padding: 16px;
  box-sizing: border-box;
}

/* 其他辅助样式 */
:deep(.el-collapse-item__content) {
  overflow: visible;
  max-height: none !important;
}

:deep(.el-tabs__content) {
  overflow: visible;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  position: relative;
  z-index: 1;
  padding: 12px 16px;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #ebeef5;
  height: 1px;
}

/* 新增媒体查询,适应小屏 */
@media (max-width: 1200px) {
  .request-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .method-select,
  .url-input {
    width: 100%;
  }

  .action-buttons {
    align-self: flex-end;
    margin-top: 10px;
  }

  .response-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 增加媒体查询处理窄屏 */
@media (max-width: 768px) {
  :deep(.el-tabs__item) {
    padding: 0 10px;
    /* 窄屏下减小padding */
    font-size: 13px;
  }
}
</style>