<template>
  <el-dialog v-model="dialogVisible" title="保存为用例" width="30%" :before-close="handleClose">
  <el-input
      v-model="filterText"
      style="width: 240px"
      placeholder="Filter keyword"
  />

  <el-tree
      ref="treeRef"
      style="max-width: 600px"
      class="filter-tree"
      :data="data"
      :props="defaultProps"
      default-expand-all
      highlight-current
      check-on-click-node
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
  >
    <template #default="{ node, data }">
      <div class="tree-node">
        <span class="tree-icon">
          <el-icon><Folder /></el-icon>
        </span>
        <span class="node-label">{{ node.label }}</span>
      </div>
    </template>
  </el-tree>

  <template #footer>
    <span class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitForm">
        确认
      </el-button>
    </span>
  </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref, watch, watchEffect} from 'vue'
import {ElTree} from 'element-plus'
import {getSaveAsTree} from "@/apis/api_test/case_test/get_save_as_tree";
import {Folder} from "@element-plus/icons-vue";

interface Tree {
  [key: string]: any
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

const emit = defineEmits(['update:modelValue','saveAsCaseApi']) // 定义emit函数
const props = defineProps({
  projectId: Number,
  modelValue: Boolean
})

const dialogVisible = ref(false)
watchEffect(() => {
  dialogVisible.value = props.modelValue
})

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}


interface TreeNode {
  name?: string;
  children?: TreeNode[] | null;
  [key: string]: any;
}

function renameKeys(data: TreeNode[]): TreeNode[] {
  return data.map(({ name: label, children, ...rest }) => ({
    label,
    ...rest,
    children: children ? renameKeys(children) : null,
  }));
}

const data = ref<TreeNode[]>([])

const getTree = async () => {
  if (props.projectId) {
    const res = await getSaveAsTree(props.projectId)
    if (res) {
      let treeData = res.data.data
      data.value = renameKeys([treeData])
    }
  }

}

getTree()

const case_tree_folder_id = ref(0)
const handleNodeClick = (data: Tree) => {
  if (data.type === '1') {
    case_tree_folder_id.value = data.id
  }
}

const submitForm = async () => {
  emit('saveAsCaseApi', case_tree_folder_id.value)
  emit('update:modelValue', false)
}

</script>

<style scoped>
</style>
