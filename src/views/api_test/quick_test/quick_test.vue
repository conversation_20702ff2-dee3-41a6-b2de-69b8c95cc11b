<template>
    <div class="common-layout resizable-container">
        <div class="split-pane">
            <div class="aside-wrapper" :style="{ width: leftWidth + 'px' }">
                <el-card style="height: 100%;">
                    <quick_tree :projectId='projectId' @ifShowApi="ifShowApi" @getFolderOrApi="getFolderOrApi" @getApiSubData="getApiSubData" @getResponseData="getResponseData" @getTestResultData="getTestResultData" @nodeNameUpdated="handleNodeNameUpdated" @updateLoadingState="updateLoadingState"></quick_tree>
                </el-card>
            </div>
            <div 
                class="resizer"
                @mousedown="startResize"
                @touchstart="startTouchResize">
                <div class="resizer-handle"></div>
            </div>
            <div class="main" :style="{ width: `calc(100% - ${leftWidth}px - 10px)` }">
                <el-card style="height: 100%; display: flex; flex-direction: column;">
                    <div style="flex: 1; overflow: auto; position: relative;">
                                                 <quick_test_api v-if="ifShow === '0'" :key="apiId" :ifStream="ifStream" :apiSubData="apiSubData" :responseData="responseData" :testResultData="testResultData" @getApiSubData="getApiSubData" @getResponseData="getResponseData" :apiData="folderOrApiData" :projectId="projectId" :creatorId='creatorId' :apiId='apiId' :dataLoading="dataLoading"></quick_test_api>
                        <quick_test_folder v-else-if="ifShow === '1'" :folderData="folderOrApiData"></quick_test_folder>
                        <after_delete_node v-else-if="ifShow === '999'"></after_delete_node>
                    </div>
                </el-card>
            </div>
        </div>
    </div>
</template>


<script setup lang='ts'>
import { ref, watchEffect, onUnmounted } from 'vue'
import quick_tree from './quick_tree.vue'
import { useStore } from '@/stores';
import quick_test_api from '@/views/api_test/quick_test/quick_test_api.vue'
import quick_test_folder from './quick_test_folder.vue';
import after_delete_node from './after_delete_node.vue';

interface ProjectInfo {
    id: number
    // add other properties as needed
}

const projectStore = useStore()
const projectId = ref((projectStore.project_info as ProjectInfo).id)
const creatorId = ref((projectStore.user_info as any).username)


watchEffect(()=>{
    projectId.value = (projectStore.project_info as ProjectInfo).id
    creatorId.value = (projectStore.user_info as any).username
})

const ifShow = ref('')
const apiId = ref(0)
const ifStream = ref(false)
const ifShowApi = (type:string, tree_node_id:number, if_stream:boolean) => {
    ifShow.value = type
    apiId.value = tree_node_id
    ifStream.value = if_stream
}


const folderOrApiData = ref({})
const getFolderOrApi = (data:object) => {
    folderOrApiData.value = data
}

const apiSubData = ref({})
const getApiSubData = (data:any) => {
    apiSubData.value = data
}

const responseData = ref<Record<string, any> | undefined>({})
const getResponseData = (data: any) => {
    // console.log("getResponseData", data);
    // 直接将接收到的数据赋值给responseData 这边可能是对象也可能是字符串
    responseData.value = data;
}

const testResultData = ref([])
const getTestResultData = (data:any) => {
    testResultData.value = data
}

const dataLoading = ref(false)
const updateLoadingState = (loading: boolean) => {
    dataLoading.value = loading
}

const handleNodeNameUpdated = (newName: string) => {
  if ('label' in folderOrApiData.value) {
    (folderOrApiData.value as any).label = newName;
  }
}

// 拖拽分隔线相关逻辑
const leftWidth = ref(360) // 初始宽度300px
const minWidth = 0 // 最小宽度200px
const maxWidth = 500 // 最大宽度500px
let startX = 0
let startWidth = 0
let isDragging = false

// 鼠标拖拽开始
const startResize = (e: MouseEvent) => {
    isDragging = true
    document.body.classList.add('resizing')
    startX = e.clientX
    startWidth = leftWidth.value

    // 添加事件监听
    document.addEventListener('mousemove', resize)
    document.addEventListener('mouseup', stopResize)
    
    // 防止选中文本
    e.preventDefault()
}

// 触摸拖拽开始
const startTouchResize = (e: TouchEvent) => {
    isDragging = true
    document.body.classList.add('resizing')
    startX = e.touches[0].clientX
    startWidth = leftWidth.value

    // 添加事件监听
    document.addEventListener('touchmove', touchResize)
    document.addEventListener('touchend', stopTouchResize)
    
    // 防止选中文本
    e.preventDefault()
}

// 鼠标拖拽过程
const resize = (e: MouseEvent) => {
    if (!isDragging) return
    
    const offset = e.clientX - startX
    let newWidth = startWidth + offset
    
    // 限制最小和最大宽度
    if (newWidth < minWidth) newWidth = minWidth
    if (newWidth > maxWidth) newWidth = maxWidth
    
    leftWidth.value = newWidth
}

// 触摸拖拽过程
const touchResize = (e: TouchEvent) => {
    if (!isDragging) return
    
    const offset = e.touches[0].clientX - startX
    let newWidth = startWidth + offset
    
    // 限制最小和最大宽度
    if (newWidth < minWidth) newWidth = minWidth
    if (newWidth > maxWidth) newWidth = maxWidth
    
    leftWidth.value = newWidth
}

// 鼠标拖拽结束
const stopResize = () => {
    isDragging = false
    document.body.classList.remove('resizing')
    document.removeEventListener('mousemove', resize)
    document.removeEventListener('mouseup', stopResize)
}

// 触摸拖拽结束
const stopTouchResize = () => {
    isDragging = false
    document.body.classList.remove('resizing')
    document.removeEventListener('touchmove', touchResize)
    document.removeEventListener('touchend', stopTouchResize)
}

// 组件卸载时清理事件监听和样式
onUnmounted(() => {
    document.body.classList.remove('resizing')
    document.removeEventListener('mousemove', resize)
    document.removeEventListener('mouseup', stopResize)
    document.removeEventListener('touchmove', touchResize)
    document.removeEventListener('touchend', stopTouchResize)
})
</script>

<style scoped>
.common-layout {
  height: 100vh;
  overflow: hidden;
}

.split-pane {
  display: flex;
  height: 100%;
  position: relative;
}

.aside-wrapper {
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  transition: width 0.1s;
}

.main {
  flex-grow: 1;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  transition: width 0.1s;
  display: flex;
  flex-direction: column;
}

.main el-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible;
}

:deep(.el-card__body) {
  overflow: visible;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.resizer {
  width: 10px;
  height: 100%;
  background-color: #ffffff;
  cursor: col-resize;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  flex-shrink: 0;
  z-index: 10;
}

.resizer-handle {
  width: 2px;
  height: 30px;
  background-color: #dcdfe6;
  border-radius: 1px;
}

.resizer:hover .resizer-handle {
  background-color: #409eff;
}

/* 拖拽过程中改变指针样式 */
.resizable-container.dragging {
  cursor: col-resize;
}
</style>

<style>
/* 全局样式，在body上添加拖拽时的样式 */
body.resizing {
  cursor: col-resize !important;
  user-select: none;
}

body.resizing * {
  cursor: col-resize !important;
}
</style>