<template>
  <div>
    <!-- 添加提示信息区域 -->
    <div class="tip-container">
      <div class="tip-content">
        <el-icon class="tip-icon">
          <InfoFilled />
        </el-icon>
        <span>用户可以在此上传openapi类型的接口文档并交由系统及大模型处理，即可快速完成解析、用例生成等功能，大幅提升接口测试效率，执行成功后，请前往 [接口用例菜单-AI生成目录下] 进行调试</span>
      </div>
    </div>
    <div class="button-container">
      <!-- 添加模型选择下拉框 -->
      <el-select v-model="selectedModel" placeholder="选择模型" class="compact-select">
        <el-option v-for="item in filteredModelOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>

      <el-button type="primary" @click="showUploadDialog">
        <el-icon>
          <Plus />
        </el-icon> 上传文件
      </el-button>

      <el-button type="primary" @click="refreshFileList">
        <el-icon>
          <Refresh />
        </el-icon> 刷新
      </el-button>
    </div>

    <!-- 文件列表表格 -->
    <div class="table-container">
      <el-table v-loading="tableLoading" :data="tableData" style="width: 100%">
        <el-table-column prop="file_name" label="文件名称" min-width="200" />
        <el-table-column prop="file_size" label="文件大小" width="180">
          <template #default="scope">
            {{ formatFileSize(scope.row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column prop="user_id" label="创建人" width="180">
          <template #default="scope">
            {{ getUsernameById(scope.row.user_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template v-slot="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="scope">
            <el-button type="primary" link @click="parseFile(scope.row)" :loading="scope.row.parsing"
              :disabled="scope.row.parsing">
              {{ scope.row.parsing ? '解析中' : '生成' }}
            </el-button>
            <el-button type="info" link @click="checkParseStatus(scope.row)">
              查看进度
            </el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)" :disabled="scope.row.parsing">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 上传文件对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传文件" width="500px">
      <div class="upload-area">
        <el-upload ref="uploadRef" class="upload-component" :action="uploadUrl" :auto-upload="false" :limit="1"
          :on-change="handleFileChange" :on-exceed="handleExceed" :file-list="fileList" accept=".json"
          :data="uploadData" :headers="uploadHeaders" :on-success="handleUploadSuccess" :on-error="handleUploadError"
          :before-upload="beforeUploadCheck">
          <template #trigger>
            <el-button type="primary">选择文件</el-button>
          </template>

          <template #tip>
            <div class="el-upload__tip">
              仅支持 OpenAPI 3.1格式的 .json 文件，且文件大小不超过 5MB
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" :disabled="!selectedFile" @click="submitUpload" :loading="uploading">
            {{ uploading ? '上传中...' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 解析任务详情抽屉 -->
    <el-drawer v-model="drawerVisible" direction="rtl" :title="null" size="60%" custom-class="parse-detail-drawer">
      <template #header>
        <div class="drawer-header">
          <h2 class="drawer-title">解析任务详情</h2>
        </div>
      </template>

      <div v-if="currentParseResult && currentParseResult.length > 0" class="parse-result-container">
        <div class="parse-status">
          <div class="status-summary">
            <h3>解析状态概览</h3>
            <div class="status-metrics">
              <el-statistic title="总任务数" :value="currentParseResult.length" class="metric-item">
                <template #suffix>
                  <el-icon class="metric-icon">
                    <DocumentChecked />
                  </el-icon>
                </template>
              </el-statistic>
              <el-statistic title="已完成" :value="getStatusCount('SUCCESS')" class="metric-item">
                <template #suffix>
                  <el-icon class="metric-icon success-icon">
                    <Check />
                  </el-icon>
                </template>
              </el-statistic>
              <el-statistic title="处理中" :value="getStatusCount('PROCESSING') + getStatusCount('PENDING')"
                class="metric-item">
                <template #suffix>
                  <el-icon class="metric-icon processing-icon">
                    <Loading />
                  </el-icon>
                </template>
              </el-statistic>
              <el-statistic title="失败" :value="getStatusCount('FAILURE')" class="metric-item">
                <template #suffix>
                  <el-icon class="metric-icon failure-icon">
                    <CircleClose />
                  </el-icon>
                </template>
              </el-statistic>
            </div>
          </div>

          <div class="refresh-status">
            <el-button type="primary" size="small" @click="refreshParseStatus" :loading="refreshingStatus"
              class="refresh-button">
              <el-icon>
                <Refresh />
              </el-icon> 刷新状态
            </el-button>
          </div>

          <el-divider class="status-divider" />

          <div class="status-list">
            <div v-for="(task, index) in currentParseResult" :key="index" class="status-item">
              <el-card
                :class="{ 'task-card': true, 'task-success': task.status === 'SUCCESS', 'task-processing': task.status === 'PROCESSING' || task.status === 'PENDING', 'task-failure': task.status === 'FAILURE' }">
                <template #header>
                  <div class="task-header">
                    <span class="task-id">任务ID: {{ task.task_id }}</span>
                    <div class="task-status">
                      <el-tag :type="getStatusType(task.parse_task_status)" size="small" class="status-tag">解析: {{
                        task.parse_task_status }}</el-tag>
                      <el-tag :type="getStatusType(task.test_cases_task_status)" size="small" class="status-tag">测试用例:
                        {{ task.test_cases_task_status }}</el-tag>
                      <el-tag :type="getStatusType(task.status)" class="status-tag">总体状态: {{ task.status }}</el-tag>
                    </div>
                  </div>
                </template>

                <div class="task-info">
                  <div class="api-info" v-if="task.openapi_data && Object.keys(task.openapi_data).length > 0">
                    <h4 class="info-title">API 信息</h4>
                    <div class="api-details">
                      <p class="info-item"><strong>接口路径:</strong> {{ task.openapi_data.path || '暂无路径' }}</p>
                      <p class="info-item" v-if="task.openapi_data.method"><strong>请求方法:</strong> {{
                        task.openapi_data.method.toUpperCase() }}</p>
                      <p class="info-item" v-if="task.openapi_data.server && task.openapi_data.server.url">
                        <strong>URL:</strong> {{ task.openapi_data.server.url }}
                      </p>
                      <p class="info-item"
                        v-if="task.openapi_data.operation && task.openapi_data.operation.description">
                        <strong>描述:</strong> {{ task.openapi_data.operation.description }}
                      </p>
                    </div>
                  </div>

                  <el-divider
                    v-if="task.llm_result && task.llm_result.test_cases && task.llm_result.test_cases.length > 0"
                    class="info-divider" />

                  <div class="test-cases"
                    v-if="task.llm_result && task.llm_result.test_cases && task.llm_result.test_cases.length > 0">
                    <h4 class="info-title">生成的测试用例 ({{ task.llm_result.test_cases.length }}个)</h4>
                    <el-collapse class="test-cases-collapse">
                      <el-collapse-item v-for="(testCase, caseIndex) in task.llm_result.test_cases" :key="caseIndex"
                        :title="testCase.api_name || `测试用例 ${caseIndex + 1}`" class="test-case-item">
                        <div class="test-case-detail">
                          <p class="info-item"><strong>URL:</strong> {{ testCase.request_url }}</p>
                          <p class="info-item"><strong>请求方法:</strong> {{ testCase.request_method }}</p>
                          <p class="info-item"><strong>内容类型:</strong> {{ testCase.content_type }}</p>

                          <div v-if="testCase.headers && Object.keys(testCase.headers).length > 0"
                            class="headers-section">
                            <h5 class="sub-title">请求头</h5>
                            <el-descriptions :column="1" border class="headers-table">
                              <el-descriptions-item v-for="(value, key) in testCase.headers" :key="key" :label="key">
                                {{ value }}
                              </el-descriptions-item>
                            </el-descriptions>
                          </div>

                          <div v-if="testCase.body" class="request-body">
                            <h5 class="sub-title">请求体</h5>
                            <pre class="body-content">{{ typeof testCase.body === 'object' ? JSON.stringify(testCase.body,
                          null, 2) : testCase.body }}</pre>
                          </div>

                          <div v-if="testCase.tests && testCase.tests.length > 0" class="assertions-section">
                            <h5 class="sub-title">断言 ({{ testCase.tests.length }}个)</h5>
                            <el-table :data="testCase.tests" stripe style="width: 100%" class="assertions-table">
                              <el-table-column prop="name" label="断言名称" />
                              <el-table-column prop="description" label="描述" />
                            </el-table>
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>

                  <div v-if="task.error_message" class="error-message">
                    <h4 class="error-title">错误信息</h4>
                    <el-alert type="error" :closable="false" show-icon class="error-alert">
                      {{ task.error_message }}
                    </el-alert>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty-result">
        <el-empty description="暂无解析结果" />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile, UploadFile, UploadFiles } from 'element-plus'
import {
  Refresh,
  Check,
  CircleClose,
  Loading,
  DocumentChecked,
  Plus,
  InfoFilled
} from '@element-plus/icons-vue'
import { parserOpenaiFile } from '@/apis/api_test/smart_api/parser_openai_file'
import { getParserFileStatus } from '@/apis/api_test/smart_api/get_parser_file_status'
import { getAllSmartFile } from '@/apis/api_test/smart_api/get_all_smart_file'
import { deleteSmartFile } from '@/apis/api_test/smart_api/delete_smart_file'
import { getAllOpenaiConfig } from '@/apis/project/openai_config/get_all_openai_config'
import { useStore } from '@/stores'
import { formatDate } from '@/util/format_date';
import Cookies from 'js-cookie'


// 定义类型
interface UploadedFile {
  file_id: number;
  file_name: string;
  file_path: string;
  status: string;
  action: string;
  parsing?: boolean;
  taskIds?: string[];
  create_time?: string;
}

interface ProjectInfo {
  id: number;
  [key: string]: any;
}

interface UserInfo {
  id: number;
  [key: string]: any;
}

interface ParseTask {
  id: number;
  task_id: string;
  status: string;
  parse_task_status: string;
  test_cases_task_status: string;
  openapi_data: any;
  llm_result: any;
  error_message: string | null;
  create_time: string;
  update_time: string;
  file: number;
}

// 获取store
const mainStore = useStore()
const projectStore = useStore()
const dicStore = useStore()
const userStore = useStore()
const uploadRef = ref<UploadInstance>()

// 动态配置上传URL
const dicKey = 'upload_file_server_url'
const uploadFileServerUrl = ref('http://127.0.0.1:8000')
const uploadUrl = ref('http://127.0.0.1:8000/api/api_test/smart_api_file_upload/')

watch(() => dicStore.dic_list, () => {
  const found = dicStore.dic_list.find((o: any) => o.dic_key === dicKey);
  if (found) {
    uploadFileServerUrl.value = found.dic_value
    uploadUrl.value = `${uploadFileServerUrl.value}/api/api_test/smart_api_file_upload/`
    console.log('uploadUrl', uploadUrl.value)
  }
}, { deep: true, immediate: true })

// 上传相关状态
const fileList = ref<any[]>([]) // el-upload 需要的 fileList
const selectedFile = ref<UploadRawFile | null>(null)
const uploadedFiles = ref<UploadedFile[]>([])
const uploading = ref(false)
const refreshingStatus = ref(false)
const currentFileId: any = ref(null)
const uploadDialogVisible = ref(false)

// 表格相关状态
const tableData = ref<UploadedFile[]>([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const userInfo = computed(() => userStore.user_info)

// 抽屉相关
const drawerVisible = ref(false)
const currentParseResult = ref<ParseTask[] | null>(null)

// 获取 Token
const RequestToken = Cookies.get('token')

// 计算上传所需的 data 和 headers
const uploadData = computed(() => ({
  file_type: '1',
  project_id: ((projectStore.project_info as ProjectInfo)?.id || 0).toString(),
  user_id: ((mainStore.user_info as UserInfo)?.id || 0).toString()
}))

const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${RequestToken}`
}))

// 定义parsingFiles变量，用于存储正在解析中的文件ID
const parsingFiles = ref<number[]>([])

// 创建一个Map来存储每个解析任务的开始时间
const parseStartTimes = ref(new Map<number, number>())

// 检查解析是否超时（超过10分钟）
const isParseTimeout = (fileId: number): boolean => {
  const startTime = parseStartTimes.value.get(fileId)
  if (!startTime) return false

  // 计算经过的时间（毫秒）
  const elapsedTime = Date.now() - startTime
  // 10分钟超时（10 * 60 * 1000 = 600000毫秒）
  return elapsedTime > 600000
}

// 保存解析状态到localStorage
const saveParsingStatus = () => {
  // 筛选出正在解析的文件ID
  const parsing = tableData.value
    .filter(file => file.parsing)
    .map(file => file.file_id)

  if (parsing.length > 0) {
    // 存储到localStorage
    localStorage.setItem('parsing_files', JSON.stringify(parsing))

    // 同时保存开始时间Map，转为数组存储
    const timesArray = Array.from(parseStartTimes.value.entries())
    localStorage.setItem('parsing_start_times', JSON.stringify(timesArray))
  } else {
    // 如果没有正在解析的文件，清除localStorage
    localStorage.removeItem('parsing_files')
    localStorage.removeItem('parsing_start_times')
  }
}

// 从localStorage恢复解析状态
const restoreParsingStatus = () => {
  const stored = localStorage.getItem('parsing_files')
  const storedTimes = localStorage.getItem('parsing_start_times')

  if (stored) {
    try {
      parsingFiles.value = JSON.parse(stored)

      // 恢复开始时间
      if (storedTimes) {
        parseStartTimes.value = new Map(JSON.parse(storedTimes))
      } else {
        // 如果没有存储时间，为每个文件设置当前时间减去5分钟的时间，这样还有5分钟超时时间
        const fiveMinutesAgo = Date.now() - 300000
        parsingFiles.value.forEach(fileId => {
          parseStartTimes.value.set(fileId, fiveMinutesAgo)
        })
      }

      // 更新表格中的文件状态
      if (tableData.value && tableData.value.length > 0) {
        tableData.value.forEach(file => {
          if (parsingFiles.value.includes(file.file_id)) {
            file.parsing = true
          }
        })
      }
    } catch (e) {
      console.error('恢复解析状态失败:', e)
      localStorage.removeItem('parsing_files')
      localStorage.removeItem('parsing_start_times')
    }
  }
}

// 自动检查解析中的文件状态
const checkParsingFiles = async () => {
  if (parsingFiles.value.length === 0) return

  for (const fileId of parsingFiles.value) {
    // 查找文件
    const file = tableData.value.find(f => f.file_id === fileId)
    if (file) {
      // 检查是否已超时
      if (isParseTimeout(fileId)) {
        // 如果已超时，强制解锁
        file.parsing = false
        // 从解析中的文件列表中移除
        parsingFiles.value = parsingFiles.value.filter(id => id !== fileId)
        // 保存状态
        saveParsingStatus()
        // 移除开始时间记录
        parseStartTimes.value.delete(fileId)
        // 显示提示
        ElMessage.warning(`文件 "${file.file_name}" 解析时间超过10分钟，已自动解锁`)
        continue
      }

      // 获取解析状态
      try {
        const response = await getParserFileStatus(fileId)

        // 检查响应格式
        if (response && response.data && response.data.results && response.data.results.code === 2000) {
          const tasks = response.data.results.data

          // 检查是否所有任务都已完成
          const allCompleted = tasks.every((task: any) =>
            task.status === 'SUCCESS' || task.status === 'FAILURE' || task.status === 'PARTIAL_SUCCESS'
          )

          if (allCompleted) {
            // 更新文件状态为已完成
            file.parsing = false
            // 从正在解析的文件列表中移除
            parsingFiles.value = parsingFiles.value.filter(id => id !== fileId)
            // 保存状态
            saveParsingStatus()
            // 移除开始时间记录
            parseStartTimes.value.delete(fileId)
          }
        }
      } catch (error) {
        console.error('检查解析状态失败:', error)

        // 如果检查失败且已超时，也解锁
        if (isParseTimeout(fileId)) {
          file.parsing = false
          parsingFiles.value = parsingFiles.value.filter(id => id !== fileId)
          saveParsingStatus()
          parseStartTimes.value.delete(fileId)
        }
      }
    }
  }
}

// 显示上传对话框
const showUploadDialog = () => {
  uploadDialogVisible.value = true
  // 清空选择的文件
  fileList.value = []
  selectedFile.value = null
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 处理文件选择
const handleFileChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
  // 仅保留最后一个文件
  if (uploadFiles.length > 1) {
    uploadFiles.splice(0, uploadFiles.length - 1);
  }
  // 获取原始文件对象用于后续检查或操作
  selectedFile.value = uploadFile.raw || null;
  // 更新 fileList 以反映在 UI 上
  fileList.value = uploadFiles;
}

// 处理超出文件限制
const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  ElMessage.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + uploadFiles.length} 个文件`)
}

// 上传前检查
const beforeUploadCheck: UploadProps['beforeUpload'] = (rawFile) => {
  if (!rawFile.name.endsWith('.json')) {
    ElMessage.error('只能上传JSON文件')
    return false
  }
  if (rawFile.size / 1024 / 1024 > 5) {
    ElMessage.error('文件大小不能超过5MB!')
    return false
  }
  uploading.value = true; // 开始上传，设置状态
  return true
}

// 处理上传成功
const handleUploadSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  uploading.value = false;
  if (response.code === 2000) {
    ElMessage.success('文件上传成功')
    // 添加到已上传文件列表
    uploadedFiles.value = [...uploadedFiles.value, ...response.data]
    // 关闭对话框
    uploadDialogVisible.value = false
    // 清空选择的文件
    fileList.value = []
    selectedFile.value = null
    if (uploadRef.value) {
      uploadRef.value.clearFiles() // 清空 el-upload 内部文件列表
    }
    // 刷新文件列表
    fetchFileList()
  } else {
    ElMessage.error(response.msg || '上传失败')
    // 如果上传失败，可能需要从 fileList 中移除该文件
    const index = fileList.value.findIndex(f => f.uid === uploadFile.uid);
    if (index > -1) {
      fileList.value.splice(index, 1);
    }
    selectedFile.value = null
  }
}

// 处理上传失败
const handleUploadError: UploadProps['onError'] = (error, uploadFile) => {
  uploading.value = false;
  console.error('上传失败:', error)
  let errorMessage = '上传失败，请检查网络或联系管理员';
  try {
    // 尝试解析错误信息
    const errorData = JSON.parse(error.message);
    errorMessage = errorData.msg || errorData.detail || errorMessage;
  } catch (e) {
    // 解析失败，使用默认错误信息
  }
  ElMessage.error(errorMessage);
  // 从 fileList 中移除失败的文件
  const index = fileList.value.findIndex(f => f.uid === uploadFile.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
  selectedFile.value = null
}

// 触发上传
const submitUpload = () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  uploadRef.value!.submit() // 调用 el-upload 的 submit 方法
}

// 添加模型选择相关
interface ModelOption {
  id: number;
  name: string;
  type: string;
  value: Record<string, any>;
  creator: string;
  create_time: string;
  project: number;
}

const modelOptions = ref<ModelOption[]>([])
const selectedModel = ref<number | null>(null)

// 过滤计算属性 - 只显示type为1的模型
const filteredModelOptions = computed(() => {
  return modelOptions.value.filter(model => model.type === '1')
})

// 加载模型选项
const loadModelOptions = async () => {
  try {
    const response = await getAllOpenaiConfig((projectStore.project_info as ProjectInfo)?.id || 0)
    // console.log('API返回模型数据:', response)
    if (response?.data?.data) {
      modelOptions.value = response.data.data

      // 查找第一个类型为1的模型并设为默认选择
      const firstModel = modelOptions.value.find(model => model.type === '1')
      if (firstModel) {
        // console.log('使用第一个文本模型:', firstModel.id)
        selectedModel.value = firstModel.id
      }
    } else {
      modelOptions.value = []
      selectedModel.value = null
    }
  } catch (error) {
    console.error('加载模型配置失败:', error)
    modelOptions.value = []
    selectedModel.value = null
  }
}

// 解析文件
const parseFile = async (file: UploadedFile) => {
  // 检查是否选择了模型
  if (!selectedModel.value) {
    ElMessage.warning('请先选择模型')
    return
  }

  const fileIndex = tableData.value.findIndex(f => f.file_id === file.file_id);
  if (fileIndex === -1) {
    console.error('未在 tableData 中找到文件:', file);
    ElMessage.error('内部错误：找不到文件记录');
    return;
  }

  try {
    // 确认是否解析
    await ElMessageBox.confirm(
      `确定要解析文件 "${file.file_name}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 立即更新状态为解析中，让 UI 反映
    tableData.value[fileIndex] = {
      ...tableData.value[fileIndex],
      parsing: true
    };

    // 记录解析开始时间
    parseStartTimes.value.set(file.file_id, Date.now())

    // 更新解析中的文件列表
    if (!parsingFiles.value.includes(file.file_id)) {
      parsingFiles.value.push(file.file_id)
    }
    // 保存状态
    saveParsingStatus()

    // 解析参数 - 添加模型ID
    const parseParams = {
      file_id: file.file_id,
      project_id: (projectStore.project_info as ProjectInfo)?.id || 0,
      user_id: (mainStore.user_info as UserInfo)?.id || 0,
      openai_config_id: selectedModel.value // 添加选中的模型ID
    }

    // 调用解析接口
    const response = await parserOpenaiFile(parseParams)

    // 检查响应码和数据结构
    if (response && response.data && response.data.code === 2000 && response.data.data && response.data.data.task_ids) {
      ElMessage.success('解析任务已提交')
      // 更新文件状态和 taskIds
      tableData.value[fileIndex] = {
        ...tableData.value[fileIndex],
        taskIds: response.data.data.task_ids
      };
      currentFileId.value = file.file_id

      // 如果有任务ID，立即检查状态
      if (response.data.data.task_ids.length > 0 && fileIndex !== -1) {
        // 传递更新后的文件对象给 checkParseStatus
        checkParseStatus(tableData.value[fileIndex])
      }

      // 设置超时检查，10分钟后强制解锁
      setTimeout(() => {
        const currentFileIndex = tableData.value.findIndex(f => f.file_id === file.file_id);
        if (currentFileIndex !== -1 && tableData.value[currentFileIndex].parsing) {
          // console.log(`文件 ${file.file_name} 解析超时（10分钟），强制解锁`)
          tableData.value[currentFileIndex] = {
            ...tableData.value[currentFileIndex],
            parsing: false
          }
          // 从解析中的文件列表中移除
          parsingFiles.value = parsingFiles.value.filter(id => id !== file.file_id)
          // 保存状态
          saveParsingStatus()
          // 移除开始时间记录
          parseStartTimes.value.delete(file.file_id)
          // 显示提示
          ElMessage.warning(`文件 "${file.file_name}" 解析时间超过10分钟，已自动解锁`)
        }
      }, 600000) // 10分钟超时
    } else {
      // 解析失败或响应格式不符
      ElMessage.error(response?.data?.msg || '解析任务提交失败或响应无效')
      // 恢复 parsing 状态
      tableData.value[fileIndex] = {
        ...tableData.value[fileIndex],
        parsing: false
      };
      // 从解析中的文件列表中移除
      parsingFiles.value = parsingFiles.value.filter(id => id !== file.file_id)
      // 保存状态
      saveParsingStatus()
      // 移除开始时间记录
      parseStartTimes.value.delete(file.file_id)
    }
  } catch (error) {
    // 处理取消操作或网络错误等
    if (error === 'cancel') {
      // 如果取消，恢复 parsing 状态
      tableData.value[fileIndex] = {
        ...tableData.value[fileIndex],
        parsing: false
      };
      // 从解析中的文件列表中移除
      parsingFiles.value = parsingFiles.value.filter(id => id !== file.file_id)
      // 保存状态
      saveParsingStatus()
      // 移除开始时间记录
      parseStartTimes.value.delete(file.file_id)
      return
    }
    console.error('解析失败:', error)
    ElMessage.error('解析失败，请重试')
    // 出错时恢复 parsing 状态
    tableData.value[fileIndex] = {
      ...tableData.value[fileIndex],
      parsing: false
    };
    // 从解析中的文件列表中移除
    parsingFiles.value = parsingFiles.value.filter(id => id !== file.file_id)
    // 保存状态
    saveParsingStatus()
    // 移除开始时间记录
    parseStartTimes.value.delete(file.file_id)
  }
}

// 检查解析状态
const checkParseStatus = async (file: UploadedFile) => {
  try {
    refreshingStatus.value = true

    // 调用获取状态接口
    const response = await getParserFileStatus(file.file_id)

    // 处理嵌套的 data 结构
    if (response && response.data && response.data.code === 2000 && response.data.data) {
      // 显示解析结果
      currentParseResult.value = response.data.data
      currentFileId.value = file.file_id
      drawerVisible.value = true

      // 检查是否所有任务都已完成
      const allCompleted = response.data.data.every((task: ParseTask) =>
        task.status === 'SUCCESS' || task.status === 'FAILURE' || task.status === 'PARTIAL_SUCCESS'
      )

      // 找到文件索引
      const fileIndex = tableData.value.findIndex(f => f.file_id === file.file_id);
      if (fileIndex !== -1) {
        if (allCompleted) {
          // 更新文件状态为已完成
          tableData.value[fileIndex] = {
            ...tableData.value[fileIndex],
            parsing: false
          };
          // 从解析中的文件列表中移除
          parsingFiles.value = parsingFiles.value.filter(id => id !== file.file_id)
          // 保存状态
          saveParsingStatus()
        } else {
          // 如果有任务未完成，10秒后自动刷新状态
          setTimeout(() => {
            if (drawerVisible.value && currentFileId.value === file.file_id) {
              refreshParseStatus()
            }
          }, 10000)
        }
      }
    } else {
      ElMessage.error(response?.data?.msg || '获取解析状态失败')
    }
  } catch (error) {
    console.error('获取解析状态失败:', error)
    ElMessage.error('获取解析状态失败，请重试')
  } finally {
    refreshingStatus.value = false
  }
}

// 刷新解析状态
const refreshParseStatus = async () => {
  if (!currentFileId.value) return

  try {
    refreshingStatus.value = true

    // 调用获取状态接口
    const response = await getParserFileStatus(currentFileId.value)

    // 处理嵌套的 data 结构
    if (response && response.data && response.data.code === 2000 && response.data.data) {
      // 更新解析结果
      currentParseResult.value = response.data.data

      // 检查是否所有任务都已完成
      const allCompleted = response.data.data.every((task: ParseTask) =>
        task.status === 'SUCCESS' || task.status === 'FAILURE' || task.status === 'PARTIAL_SUCCESS'
      )

      // 找到关联的文件
      const file = tableData.value.find(f => f.file_id === currentFileId.value)

      if (file) {
        const fileIndex = tableData.value.findIndex(f => f.file_id === currentFileId.value);

        if (allCompleted) {
          // 更新文件状态为已完成
          if (fileIndex !== -1) {
            tableData.value[fileIndex] = {
              ...tableData.value[fileIndex],
              parsing: false
            };
            // 从解析中的文件列表中移除
            parsingFiles.value = parsingFiles.value.filter(id => id !== file.file_id)
            // 保存状态
            saveParsingStatus()
          }
        } else {
          // 如果有任务未完成，10秒后自动刷新状态
          setTimeout(() => {
            if (drawerVisible.value && currentFileId.value === file.file_id) {
              refreshParseStatus()
            }
          }, 10000)
        }
      }
    } else {
      ElMessage.error(response?.data?.msg || '获取解析状态失败')
    }
  } catch (error) {
    console.error('刷新解析状态失败:', error)
    ElMessage.error('刷新解析状态失败，请重试')
  } finally {
    refreshingStatus.value = false
  }
}

// 获取状态对应的类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'SUCCESS':
      return 'success'
    case 'PARTIAL_SUCCESS':
      return 'warning'
    case 'FAILURE':
      return 'danger'
    case 'PROCESSING':
      return 'warning'
    case 'PENDING':
      return 'info'
    default:
      return 'info'
  }
}

// 获取指定状态的任务数量
const getStatusCount = (status: string) => {
  if (!currentParseResult.value) return 0
  return currentParseResult.value.filter(task => task.status === status).length
}

// 处理分页大小改变
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  fetchFileList()
}

// 处理页码改变
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchFileList()
}

// 获取文件列表
const fetchFileList = async () => {
  try {
    tableLoading.value = true
    const projectId = (projectStore.project_info as ProjectInfo)?.id || 0

    // 声明response变量在if语句之前，以便后续可以使用
    let response: any

    // 如果有特定文件ID，则传入该ID；否则不传ID参数
    // if (currentFileId.value) {
    //   response = await getAllSmartFile(pageSize.value, currentPage.value, projectId, currentFileId.value)
    // } else {
    //   // 不传入ID参数，让后端API返回所有文件
    //   response = await getAllSmartFile(pageSize.value, currentPage.value, projectId)
    // }
    response = await getAllSmartFile(pageSize.value, currentPage.value, projectId)

    if (response && response.data && response.data.results && response.data.results.code === 2000) {
      // 按照实际返回的嵌套结构获取数据列表
      tableData.value = response.data.results.data.map((item: any) => ({
        ...item,
        file_id: item.id, // 确保字段名一致
        parsing: false // 初始化解析状态
      }))

      // 分页数据在外层
      total.value = response.data.count || 0

      // 恢复解析状态
      restoreParsingStatus()

    } else {
      ElMessage.error(response?.data?.results?.msg || '获取文件列表失败')
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败，请重试')
  } finally {
    tableLoading.value = false
  }
}

// 处理删除文件
const handleDelete = async (file: UploadedFile) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.file_name}" 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await deleteSmartFile(file.file_id)

    if (response && response.data && response.data.code === 2000) {
      ElMessage.success('文件删除成功')
      // 刷新文件列表
      fetchFileList()
    } else {
      ElMessage.error(response?.data?.msg || '删除文件失败')
    }
  } catch (error) {
    if (error === 'cancel') return
    console.error('删除文件失败:', error)
    ElMessage.error('删除文件失败，请重试')
  }
}

// 刷新文件列表
const refreshFileList = () => {
  fetchFileList()
}

// 页面加载时获取文件列表
onMounted(() => {
  fetchFileList()
  loadModelOptions() // 在组件挂载时加载模型选项

  // 每30秒检查一次解析中的文件状态
  const timer = setInterval(() => {
    if (parsingFiles.value.length > 0) {
      checkParsingFiles()
    }
  }, 30000)

  // 在组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(timer)
  })
})

// 根据用户ID获取用户名
const getUsernameById = (userId: number | string) => {
  // 将userId转换为字符串进行比较
  const id = userId.toString()

  // 如果是当前用户
  if (userInfo.value) {
    // 使用类型断言访问属性
    const currentUser = userInfo.value as any
    if (currentUser.id && currentUser.id.toString() === id) {
      return currentUser.username || '未知用户'
    }
  }

  // 如果当前没有用户列表或无法访问，直接返回ID
  return `用户${id}`
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  // 保留两位小数，并移除末尾的0和小数点
  return (bytes / Math.pow(1024, i)).toFixed(2).replace(/\.?0+$/, '') + ' ' + units[i];
}
</script>

<style scoped>
/* 添加提示信息样式 */
.tip-container {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-left: 4px solid #979797;
}

.tip-content {
  display: flex;
  align-items: center;
  color: #979797;
  font-size: 12px;
  line-height: 1.5;
}

.tip-icon {
  margin-right: 8px;
  font-size: 16px;
}

.button-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 16px;
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 30px 0;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  margin: 10px 0;
  background-color: #fafafa;
}

.upload-component {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

/* 抽屉相关样式优化 */
.parse-detail-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.drawer-header {
  width: 100%;
}

.drawer-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.parse-result-container {
  padding: 16px 20px;
}

.status-summary {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.status-summary h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.status-metrics {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.metric-item {
  flex: 1;
  min-width: 120px;
  background-color: white;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.metric-icon {
  font-size: 18px;
  margin-left: 4px;
}

.success-icon {
  color: #67c23a;
}

.processing-icon {
  color: #e6a23c;
}

.failure-icon {
  color: #f56c6c;
}

.refresh-status {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}

.refresh-button {
  font-size: 13px;
}

.status-divider {
  margin: 16px 0;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.task-card {
  transition: all 0.3s;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.task-success {
  border-left: 4px solid #67c23a;
}

.task-processing {
  border-left: 4px solid #e6a23c;
}

.task-failure {
  border-left: 4px solid #f56c6c;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.task-id {
  font-weight: 600;
  font-size: 14px;
  color: #606266;
}

.task-status {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-tag {
  font-size: 12px;
}

.task-info {
  padding: 16px 4px;
}

.info-title {
  margin-top: 0;
  margin-bottom: 12px;
  padding-bottom: 8px;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px dashed #ebeef5;
}

.api-details {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.info-item {
  line-height: 1.8;
  margin-bottom: 8px;
  word-break: break-word;
}

.info-item strong {
  font-weight: 600;
  color: #606266;
  margin-right: 4px;
}

.info-divider {
  margin: 20px 0;
}

.test-cases-collapse {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.test-case-item :deep(.el-collapse-item__header) {
  font-weight: 600;
  color: #606266;
}

.test-case-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.sub-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin: 16px 0 8px;
}

.headers-section,
.request-body,
.assertions-section {
  margin-top: 16px;
}

.headers-table {
  margin-top: 8px;
}

.body-content {
  background-color: #f0f0f0;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  max-height: 300px;
}

.assertions-table {
  margin-top: 8px;
}

.error-message {
  margin-top: 24px;
}

.error-title {
  color: #f56c6c;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.error-alert {
  font-size: 13px;
}

.empty-result {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

/* 添加模型选择下拉框样式 */
.compact-select {
  width: 200px;
}
</style>