<template>
  <div class="tree-container">
    <div class="search-container">
      <el-input 
        v-model="filterText" 
        placeholder="输入名称查询..." 
        class="search-input"
        clearable
      >
        <template #prefix>
          <el-icon class="search-icon"><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <el-tree 
      v-loading="tree_loading" 
      ref="treeRef" 
      draggable 
      class="filter-tree" 
      :data="data" 
      :props="defaultProps" 
      default-expand-all
      :filter-node-method="filterNode" 
      :highlight-current="true" 
      :expand-on-click-node="false" 
      @node-click="showApi"
    >
      <template #default="{ node, data }">
        <div class="tree-node">
          <span class="tree-icon">
            <el-icon v-if="data.type === '1'" class="folder-icon"><Folder /></el-icon>
            <img v-else-if="!data.if_stream && data.type === '0'" :src="API" class="api-icon" alt="API" />
            <img v-else-if="data.if_stream && data.type === '0'" :src="STREAM" class="stream-icon" alt="Stream" />
            <img v-else :src="CASE" class="case-icon" alt="Case" />
          </span>
          <span class="node-label">{{ node.label }}</span>
          <div class="dropdown-container">
            <el-dropdown trigger="click">
              <span class="dropdown-trigger">
                <el-icon><More /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="data.type === '1' && data.label !== 'AI生成'" @click="dialogFolderAdd(data.id)">
                    <el-icon class="dropdown-icon"><FolderAdd /></el-icon> 新建目录
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '1'" @click="dialogApiAdd(data.id)">
                    <el-icon class="dropdown-icon"><DocumentAdd /></el-icon> 新建接口
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '0'" @click="dialogApiCaseAdd(data.id, data.if_stream)">
                    <el-icon class="dropdown-icon"><Plus /></el-icon> 新建用例
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '0'" @click="dialogRunCase(data.id)">
                    <el-icon class="dropdown-icon"><VideoPlay /></el-icon> 执行用例
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '1'" @click="dialogApiRun(data.id)">
                    <el-icon class="dropdown-icon"><VideoPlay /></el-icon> 执行API
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="data.label === '顶级目录' || data.label === 'AI生成'" @click="dialogEdit(data.id, data.label, data.if_stream)">
                    <el-icon class="dropdown-icon"><Edit /></el-icon> 编辑
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="data.label === '顶级目录' || data.label === 'AI生成'" @click="dialogDel(data.id)">
                    <el-icon class="dropdown-icon"><Delete /></el-icon> 删除
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '2'" @click="dialogCopyCase(data.id)">
                    <el-icon class="dropdown-icon"><CopyDocument /></el-icon> 复制
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '0'" @click="dialogMoveCase(data.id)">
                    <el-icon class="dropdown-icon"><Position /></el-icon> 移动
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
  
  <tree_add_dialog v-model="dialogVisible" :folderId="folderId" @updateTreeData="updateTreeData"></tree_add_dialog>
  <tree_add_api v-model="dialogApiVisible" :folderId="folderId" @updateTreeData="updateTreeData"></tree_add_api>
  <tree_add_api_case :ifStream="ifStream" v-model="dialogApiCaseVisible" :folderId="folderId" @updateTreeData="updateTreeData">
  </tree_add_api_case>
  <tree_edit_dialog v-model="dialogVisibleEdit" :ifStream="ifStream" :folderId="folderId" :folderName="folderName"
    @updateTreeData="updateTreeData" @nodeNameUpdated="handleNodeNameUpdated"></tree_edit_dialog>
  <tree_del_dialog v-model="dialogVisibleDel" :folderId="folderId" @updateTreeData="updateTreeData"> </tree_del_dialog>
  <run_api :showSaveAsButton="true" v-model="dialogApiRunVisible" :folderId="folderId" :apiList="apiList" :envDropList="envDropList" :projectId="props.projectId"></run_api>
  
  <!-- 添加移动对话框 -->
  <el-dialog v-model="moveDialogVisible" title="移动接口" width="40%" destroy-on-close>
    <div class="folder-select-content">
      <p class="select-tip">请选择目标目录：</p>
      <el-tree
        v-loading="moveTreeLoading"
        ref="moveTreeRef"
        :data="moveTreeData"
        :props="defaultProps"
        node-key="id"
        default-expand-all
        highlight-current
        :filter-node-method="folderFilterNode"
        @node-click="handleFolderNodeClick"
        class="move-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <span class="tree-icon">
              <el-icon v-if="data.type === '1'" class="folder-icon"><Folder /></el-icon>
            </span>
            <span class="node-label">{{ node.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="moveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmMoveCase" :disabled="!selectedFolderId">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage, ElTree } from 'element-plus'
import { getCaseTree } from '@/apis/api_test/case_test/get_case_tree'
import { Folder, Plus, Edit, Delete, Search, DocumentAdd, FolderAdd, VideoPlay, CopyDocument, Position, More } from '@element-plus/icons-vue'
import tree_add_dialog from './tree_add_dialog.vue'
import tree_edit_dialog from './tree_edit_dialog.vue'
import tree_del_dialog from './tree_del_dialog.vue'
import tree_add_api from './tree_add_api.vue'
import tree_add_api_case from './tree_add_api_case.vue'
import run_api from './run_api.vue'
import { getCaseApiSubData } from '@/apis/api_test/case_test/get_case_api_sub_data'
import { getCaseResponse } from '@/apis/api_test/case_test/get_case_response'
import { getCaseTestResult } from '@/apis/api_test/case_test/get_case_test_result'
import { postCopyChildCase } from '@/apis/api_test/case_test/post_copy_child_case'
import { getCaseOverview } from '@/apis/api_test/case_test/get_case_folder_overview'
import { postSceneApiList } from '@/apis/api_test/case_test/scene_test/post_scene_api_list'
import { getCaseEnvDropList } from '@/apis/api_test/case_test/get_env_list'
import { postMoveCase } from '@/apis/api_test/case_test/post_move_case'
import { postRunChildCaseApiBatch } from '@/apis/api_test/case_test/post_run_child_case_api_batch'
import API from '@/assets/icon/API.png'
import STREAM from '@/assets/icon/STREAM.png'
import CASE from '@/assets/icon/CASE.png'
import { useCaseApiStore } from "@/stores/case_api";


const dialogVisible = ref(false)
const folderId = ref(0)
const folderName = ref('')
const ifStream = ref(false) // 确保这是一个响应式引用
const dialogApiVisible = ref(false)
const dialogApiCaseVisible = ref(false)
const dialogVisibleEdit = ref(false)
const dialogVisibleDel = ref(false)
const dialogApiRunVisible = ref(false)
// 移动对话框相关
const moveDialogVisible = ref(false)
const moveTreeData = ref<any[]>([])
const selectedFolderId = ref<number | null>(null)
const moveTreeRef = ref<InstanceType<typeof ElTree>>()
const caseIdToMove = ref<number>(0)
const moveTreeLoading = ref(false)

// 添加执行用例的loading状态
const runCaseLoading = ref(false)

const tree_loading = ref(false)

const emit = defineEmits(['ifShowApi', 'getFolderOrApi', 'nodeNameUpdated', 'getApiSubData', 'getResponseData', 'getTestResultData', 'getCaseOverviewRichText', 'updateRunCaseLoading', 'updateLoadingState']) // 定义emit函数
const showApiPage = ref('')
//节点id
const showApiId = ref(0)

// 添加请求状态管理
const currentRequestId = ref(0)
const isLoading = ref(false)

const apiSubData = ref({})
const getCaseSubData = async (requestId: number, apiId: string, apiType: string) => {
  try {
    const res = await getCaseApiSubData(apiId, apiType)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      apiSubData.value = res.data.data
      emit('getApiSubData', apiSubData.value)
    } else if (res && res.data.data === '接口不存在') {
      apiSubData.value = {"content_type": "none", "body": []}
      emit('getApiSubData', apiSubData.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取接口数据失败:', error)
  }
}

const responseData = ref({})
const getCaseResponseData = async (requestId: number, apiId: number, apiType: string) => {
  try {
    const res = await getCaseResponse(apiId, apiType)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      responseData.value = res.data.data
      emit('getResponseData', responseData.value)
      // console.log("responseData", responseData.value)
    } else {
      responseData.value = {}
      emit('getResponseData', responseData.value)
      // console.log("responseData_not_res", responseData.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取响应数据失败:', error)
  }
}

const testResultData = ref([])
const getCaseTestResultData = async (requestId: number, apiId: number, apiType: string) => {
  try {
    const res = await getCaseTestResult(apiId, apiType)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      testResultData.value = res.data.data
      emit('getTestResultData', testResultData.value)
    } else {
      testResultData.value = []
      emit('getTestResultData', testResultData.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取测试结果失败:', error)
  }
}

const caseOverviewRichText = ref('')
const getCaseOverviewData = async (requestId: number, apiId: string) => {
  try {
    const res = await getCaseOverview(apiId)
    // 检查是否是最新的请求
    if (requestId !== currentRequestId.value) return
    
    if (res && res.data.code === 2000) {
      caseOverviewRichText.value = res.data.data.rich_text
      emit('getCaseOverviewRichText', caseOverviewRichText.value)
    } else {
      caseOverviewRichText.value = ''
      emit('getCaseOverviewRichText', caseOverviewRichText.value)
    }
  } catch (error) {
    // 如果不是最新请求，忽略错误
    if (requestId !== currentRequestId.value) return
    // console.error('获取案例概览数据失败:', error)
  }
}

const case_api_store = useCaseApiStore()
const api_type = ref('')
const showApi = async (data: any) => {
  // 生成新的请求ID，用于标识这次点击
     const requestId = Date.now()
   currentRequestId.value = requestId
   isLoading.value = true
   emit('updateLoadingState', true)

  try {
    // console.log("tree_data", data)
    case_api_store.setCaseTreeData(data)
    
    if (data.type === '0' || data.type === '2') {
      showApiPage.value = '0'
      showApiId.value = data.id
      api_type.value = data.type
      
      // 先发送基础信息
      emit('ifShowApi', showApiPage.value, showApiId.value)
      emit('getFolderOrApi', data)
      
      // 清空旧数据，避免显示上次的内容
      emit('getApiSubData', {})
      emit('getResponseData', {})
      emit('getTestResultData', [])
      
      // 并行发起所有请求，但使用requestId确保数据一致性
      await Promise.allSettled([
        getCaseSubData(requestId, String(data.id), data.type),
        getCaseResponseData(requestId, data.id, data.type),
        getCaseTestResultData(requestId, data.id, data.type)
      ])
    } else {
      showApiPage.value = '1'
      showApiId.value = data.id
      
      // 先发送基础信息
      emit('ifShowApi', showApiPage.value, showApiId.value)
      emit('getFolderOrApi', data)
      
      // 清空旧数据
      emit('getCaseOverviewRichText', '')
      
      // 获取案例概览数据
      await getCaseOverviewData(requestId, String(data.id))
    }
    // console.log(showApiPage.value)
  } catch (error) {
    // console.error('显示API数据失败:', error)
  } finally {
         // 只有当前请求完成时才清除loading状态
     if (requestId === currentRequestId.value) {
       isLoading.value = false
       emit('updateLoadingState', false)
     }
  }
}

const updateTreeData = (newTree: any, type = '') => {
  if (newTree) {
    data.value = renameKeys([newTree])
    if (type === 'del') {
      // 如果节点删除，就设置默认页面
      emit('ifShowApi', '999')
    }
  }
}

const handleNodeNameUpdated = (newName: string) => {
  emit('nodeNameUpdated', newName); // 将新的节点名称向上传递
}

const dialogFolderAdd = (id: number) => {
  folderId.value = id
  dialogVisible.value = true
}

const dialogApiAdd = (id: number) => {
  folderId.value = id
  dialogApiVisible.value = true
}

const runApi = (data: any) => {
  // console.log("当前节点数据:", data);
  const result: any = [];
  if (Array.isArray(data)) {
    data.forEach(item => {
      result.push(...runApi(item));  // 如果是数组，递归处理每个元素
    });
  } else {
    if (data.type === '0') {
      result.push(data);
    }
    if (data.children) {
      data.children.forEach((child: any) => {
        result.push(...runApi(child));  // 递归处理子节点
      });
    }
  }
  return result;
}

const apiList = ref([])
const dialogApiRun = async (id: number) => {
  folderId.value = id
  dialogApiRunVisible.value = true
  // console.log("treedata", data.value)
  // 树形结构的数据{}
  // console.log("case_api_store.case_tree_data", case_api_store.case_tree_data)
  let run_api_list = runApi(case_api_store.case_tree_data)
  // console.log("run_api_list", run_api_list)
  const res = await postSceneApiList(run_api_list)
  if (res && res.data.code === 2000) {
    apiList.value = res.data.data
  } else {
    apiList.value = []
  }
}

const dialogEdit = (id:number, name:string, if_stream:boolean) => {
  folderId.value = id
  folderName.value = name
  ifStream.value = if_stream // 确保if_stream是一个响应式引用
  dialogVisibleEdit.value = true
}

const dialogApiCaseAdd = (id: number, if_stream: boolean) => {
  folderId.value = id
  ifStream.value = if_stream // 确保if_stream是一个响应式引用
  dialogApiCaseVisible.value = true
}

// 添加执行用例函数
const dialogRunCase = async (id: number) => {
  emit('updateRunCaseLoading', true)
  const formData = {
    parent_folder_id: id,
    project: props.projectId
  }
  
  try {
    const res = await postRunChildCaseApiBatch(formData)
    if (res && res.data.code === 2000) {
      ElMessage.success('用例执行成功！请查看每个用例的响应结果')
      // 刷新当前显示的数据
      // if (showApiId.value) {
      //   await getCaseResponseData()
      //   await getCaseTestResultData()
      // }
      emit('updateRunCaseLoading', false)
    }
  } catch (error) {
    // console.error('执行用例失败:', error)
  } finally {
    emit('updateRunCaseLoading', false)
  }
}

const dialogDel = (id: number) => {
  folderId.value = id
  dialogVisibleDel.value = true
}


const dialogCopyCase = async (id: number) => {
  let form = {
    "case_id": id
  }
  const res = await postCopyChildCase(form)
  if (res && res.data.code === 2000) {
    ElMessage.success(res.data.msg)
    await getTree()
  }
}

// 添加移动用例函数
const dialogMoveCase = async (id: number) => {
  caseIdToMove.value = id
  selectedFolderId.value = null
  moveDialogVisible.value = true
  
  // 显示对话框后再加载数据，避免等待时间
  await loadMoveTreeData()
}

// 加载移动对话框的目录树
const loadMoveTreeData = async () => {
  if (props.projectId) {
    moveTreeLoading.value = true
    try {
      const res = await getCaseTree(props.projectId)
      if (res) {
        let treeData = res.data.data
        
        // 将原始数据转换成树结构并重命名键
        const renamedData = renameKeys([treeData])
        // 处理目录树，保留所有目录节点，包括特殊目录
        moveTreeData.value = processDirectoriesTree(renamedData)
      }
    } catch (error) {
      // console.error('加载目录树失败:', error)
      ElMessage.error('加载目录树失败')
    } finally {
      moveTreeLoading.value = false
    }
  }
}

// 处理目录树，保留所有目录的层级结构
const processDirectoriesTree = (data: any[]): any[] => {
  // 递归处理树节点
  const processNode = (node: any): any | null => {
    // 如果节点为空，返回null
    if (!node) return null;
    
    // 如果不是目录类型，不保留
    if (node.type !== '1') {
      return null;
    }
    
    // 创建新节点，避免修改原节点
    const newNode = { ...node };
    
    // 处理子节点
    if (node.children && node.children.length) {
      newNode.children = [];
      // 遍历子节点
      for (const child of node.children) {
        const processedChild = processNode(child);
        if (processedChild) {
          // 如果处理结果是数组，展平添加
          if (Array.isArray(processedChild)) {
            newNode.children.push(...processedChild);
          } else {
            newNode.children.push(processedChild);
          }
        }
      }
      // 如果没有子节点，设置为null
      if (newNode.children.length === 0) {
        newNode.children = null;
      }
    }
    return newNode;
  };
  
  // 处理根节点
  const result = [];
  for (const node of data) {
    const processed = processNode(node);
    if (processed) {
      // 如果返回结果是数组，展平添加到结果中
      if (Array.isArray(processed)) {
        result.push(...processed);
      } else {
        result.push(processed);
      }
    }
  }
  
  return result;
}

// 处理目录节点点击
const handleFolderNodeClick = (data: any) => {
  // 判断是否是不可选择的特殊目录
  if (data.label !== '顶级目录' && data.label !== 'AI生成') {
    selectedFolderId.value = data.id;
  } else {
    selectedFolderId.value = null;
    ElMessage.warning(`不能选择"${data.label}"作为目标目录`);
  }
}

// 过滤目录节点 - 只显示目录类型
const folderFilterNode = (value: string, data: any) => {
  if (value) {
    return data.label.includes(value)
  }
  return true
}

// 确认移动用例
const confirmMoveCase = async () => {
  if (!selectedFolderId.value) {
    ElMessage.warning('请选择目标目录')
    return
  }
  
  const form = {
    case_id: caseIdToMove.value,
    target_folder_id: selectedFolderId.value
  }
  
  try {
    const res = await postMoveCase(form)
    if (res && res.data.code === 2000) {
      ElMessage.success(res.data.msg)
      moveDialogVisible.value = false
      await getTree() // 重新加载树
    }
  } catch (error) {
    // console.error('移动失败:', error)
  }
}

interface Tree {
  [key: string]: any
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

const props = defineProps({
  projectId: Number
})

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}

interface TreeNode {
  name?: string;
  children?: TreeNode[] | null;
  [key: string]: any;
}

function renameKeys(data: TreeNode[]): TreeNode[] {
  return data.map(({ name: label, children, ...rest }) => ({
    label,
    ...rest,
    children: children ? renameKeys(children) : null,
  }));
}

// 找第一个
const findFirstApiNode = (nodes: any) => {
  for (const node of nodes) {
    if (node.type === '0') {
      return node;
    }
    if (node.children) {
      const found: any = findFirstApiNode(node.children);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

const data = ref<TreeNode[]>([])

const getTree = async () => {
  if (props.projectId) {
    tree_loading.value = true
    const res = await getCaseTree(props.projectId)
    if (res) {
      let treeData = res.data.data
      data.value = renameKeys([treeData])

      const firstApiNode = findFirstApiNode(data.value);
      if (firstApiNode) {
        showApi(firstApiNode);
      }
    }
  }
  tree_loading.value = false
}

getTree()


interface envDropList {
  id: number
  name: string
}

const envDropList = ref<envDropList[]>([])
const getEnvList = async () => {
  const res = await getCaseEnvDropList(props.projectId as number)
  if (res && res.data.code === 2000) {
    envDropList.value = res.data.data
  }
}

getEnvList()


</script>

<style scoped>
.tree-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-container {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  height: 32px;
}

.search-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-icon {
  color: #909399;
  font-size: 16px;
  margin-right: 4px;
}

.filter-tree, .move-tree {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  scrollbar-width: thin;
  scrollbar-color: #C1C1C1 #f0f0f0;
}

.filter-tree::-webkit-scrollbar, .move-tree::-webkit-scrollbar {
  width: 6px;
}

.filter-tree::-webkit-scrollbar-track, .move-tree::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.filter-tree::-webkit-scrollbar-thumb, .move-tree::-webkit-scrollbar-thumb {
  background-color: #C1C1C1;
  border-radius: 4px;
}

.filter-tree::-webkit-scrollbar-thumb:hover, .move-tree::-webkit-scrollbar-thumb:hover {
  background: #909090;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px 8px;
  position: relative;
  border-radius: 6px;
  transition: all 0.3s;
  box-sizing: border-box;
  height: 38px;
}

.tree-node:hover {
  background-color: #f0f7ff;
}

.tree-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.folder-icon {
  color: #e6a23c;
  font-size: 18px;
}

.api-icon, .stream-icon, .case-icon {
  width: 18px;
  height: 18px;
}

.node-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.dropdown-container {
  margin-left: auto;
  flex-shrink: 0;
  padding: 4px;
  position: absolute;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.dropdown-trigger {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  transition: all 0.2s;
}

.dropdown-trigger:hover {
  background-color: #e7f1fd;
  color: #409eff;
}

.tree-node:hover .dropdown-container {
  opacity: 1;
}

.dropdown-icon {
  margin-right: 5px;
  font-size: 16px;
}

:deep(.el-tree-node) {
  padding: 4px 0;
}

:deep(.el-tree-node__content) {
  height: 38px;
  padding: 0 !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  border-radius: 6px;
}

/* 增强树形结构的层级感 */
:deep(.el-tree-node__children) {
  padding-left: 16px;
}

:deep(.el-tree-node__children .el-tree-node__content) {
  position: relative;
}

/* 增强接口和用例节点区分 */
.tree-node .tree-icon .api-icon,
.tree-node .tree-icon .stream-icon,
.tree-node .tree-icon .case-icon {
  opacity: 0.85;
  transition: opacity 0.3s;
}

.tree-node:hover .tree-icon .api-icon,
.tree-node:hover .tree-icon .stream-icon,
.tree-node:hover .tree-icon .case-icon {
  opacity: 1;
}

/* 改进展开图标样式 */
:deep(.el-tree-node__expand-icon) {
  font-size: 14px;
  color: #909399;
  transition: all 0.2s;
  margin-right: 5px;
  border-radius: 2px;
  padding: 2px;
}

:deep(.el-tree-node__expand-icon:hover) {
  background-color: #f0f0f0;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree-node__content:hover) {
  background-color: transparent;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 8px 16px;
  transition: all 0.3s;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f0f7ff;
}

:deep(.el-dropdown-menu__item.is-disabled) {
  opacity: 0.6;
}

:deep(.el-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 6px 0;
}

:deep(.el-tree) {
  --el-tree-node-hover-bg-color: transparent;
}

.folder-select-content {
  padding: 12px;
}

.select-tip {
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

/* 媒体查询适配小屏 */
@media (max-width: 768px) {
  .tree-container {
    padding: 12px;
  }
  
  .node-label {
    max-width: 70%;
    font-size: 13px;
  }
  
  .tree-icon {
    margin-right: 6px;
    width: 20px;
    height: 20px;
  }
  
  .folder-icon {
    font-size: 16px;
  }
  
  .api-icon, .stream-icon, .case-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
