<template>
  <div style="border: 1px solid #ccc">
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" mode="simple" />
    <Editor style="height: 400px; overflow-y: hidden;" v-model="valueHtml" :defaultConfig="editorConfig" mode="simple"
      @onCreated="handleCreated" />
  </div>
  <div>
    <el-button v-if="!if_read_only" style="margin-top: 10px" @click="handleSave">保存</el-button>
    <el-button v-else style="margin-top: 10px" @click="handleDisable">编辑</el-button>
  </div>
</template>
<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { onBeforeUnmount, ref, shallowRef, onMounted, watchEffect } from 'vue'
import { ElMessage } from 'element-plus'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IToolbarConfig } from '@wangeditor/editor'
import { postCaseFolderOverview } from '@/apis/api_test/case_test/post_case_folder_overview'
import { getCaseOverview } from '@/apis/api_test/case_test/get_case_folder_overview'
import { useCaseApiStore } from "@/stores/case_api";

const props = defineProps({
  valueHtml: {
    type: String,
    default: ''
  }
})
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

// 内容 HTML
const valueHtml = ref('<p>hello</p>')

onMounted(() => {
  // 模拟 ajax 异步获取内容
})

watchEffect(() => {
  valueHtml.value = props.valueHtml
})

const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: [//隐藏的toolbarKey,默认为空
    "group-image",
    'insertVideo',
    'fullScreen'
  ]
}

const editorConfig = { placeholder: '请输入内容...' }

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = (editor: any) => {
  editorRef.value = editor // 记录 editor 实例，重要！
  editorRef.value.disable()
}

const if_read_only = ref(true)
const handleDisable = () => {
  editorRef.value.enable()
  if_read_only.value = false
}

const store = useCaseApiStore()
const handleSave = async () => {

  const data = {
    folder: store.case_tree_data.id,
    rich_text: valueHtml.value
  }

  const res = await postCaseFolderOverview(data)
  if (res && res.data.code === 2000) {
    ElMessage.success(res.data.msg)
    const new_res = await getCaseOverview(store.case_tree_data.id)
    if (new_res && new_res.data.code === 2000) {
      valueHtml.value = new_res.data.data.rich_text
    }
  } else {
    ElMessage.error(res.data.msg)
  }
  editorRef.value.disable()
  if_read_only.value = true
}



</script>
<style>
/*编辑器 */
.w-e-text-container [data-slate-editor] h1,
.w-e-text-container [data-slate-editor] h2,
.w-e-text-container [data-slate-editor] h3,
.w-e-text-container [data-slate-editor] h4,
.w-e-text-container [data-slate-editor] h5 {
  margin: 20px 0;
  font-weight: bold;
}

.w-e-text-container [data-slate-editor] h1 {
  font-size: 26px;
}

.w-e-text-container [data-slate-editor] h2 {
  font-size: 22px;
}

.w-e-text-container [data-slate-editor] h3 {
  font-size: 20px;
}

.w-e-text-container [data-slate-editor] h4 {
  font-size: 18px;
}

.w-e-text-container [data-slate-editor] h5 {
  font-size: 16px;
}

.w-e-text-container [data-slate-editor] strong {
  font-weight: bold;
}

.w-e-text-container [data-slate-editor] u {
  text-decoration: underline;
}

.w-e-text-container [data-slate-editor] s {
  text-decoration: line-through;
}

.w-e-text-container [data-slate-editor] em {
  font-style: italic;
}
</style>
