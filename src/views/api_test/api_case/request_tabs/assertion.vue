<template>
    <div class="assertion-container">
        <!-- 自动生成断言按钮区域 -->
        <div class="auto-generate-section">
            <div class="auto-generate-controls">
                <div class="button-group">
                    <el-button 
                        type="primary" 
                        size="small"
                        :loading="autoGenerateLoading"
                        @click="handleAutoGenerate('1')"
                        class="auto-generate-btn"
                    >
                        <el-icon><MagicStick /></el-icon>
                        自动生成断言（常规）
                    </el-button>
                    <el-button 
                        type="primary" 
                        size="small"
                        plain
                        :loading="autoGenerateLoading"
                        @click="handleAutoGenerate('2')"
                        class="auto-generate-btn"
                    >
                        <el-icon><MagicStick /></el-icon>
                        自动生成断言（详细）
                    </el-button>
                </div>
                <el-checkbox v-model="keepExistingAssertions" class="keep-checkbox">
                    保留原有断言数据
                </el-checkbox>
            </div>
        </div>

        <div class="add-row-container" v-if="currentPageData.length === 0">
            <el-button @click="addRow(0)" type="primary" plain class="add-row-button">
                <el-icon><Plus /></el-icon>
                <span>添加一行</span>
            </el-button>
        </div>
        <div class="table-container" v-if="currentPageData.length != 0">
            <el-table size="small" :data="tableData" border style="width: 100%">
            <!-- 为每个属性定义一个带有表单输入的列 -->
            <el-table-column label="断言名称">
                <template #default="scope">
                    <el-form :model="scope.row" :rules="rules" ref="ruleFormRef">
                        <el-form-item prop="name">
                            <el-input v-model="scope.row.name" />
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column label="断言对象">
                <template #default="scope">
                    <el-form :model="scope.row" :rules="rules" ref="ruleFormRef"
                        v-if="scope.row.obj === '2' || scope.row.obj === '4'">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item prop="obj">
                                    <el-select v-model="scope.row.obj" placeholder="请选择" @change="onObjChange($event, scope.row)">
                                        <el-option v-for="obj in assertObj" :key="obj.id" :label="obj.name"
                                            :value="obj.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <template v-if="scope.row.obj === '4'">
                                    <el-form-item prop="jsonpath">
                                        <el-input v-model="scope.row.jsonpath"></el-input>
                                    </el-form-item>
                                </template>
                                <template v-if="scope.row.obj === '2'">
                                    <el-form-item prop="header_key">
                                        <el-input v-model="scope.row.header_key"></el-input>
                                    </el-form-item>
                                </template>
                            </el-col>
                        </el-row>
                    </el-form>
                    <el-form :model="scope.row" :rules="rules" ref="ruleFormRef" v-else>
                        <el-form-item prop="obj">
                            <el-select v-model="scope.row.obj" placeholder="请选择" @change="onObjChange($event, scope.row)">
                                <el-option v-for="obj in assertObj" :key="obj.id" :label="obj.name" :value="obj.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column label="断言方法">
                <template #default="scope">
                    <el-form :model="scope.row" :rules="rules" ref="ruleFormRef">
                        <el-form-item prop="operator">
                            <el-select v-model="scope.row.operator" placeholder="请选择" v-if="getOperatorOptions(scope.row.obj).length > 0">
                                <el-option v-for="type in getOperatorOptions(scope.row.obj)" :key="type" :label="type" :value="type">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column label="值">
                <template #default="scope">
                    <el-form :model="scope.row">
                        <el-form-item prop="data">
                            <el-input v-model="scope.row.data" :input-style="{ color: scope.row.data.includes('{{') && scope.row.data.includes('}}') ? '#F26B4C' : 'inherit' }" v-if="scope.row.operator !== '存在' && scope.row.operator !== '类型等于'"/>
                            <el-select v-model="scope.row.data" v-if="scope.row.operator === '类型等于'">
                                <el-option label="string" value="string"></el-option>
                                <el-option label="int" value="int"></el-option>
                                <el-option label="array" value="array"></el-option>
                                <el-option label="object" value="object"></el-option>
                                <el-option label="boolean" value="boolean"></el-option>
                                <el-option label="number" value="number"></el-option>
                                <el-option label="null" value="NoneType"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <!-- 操作列，用于添加或删除行 -->
            <el-table-column label="操作" width="120">
                <template #default="scope">
                    <div class="action-buttons">
                        <el-button type="primary" size="small" circle :icon="Plus" @click="addRow(scope.$index)"
                            :disabled="scope.$index !== currentPageData.length - 1 || currentPage * pageSize < total"></el-button>
                        <el-button type="danger" size="small" circle :icon="Minus" @click="removeRow(scope.$index)"></el-button>
                    </div>
                </template>
            </el-table-column>
            </el-table>
        </div>
        <div class="pagination-container">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" @current-change="changePage"
                layout="prev, pager, next, total" :total="total" background />
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, reactive, toRaw, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus, Minus, MagicStick } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus'
import { useStore } from '@/stores';
import { genAssert } from '@/apis/api_test/gen_assert/gen_assert'

const currentPageData: any = ref([])
const currentPage = ref(1)
const pageSize = 6 // 每页显示的数据条数
const total = ref(0)
const autoGenerateLoading = ref(false)
const keepExistingAssertions = ref(false) // 是否保留原有断言数据

// modelValue是v-modle的默认属性名
const props = defineProps({
    AssertionData: Array,
    // 新增props用于接收父组件传递的数据和方法
    projectId: Number,
    apiId: Number,
    environment: Number,
    onRunApi: Function
})

const emit = defineEmits(['updatePostData']) // 定义emit函数


interface RuleForm {
    id: number
    name: string
    obj: string
    operator: string
    header_key: string
    data: string
    jsonpath: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
    id: -1,
    name: '',
    obj: '',
    operator: '',
    header_key: '',
    data: '',
    jsonpath: ''
})

const rules = reactive<FormRules<RuleForm>>({
    name: [
        { required: true, message: '请输入变量名', trigger: 'blur' },
        { validator: (rule, value) => value.trim() !== '', message: '变量名不能只包含空格', trigger: 'blur' },
    ],
    obj: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
    operator: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
    jsonpath: [
        { required: true, message: '请输入JSONPath表达式', trigger: 'blur' },
    ]
})

const dicStore = useStore()
const dicKey = 'dic_assertions'
const assertObj:any = ref([])

watch(() => dicStore.dic_list, () => {
    const found = dicStore.dic_list.find((o: any) => o.dic_key === dicKey);
    if (found) {
        try {
            // 尝试将dic_value解析为JSON
            assertObj.value = JSON.parse(found.dic_value);
        } catch (error) {
            // 如果解析失败，就直接使用原始字符串
            assertObj.value = [];
        }
        // console.log('assertObj', assertObj.value)
    }
},{deep:true,immediate:true})

const selectedTypes = ref<string[]>([])

// 监听第一个下拉框的变化，更新第二个下拉框的选项
const onObjChange = (newValue: any, row: RuleForm, isInitialization: boolean = false) => {
    const obj = assertObj.value.find((o: any) => o.id === newValue);
    selectedTypes.value = obj ? obj.operator : [];

    if (!isInitialization && !selectedTypes.value.includes(row.operator)) {
        row.operator = ''; // 只有在非初始化且当前operator不在selectedTypes中时才重置
    }
};

const ruleForms = ref<RuleForm[]>([])
const tableData = ref([])

const changePage = (newPage: number) => {
    currentPage.value = newPage
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    currentPageData.value = ruleForms.value.slice(start, end)
    tableData.value = currentPageData.value
}

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
    emit('updatePostData', toRaw(ruleForms.value))
}

// 自动生成断言逻辑
const handleAutoGenerate = async (builderType: string) => {
    if (!props.onRunApi) {
        ElMessage.error('无法获取接口运行方法')
        return
    }

    autoGenerateLoading.value = true
    
    try {
        // 先运行接口获取响应数据
        const responseData = await props.onRunApi()
        
        if (!responseData || !responseData.res_json_data) {
            ElMessage.error('获取响应数据失败，请先确保接口能正常运行')
            return
        }

        // 构造生成断言的参数
        const genAssertParams = {
            api_data: {
                res_json_data: responseData.res_json_data,
                res_headers: responseData.res_headers,
                res_status_code: responseData.res_status_code
            },
            builder_type: builderType
        }

        // 调用生成断言接口
        const genResponse = await genAssert(genAssertParams)
        
        if (genResponse && genResponse.data.code === 2000) {
            const generatedAssertions = genResponse.data.data.tests || []
            
            if (!keepExistingAssertions.value) {
                // 清空现有断言数据
                ruleForms.value = []
            }
            
            // 将生成的断言数据添加到断言列表中
            generatedAssertions.forEach((assertion: any) => {
                const newAssertion = {
                    id: -1,
                    name: assertion.name || '',
                    obj: assertion.obj || '',
                    operator: assertion.operator || '',
                    header_key: assertion.header_key || '',
                    data: assertion.data || '',
                    jsonpath: assertion.jsonpath || ''
                }
                ruleForms.value.push(newAssertion)
            })
            
            // 更新总数和分页
            total.value = ruleForms.value.length
            
            // 跳转到最后一页显示新添加的断言
            const lastPage = Math.ceil(total.value / pageSize)
            currentPage.value = lastPage || 1
            changePage(currentPage.value)
            
            // 触发页面更新以确保断言方法列正确显示
            updateData()
            
            ElMessage.success(`成功生成 ${generatedAssertions.length} 个断言`)
        } else {
            ElMessage.error('生成断言失败：' + (genResponse?.data?.msg || '未知错误'))
        }
    } catch (error) {
        // console.error('自动生成断言失败:', error)
        ElMessage.error('自动生成断言失败，请重试')
    } finally {
        autoGenerateLoading.value = false
    }
}


watch(ruleForms, () => {
    updateData()
}, { deep: true })

watch(() => props.AssertionData, () => {
    if (props.AssertionData) {
        ruleForms.value = props.AssertionData.map((data: any) => ({
            id: data.id || '',
            name: data.name || '',
            obj: data.obj || '',
            header_key: data.header_key || '',
            operator: data.operator || '',
            data: data.data || '',
            jsonpath: data.jsonpath || ''
        }));
        total.value = props.AssertionData.length;
        changePage(1);
        ruleForms.value.forEach(form => {
            onObjChange(form.obj, form, true); // 初始化时传递true
        });
    }
}, { deep: true, immediate: true });


const addRow = (index: number) => {
    // 当点击添加一行按钮，或者点击+号
    if ((index === 0) || (index === currentPageData.value.length - 1 && currentPage.value * pageSize >= total.value)) {
        const newRow = {
            id: -1,
            name: '',
            obj: '',
            header_key: '',
            operator: '',
            data: '',
            jsonpath: ''
        }
        ruleForms.value.push(newRow) // 先更新ruleForms
        total.value += 1
        if (total.value > currentPage.value * pageSize) {
            currentPage.value += 1
        }
        changePage(currentPage.value) // 然后更新currentPageData
    }
}

const removeRow = (index: number) => {
    const globalIndex = (currentPage.value - 1) * pageSize + index
    ruleForms.value.splice(globalIndex, 1) // 先更新ruleForms
    total.value -= 1
    if (currentPageData.value.length === 0 && currentPage.value > 1) {
        currentPage.value -= 1
    }
    changePage(currentPage.value) // 然后更新currentPageData
}

// 根据断言对象类型获取可用的操作符选项
const getOperatorOptions = (obj: string) => {
    if (!obj || !assertObj.value.length) return []
    const foundObj = assertObj.value.find((o: any) => o.id === obj)
    return foundObj ? foundObj.operator : []
}

</script>

<style scoped>
/* 断言容器样式 */
.assertion-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  min-height: 0;
  overflow: visible;
}

/* 自动生成断言按钮区域样式 */
.auto-generate-section {
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.auto-generate-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.button-group {
    display: flex;
    gap: 8px;
}

.keep-checkbox {
    color: #606266;
    font-size: 12px;
}

.auto-generate-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    padding: 6px 10px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .auto-generate-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .button-group {
        flex-wrap: wrap;
    }
}

/* 调整表格单元格的内边距 */
.el-table .el-table__body-wrapper .el-table__row .el-table__cell {
    padding-bottom: 0;
    /* 减少底部内边距 */
}

/* 调整表单元素的外边距 */
.el-table .el-form-item {
    margin-bottom: 0;
    /* 移除底部外边距 */
}

/* 添加一行按钮样式 */
.add-row-container {
    display: flex;
    justify-content: center;
    padding: 15px 0;
    background-color: #f9fafc;
    border-radius: 6px;
    border: 1px dashed #dcdfe6;
    margin-bottom: 12px;
}

.add-row-button {
    width: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    padding: 10px 20px;
    transition: all 0.3s;
}

.add-row-button:hover {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #c6e2ff;
}

/* 表格内操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 分页容器样式 */
.pagination-container {
    margin-top: 12px;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
    padding: 10px 0;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
    position: sticky;
    bottom: 0;
    z-index: 10;
}
</style>