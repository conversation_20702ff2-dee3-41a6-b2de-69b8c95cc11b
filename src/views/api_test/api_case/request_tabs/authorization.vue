<template>
  <el-form :model="form" label-width="100px">
    <el-form-item>
      <span style="font-weight: bold;margin-right: 10px;">类型</span>
      <el-select style="width: 200px;" v-model="form.type" placeholder="please select your zone" @change="clearData">
        <el-option label="None" value="none" />
        <el-option label="继承父级认证" value="parent_auth" />
        <el-option label="Basic Auth" value="basic_auth" />
        <el-option label="Bearer Token" value="bearer_token" />
      </el-select>
    </el-form-item>
    <el-form-item label="Username" v-if="form.type === 'basic_auth'" style="width: 500px">
      <el-input v-model="form.username" :input-style="{ color: form.username.includes('{{') && form.username.includes('}}') ? '#F26B4C' : 'inherit' }" />
    </el-form-item>
    <el-form-item label="Password" v-if="form.type === 'basic_auth'" style="width: 500px">
      <el-input v-model="form.password" :input-style="{ color: form.password.includes('{{') && form.password.includes('}}') ? '#F26B4C' : 'inherit' }" />
    </el-form-item>
    <el-form-item label="Token" v-if="form.type === 'bearer_token'" style="width: 500px">
      <el-input v-model="form.token" :input-style="{ color: form.token.includes('{{') && form.token.includes('}}') ? '#F26B4C' : 'inherit' }"/>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, watch, toRaw } from 'vue'

const props = defineProps({
  AuthorizationData: Object
})

const emit = defineEmits(['updatePostData'])

const form = ref({
  username: '',
  password: '',
  token: '',
  type: ''
})

const updateData = () => {
  // 使用toRaw函数确保传递的是非响应式对象
  // 使用toRaw函数确保传递的是非响应式对象的原因是，Vue 3中的响应式系统会将数据包装成响应式对象。
  // 当这些响应式对象被传递到Vue组件或函数外部时，外部环境可能不需要或不应该操作这些响应式对象，
  // 因为这可能会导致不可预见的副作用，尤其是在不支持Vue响应式系统的环境中。
  emit('updatePostData', toRaw(form.value))
}

// 监听form的变化，使用deep选项来深度监听对象内部的变化
watch(form, () => {
  updateData()
}, { deep: true })


watch(() => props.AuthorizationData, () => {
  if (props.AuthorizationData) {
    form.value.username = props.AuthorizationData.username || ''
    form.value.password = props.AuthorizationData.password || ''
    form.value.token = props.AuthorizationData.token || ''
    form.value.type = props.AuthorizationData.type || 'none'
  }
},{deep: true, immediate: true})
// 清空数据
const clearData = () => {
  form.value.username = ''
  form.value.password = ''
  form.value.token = ''
}

</script>
