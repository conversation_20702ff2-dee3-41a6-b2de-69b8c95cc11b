<template>
  <el-col :span="24">
    <el-row :gutter="20">
      <el-col :span="20">
        <el-empty class="chart" v-if="chartOption.series[0].data[0].value === 0 && chartOption.series[0].data[1].value === 0" description="暂无数据" />
        <ECharts v-else class="chart" :option="chartOption" autoresize />
      </el-col>
      <el-col :span="4">
        <el-button type="primary" @click="handleExport" style="width: 120%;">导出报告</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-descriptions class="chart" title="接口执行结果" v-model="result_data">
          <el-descriptions-item label="总耗时">{{ result_data.total_elapsed_time }}s</el-descriptions-item>
          <el-descriptions-item label="接口请求耗时">{{ result_data.api_elapsed_time }}s</el-descriptions-item>
          <el-descriptions-item label="平均接口请求耗时">{{ result_data.avg_api_elapsed_time }}s</el-descriptions-item>
          <el-descriptions-item label="循环数">{{ result_data.iterations }}</el-descriptions-item>
          <el-descriptions-item label="断言数">{{ result_data.assertion_count }}</el-descriptions-item>
          <el-descriptions-item label="最近执行时间">{{ formatDate(result_data.execution_time) }}</el-descriptions-item>
        </el-descriptions>
      </el-col>
    </el-row>
  </el-col>
</template>

<script setup lang="ts">
import { ref, watchEffect, inject } from 'vue';
import { ElMessage } from 'element-plus';
import { formatDate } from '@/util/format_date';
import { generateHtmlReport, downloadHtmlReport } from '@/utils/export_report';

const props = defineProps({
  result_data: {
    type: Object,
    required: true
  }
})

// 从父组件注入 API 列表和场景数据
const apiList = inject<any>('apiList', ref([]))
const activateSceneData = inject<any>('activateSceneData', ref({}))

const result_data:any = ref({})
watchEffect(() => {
  result_data.value = props.result_data
})

const chartOption = ref({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    top: '5%',
    left: 'center'
  },
  series: [
    {
      name: '接口执行状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '70%'],
      startAngle: 180,
      endAngle: 360,
      data: [
        { value: 0, name: '成功', itemStyle: { color: '#46D6A0' } },
        { value: 0, name: '失败', itemStyle: { color: '#FC97AF' } }
      ]
    }
  ]
});

watchEffect(() => {
  if (result_data.value.echart_data) {
    chartOption.value.series[0].data[0].value = result_data.value.echart_data.success_count || 0;
    chartOption.value.series[0].data[1].value = result_data.value.echart_data.fail_count || 0;
  } else {
    chartOption.value.series[0].data[0].value = 0;
    chartOption.value.series[0].data[1].value = 0;
  }
});

const handleExport = () => {
  try {
    // 检查是否有数据
    if (!result_data.value || !apiList.value || apiList.value.length === 0) {
      ElMessage.warning('暂无数据可导出')
      return
    }

    // 构造报告数据
    const reportData = {
      scene_api_info: apiList.value,
      scene_api_result: result_data.value
    }

    // 获取场景名称
    const sceneName = activateSceneData.value?.scene_name || '未命名场景'

    // 生成HTML报告
    const htmlContent = generateHtmlReport(reportData, sceneName)
    
    // 下载报告
    const filename = `${sceneName}_测试报告_${new Date().getTime()}.html`
    downloadHtmlReport(htmlContent, filename)
    
    ElMessage.success('报告导出成功！')
  } catch (error) {
    ElMessage.error('导出报告失败，请确保API已经运行')
  }
}
</script>

<style>
.chart {
  width: 100%;
  height: 300px;
}
</style>