<template>
  <el-form ref="sceneFormRef" style="max-width: 600px" :model="sceneForm" :rules="rules" label-width="auto"
    class="demo-ruleForm" :size="formSize" status-icon>
    <el-form-item label="场景名称" prop="scene_name">
      <el-input v-model="sceneForm.scene_name" placeholder="请输入场景名称" />
    </el-form-item>
    <el-form-item label="场景编码" prop="scene_code">
      <el-input disabled v-model="sceneForm.scene_code" placeholder="请输入场景编码" />
    </el-form-item>
    <el-form-item label="运行环境" prop="environment_id">
      <el-select v-model="sceneForm.environment_id" placeholder="选择运行环境">
        <el-option v-for="env in run_env" :key="env.id" :label="env.name" :value="env.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="测试数据" prop="var_file">
      <el-row>
        <el-col :span="18">
          <el-upload 
            v-model:file-list="sceneForm.var_file"
            :action="uploadFileServerUrl + '/api/api_test/api_test_data_file_upload/'"
            accept=".csv" :multiple="false" :headers="{ 'Authorization': 'Bearer ' + RequestToken }" 
            :data="{ 'file_type': '1', 'parent_scene_code': sceneForm.scene_code }" 
            :on-remove="handleRemove"
            :limit="1" 
            :on-success="onSuccess"
            :on-error="onError">
            <el-button type="" size="small">上传csv文件</el-button>
          </el-upload>
        </el-col>
        <el-col :span="6">
          <el-button type="" size="small" @click="show_csv_data_preview" style="margin-left: 10px;">预览</el-button>
        </el-col>
      </el-row>
  </el-form-item>
    <el-form-item label="循环次数" prop="iterations">
      <el-input v-model="sceneForm.iterations" type="number" />
    </el-form-item>
    <el-form-item label="延迟时间" prop="delay_time">
      <el-input v-model="sceneForm.delay_time" type="number">
        <template #append>
          毫秒
        </template>
      </el-input>
    </el-form-item>
    <el-form-item>
      <el-button style="margin-left: auto;" type="primary" :loading="run_loading" @click="runSceneApiList">
        运行
      </el-button>
      <el-button v-if="!props.showSaveAsButton" style="margin-right: auto;"
        @click="submitForm(sceneFormRef)">保存</el-button>
      <el-button v-if="props.showSaveAsButton" style="margin-right: auto;" @click="open_save_as"
        :loading="run_loading">另存为场景</el-button>
    </el-form-item>
  </el-form>
  <save_as_scene v-model="dialogSaveAs" :projectId="props.projectId" @saveAsScene="saveAsScene"></save_as_scene>
  <csv_data_preview v-model="show_csv_data_dialog" :csv_data="csv_data"></csv_data_preview>
</template>

<script lang="ts" setup>
import { reactive, ref, watchEffect, watch, inject } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { postSceneApi } from '@/apis/api_test/case_test/scene_test/post_scene_api'
import { runSceneApi } from '@/apis/api_test/case_test/scene_test/run_scene_api'
import { ElMessage } from 'element-plus'
import { getSceneInfo } from '@/apis/api_test/case_test/scene_test/get_scene_info'
import { getSceneResult } from '@/apis/api_test/case_test/scene_test/get_scene_result'
import save_as_scene from '@/views/api_test/api_case/save_as_scene.vue'
import { postSceneCaseTree } from '@/apis/api_test/scene_test/post_scene_case_tree'
import Cookies from 'js-cookie'
import { deleteSceneCsvFile } from '@/apis/api_test/scene_test/delete_scene_csv_file'
import csv_data_preview from '@/views/api_test/scene_case/csv_data_preview.vue'
import { getSceneCsvData } from '@/apis/api_test/scene_test/get_scene_csv_data'
import { useStore } from '@/stores';

const dicStore = useStore()
const dicKey = 'upload_file_server_url'
const uploadFileServerUrl = ref('http://127.0.0.1:8000')

watch(() => dicStore.dic_list, () => {
    const found = dicStore.dic_list.find((o: any) => o.dic_key === dicKey);
    if (found) {
        uploadFileServerUrl.value = found.dic_value
        // console.log('uploadFileServerUrl', uploadFileServerUrl.value)
    }
},{deep:true,immediate:true})

const RequestToken = Cookies.get('token')

// const fileList = ref<UploadUserFile[]>([])

const emit = defineEmits(['loading', 'resultData'])

const dialogSaveAs = ref(false)
const open_save_as = () => {
  dialogSaveAs.value = true
}

const props = defineProps({
  envDropList: Array,
  apiList: Array,
  projectId: Number,
  scene_code: String,
  result_data: {
    type: Object,
    default: () => { }
  },
  activate_scene_data: {
    type: Object,
    default: () => { }
  },
  showSaveAsButton: {
    type: Boolean
  }
})



const run_env = ref()
const folder_id_of_api = ref([])
const scene_code: any = ref(props.scene_code)
const show_csv_data_dialog = ref(false)
const csv_data = ref([])

const getCsvData = async (scene_code: string) => {
    const res = await getSceneCsvData(scene_code)
    if (res && res.data.code === 2000) {
        csv_data.value = res.data.data
    } else {
        ElMessage.warning(res.data.msg)
        csv_data.value = []
    }
}

const show_csv_data_preview = () =>{
  show_csv_data_dialog.value = true
  getCsvData(sceneForm.value.scene_code.toString())
}
const handleFolderId = (apiList: any) => {
  // console.log("处理前的 apiList:", apiList); // 输出处理前的数据
  const folderIds = apiList.filter((item: any) => item.checked).map((item: any) => item.folder_id);
  // console.log("提取的 folder_id:", folderIds); // 输出处理后的结果
  return folderIds;
};


interface RuleForm {
  scene_name: String
  scene_code: String
  environment_id: any
  iterations: Number
  delay_time: Number
  folder_id_of_api: Array<Number>
  scene_type: Number
  folder: any
  project_id: any
  var_file: Array<any>
}

const formSize = ref<ComponentSize>('default')
const sceneFormRef = ref<FormInstance>()
const sceneForm = ref<RuleForm>({
  scene_name: "",
  scene_code: scene_code.value,
  environment_id: run_env.value,
  iterations: 1,
  delay_time: 10,
  folder_id_of_api: folder_id_of_api.value,
  scene_type: 1,
  //只保存场景时，folder为null
  folder: null,
  project_id: props.projectId,
  var_file: []
})

watchEffect(() => {
  run_env.value = props.envDropList
})

watch(() => props.activate_scene_data, (newActivateSceneData, oldActivateSceneData) => {
  if (newActivateSceneData !== oldActivateSceneData) {
    sceneForm.value.scene_name = newActivateSceneData.scene_name;
    sceneForm.value.scene_code = newActivateSceneData.scene_code;
    sceneForm.value.environment_id = newActivateSceneData.environment_id;
    sceneForm.value.iterations = newActivateSceneData.iterations;
    sceneForm.value.delay_time = newActivateSceneData.delay_time;
    sceneForm.value.var_file = newActivateSceneData.var_file;
    scene_code.value = newActivateSceneData.scene_code;
  }
}, { deep: true, immediate: true });

const rules = reactive<FormRules<RuleForm>>({
  environment_id: [
    {
      required: true,
      message: '请选择运行环境',
      trigger: 'blur',
    },

  ],
  var_file: [
    {
      required: false,
      message: '请上传csv文件',
      trigger: 'blur',
    },

  ],
  iterations: [
    {
      required: true,
      message: '请输入循环次数',
      trigger: 'blur',
    },

  ],
  delay_time: [
    {
      required: true,
      message: '请输入延迟时间',
      trigger: 'blur',
    },
  ],
  scene_name: [
    {
      required: true,
      message: '请输入场景名称',
      trigger: 'blur',
    },

  ],
  scene_code: [
    {
      required: true,
      message: '请输入场景编码',
      trigger: 'blur',
    },

  ]
})

watch(() => props.apiList, (newApiList) => {
  folder_id_of_api.value = handleFolderId(newApiList);
  // 确保 sceneForm 中的 folder_id_of_api 始终与 folder_id_of_api.value 同步
  sceneForm.value.folder_id_of_api = folder_id_of_api.value;
}, { immediate: true, deep: true });



const submitForm = async (formEl: FormInstance | undefined): Promise<boolean> => {
  if (folder_id_of_api.value.length === 0) {
    ElMessage.warning('请选择要运行的接口')
    return false
  }
  if (!formEl) return false
  return new Promise((resolve) => {
    formEl.validate(async (valid, fields) => {
      if (valid) {
        // console.log("sceneForm", sceneForm)
        try {
          const res = await postSceneApi(sceneForm.value) // 使用 await 等待 Promise 解决
          if (res.data.code === 2000) {
            ElMessage.success(res.data.msg)
            resolve(true)
            const scene_info = await getSceneInfo(scene_code.value)
            if (scene_info && scene_info.data.code === 2000) {
              sceneForm.value = scene_info.data.data
            }
          } else {
            ElMessage.error(res.data.msg)
            resolve(false)
          }
        } catch (error: any) {
          ElMessage.error(error.message)
          resolve(false)
        }
      } else {
        ElMessage.error('保存失败，请先将运行参数填写完整')
        resolve(false)
      }
    })
  })
}


const run_loading = ref(false)
const setLoading = inject('setLoading') as (isLoading: boolean) => void;
const runSceneApiList = async () => {
  setLoading(true);
  run_loading.value = true
  const isSaved: any = await submitForm(sceneFormRef.value);
  if (isSaved) {
    const res = await runSceneApi({
      // 调整为动态
      scene_code: scene_code.value
    });
    if (res.data.code === 2000) {
      ElMessage.success(res.data.msg);
      const scene_result = await getSceneResult(scene_code.value)
      if (scene_result && scene_result.data.code === 2000) {
        emit('resultData', scene_result.data.data)
      }
    } else {
      ElMessage.error(res.data.msg);
    }
  }
  setLoading(false);
  run_loading.value = false
}

const saveAsScene = async (parent_folder_id: number) => {
  // 如果用户没有选中目录就提示
  if (parent_folder_id === 0) {
    ElMessage.warning('请选择要保存的目录')
    return
  }
  const ifSaved = await submitForm(sceneFormRef.value);
  if (ifSaved) {
    const postForm = reactive<any>({
      name: sceneForm.value.scene_name,
      parent: parent_folder_id || 0,
      type: '2',
      scene_code: scene_code.value || null
    })
    // 先建立一个接口，用于保存用例
    const response = await postSceneCaseTree(postForm)
    if (response && response.data.code === 2000) {
      ElMessage.success('请前往场景列表查看')
    } else {
      ElMessage.error(response.data.msg)
    }
  }
}

const handleRemove = async (file: any) => {
  // console.log(file)
  const res = await deleteSceneCsvFile(scene_code.value, file.name)
  if (res.data.code === 2000) {
    ElMessage.success(res.data.msg)
  } else {
    ElMessage.error(res.data.msg)
  }
}

const onSuccess = (res: any) => {
  ElMessage.success("上传成功")
  // console.log(res)
}

const onError = (err: any) => {
  ElMessage.error(err.message)
}

</script>