<template>
  <el-tabs>
    <el-tab-pane label="概览">
        <p style="margin-bottom: 10px; font-size: 14px; font-weight: bold;">名称：{{ folderName }}</p>
        <p><wang_editor :valueHtml="caseOverviewRichText" /></p>
    </el-tab-pane>
    <el-tab-pane label="接口列表">
    </el-tab-pane>
  </el-tabs>
</template>


<script setup lang='ts'>
import { ref, reactive, watchEffect } from 'vue'
import wang_editor from './wang_editor.vue'

const form = reactive({
  desc: ''
})

const props = defineProps({
  folderData: Object,
  caseOverviewRichText: String
})

const folderName = ref('')
const caseOverviewRichText = ref('')
watchEffect(() => {
  folderName.value = props.folderData?.label || ''
  caseOverviewRichText.value = props.caseOverviewRichText || ''
})

</script>

<style scoped></style>