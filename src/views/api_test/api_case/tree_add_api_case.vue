<template>
  <el-dialog v-model="dialogVisible" title="新建用例" width="25%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="85px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="用例名称" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item label="用例等级">
        <el-radio-group v-model="radio" size="large">
          <el-radio-button label="P0" value="0" />
          <el-radio-button label="P1" value="1" />
          <el-radio-button label="P2" value="2" />
          <el-radio-button label="P3" value="3" />
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postChildCase } from '@/apis/api_test/case_test/post_child_case'
import { useStore } from '@/stores';
import { useCaseApiStore } from "@/stores/case_api";
import { getCaseTree } from "@/apis/api_test/case_test/get_case_tree";

type mainStore = {
  project_info: {
    id: number
    name: string
  }
}

const mainStore = useStore() as mainStore

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  folderId: Number,
  ifStream: Boolean
})
const radio = ref('0')

const dialogVisible = ref(false)
const parent_id = ref(props.folderId || 0)
const project_id = ref()
const caseApiStore = useCaseApiStore()

watchEffect(() => {
  dialogVisible.value = props.modelValue
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.name = ''
  }
})

watchEffect(() => {
  project_id.value = mainStore.project_info.id
})

watchEffect(() => {
  parent_id.value = props.folderId || 0
})

watchEffect(() => {
  caseApiStore.setCaseLevel(radio.value)
})


const emit = defineEmits(['update:modelValue', 'updateTreeData']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  name: string
  parent: number
  type: string
  case_level: string
  if_stream: boolean
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  name: '',
  parent: parent_id.value || 0,
  type: '2',
  case_level: radio.value,
  if_stream: props.ifStream
})

watchEffect(() => {
  ruleForm.parent = parent_id.value || 0
  ruleForm.type = '2'
  ruleForm.case_level = radio.value
})

watchEffect(() => {
  ruleForm.if_stream = props.ifStream;
});



const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入用例名', trigger: 'blur' },
  ],
  case_level: [
    { required: true, message: '请选择用例等级', trigger: 'blur' },
  ],
})


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await postChildCase(ruleForm)
      if (response && response.data.code === 2000) {
        const TreeDataResponse = await getCaseTree(project_id.value)
        if (TreeDataResponse && TreeDataResponse.data.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateTreeData', TreeDataResponse.data.data)  // 触发 update 事件
        }
      } else {
        ElMessage.error(response.data.msg)
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}

</script>
<style scoped></style>