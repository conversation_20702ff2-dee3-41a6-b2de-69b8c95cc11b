<template>
    <el-table size="small" :data="tableData" border style="width: 100%">
        <el-table-column prop="status" label="Status" width="100px">
            <template #default="{ row }">
                <el-tag effect="dark" :type="row.status === '通过' ? 'success' : 'danger'">{{ row.status }}</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="title" label="Title" width="300px"/>
        <el-table-column prop="error_log" label="AssertErrorLog"/>
    </el-table>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue'

const props = defineProps({
    AssertResultData: Array
})
const tableData:any = ref([])

watchEffect(()=>{
    tableData.value = props.AssertResultData || []
})
</script>

