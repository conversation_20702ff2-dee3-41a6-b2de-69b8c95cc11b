<template>
  <div class="main">
    <code-mirror v-model="codeVal" wrap basic :lang="lang" style="height: 400px; font-size: 12px;" :extensions="extensions" readonly />
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import CodeMirror from 'vue-codemirror6'
import { json, jsonParseLinter } from '@codemirror/lang-json';
import { linter } from '@codemirror/lint';

const props = defineProps({
  ResponseJsonData: Object || String
})

const codeVal = ref('');

watchEffect(() => {
  if (props.ResponseJsonData && props.ResponseJsonData.stream_data) {
    // 直接处理并赋值，不使用 JSON.stringify
    codeVal.value = props.ResponseJsonData.stream_data.replace(/\\n/g, '\n');
  } else {
    codeVal.value = JSON.stringify(props.ResponseJsonData || {}, null, '\t');
  }
});

const lang = json();
const jsonLinter = linter(jsonParseLinter());
const extensions = [jsonLinter];
</script>

<style>
/* required! */
.cm-editor {
height: 100%;
}

</style>