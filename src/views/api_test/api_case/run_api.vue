<template>
    <el-dialog v-model="dialogApiRunVisible" title="执行API" fullscreen :open-delay="200" :before-close="handleClose">
        <!-- <el-select         
            v-model="select_value"
            placeholder="Select"
            style="width: 240px">
            <el-option
                v-for="number in selectOptions"
                :key="number"
                :label="`迭代${number}`"
                :value="number">
            </el-option>
        </el-select> -->
        <el-radio-group v-model="select_value" style="margin-bottom: 10px;">
            <el-radio-button  v-for="number in selectOptions" :key="number" :label="`迭代${number}`" :value="number"></el-radio-button>
        </el-radio-group>
        <el-row>
            <el-col :span="16" v-loading="loading" element-loading-text="保存并运行中...">
                <div class="itxst">
                    <draggable tag="el-collapse" :list="apiList" ghost-class="ghost" chosen-class="chosenClass"
                        animation="300" @start="onStart" @end="onEnd" item-key="id">
                        <template #item="{ element }">
                            <el-collapse style="width: 100%;">
                                <el-collapse-item :title="element.request_method + ' ' + element.api_name">
                                    <template #title>
                                        <el-checkbox v-model="element.checked" @click.stop="checkItemChange(element)">
                                            <el-tag style="width: 40px;" type="info">{{ element.request_method
                                                }}</el-tag> 
                                            <el-tooltip 
                                                :content="element.api_name + ' ' + element.request_url" 
                                                placement="top"
                                                :disabled="element.request_url.length <= 50"
                                            >
                                                <el-tag style="font-size: 12px; color: #4A4A4A;">
                                                    {{ element.api_name }}
                                                    {{ element.request_url.length > 50 
                                                        ? element.request_url.substring(0, 50) + '...' 
                                                        : element.request_url 
                                                    }}
                                                </el-tag>
                                            </el-tooltip>
                                        </el-checkbox>
                                        <div
                                            style="display: flex; justify-content: right; align-items: center; width: 100%; margin-right: 15px;">
                                            <span style="margin-right: 10px;">
                                                <!-- <el-icon>
                                                    <ZoomIn />
                                                </el-icon> -->
                                                <!-- <span class="status-text">ExecStatus: </span> -->
                                                <el-tag
                                                    v-if="element.execution_result?.execution_status.includes('fail')"
                                                    style="color: #FC97AF; font-size: 12px;">
                                                    {{ element.execution_result?.execution_status }}
                                                </el-tag>
                                                <el-tag v-else-if="element.execution_result?.execution_status.includes('success')" style="color: #46D6A0; font-size: 12px;">
                                                    {{ element.execution_result?.execution_status }}
                                                </el-tag>
                                            </span>
                                            <el-icon>
                                                <Compass />
                                            </el-icon>
                                            <span class="status-text">Status: </span><span
                                                style="font-size: 12px; width: 40px;" :style="{ color: element.execution_result?.res_status_code >= 400 ? 'red' : 'green' }">{{
        element.execution_result?.res_status_code }}</span>
                                            <el-icon>
                                                <Timer />
                                            </el-icon>
                                            <span class="status-text">Time: </span><span
                                                style="font-size: 12px; width: 60px;">{{
        element.execution_result?.res_elapsed }}ms </span>
                                            <el-icon>
                                                <DocumentCopy />
                                            </el-icon>
                                            <span class="status-text">Size: </span><span
                                                style="font-size: 12px; width: 40px;">{{
        element.execution_result?.response_size?.total }}B </span>
                                            <span style="margin-left: 5px;">
                                                <el-link :underline="false" @click.stop="deleteItem(element)">
                                                    <el-icon>
                                                        <Delete />
                                                    </el-icon>
                                                </el-link>
                                            </span>
                                        </div>
                                    </template>
                                    <el-tabs v-if="element.execution_result?.error_info === null"
                                        v-model="element.activeTab" class="demo-tabs"
                                        @tab-click="handleTabClick(element, $event)">
                                        <el-tab-pane style="font-size: 12px;" label="Body"
                                            :name="element.api_id + 'Body'">
                                            <response_body :ResponseJsonData="element.execution_result?.res_json_data">
                                            </response_body>
                                        </el-tab-pane>
                                        <el-tab-pane label="Headers" :name="element.api_id + 'Headers'">
                                            <response_headers
                                                :ResponseHeadersData="element.execution_result?.res_headers">
                                            </response_headers>
                                        </el-tab-pane>
                                        <el-tab-pane label="断言结果" :name="element.api_id + 'Assert'">
                                            <assert_result
                                                :AssertResultData="element.execution_result?.assertion_result">
                                            </assert_result>
                                        </el-tab-pane>
                                    </el-tabs>
                                    <span v-if="element.execution_result?.error_info != null"
                                        style="color: #F56C6C; font-size: 12px;">
                                        {{ element.execution_result?.error_info }}
                                    </span>
                                </el-collapse-item>
                            </el-collapse>
                        </template>
                    </draggable>
                </div>
            </el-col>
            <el-col :span="8">
                <el-row :gutter="20" style="display: flex; flex-direction: column; height: 100%">
                    <el-col :span="24" style="flex: 1;">
                        <run_api_result :result_data="result_data" />
                    </el-col>
                    <el-col :span="24" style="flex: 1;">
                        <run_api_form :showSaveAsButton="props.showSaveAsButton" :result_data="result_data" @resultData="resultData" :envDropList="props.envDropList" :apiList="apiList"
                            :activate_scene_data="props.activate_scene_data" :projectId="props.projectId" :scene_code="scene_code" />
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </el-dialog>
</template>
<script setup lang='ts'>
import { ref, watch, provide, computed } from "vue";
import draggable from "vuedraggable";
import run_api_form from './run_api_form.vue'
import run_api_result from './run_api_result.vue'
import response_body from './run_api_response/response_body.vue'
import response_headers from './run_api_response/response_headers.vue'
import assert_result from './run_api_response/assert_result.vue'
import { deleteSceneList } from '@/apis/api_test/scene_test/delete_scene';
import { generateRandomString } from '@/util/generate_random_string'


const loading = ref(false);
const setLoading = (isLoading: boolean) => {
    loading.value = isLoading;
}
provide('setLoading', setLoading);

/*
draggable 对CSS样式没有什么要求万物皆可拖拽
:list="state.list"         //需要绑定的数组
ghost-class="ghost"        //被替换元素的样式
chosen-class="chosenClass" //选中元素的样式
animation="300"            //动画效果
@start="onStart"           //拖拽开始的事件
@end="onEnd"               //拖拽结束的事件
*/

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    apiList: {
        type: Array,
        default: () => []
    },
    envDropList: {
        type: Array,
        default: () => []
    },
    projectId: {
        type: Number,
        default: 0
    },
    resultHistoryData: {
        type: Object,
        default: () => {}
    },
    activate_scene_data: {
        type: Object,
        default: () => {}
    },
    showSaveAsButton: {
        type: Boolean
    }
})


const emit = defineEmits(['update:modelValue']) // 定义emit函数

const dialogApiRunVisible = ref(false);

const apiList = ref<any[]>([])

// 提供数据给子组件用于导出报告
provide('apiList', apiList);
provide('activateSceneData', computed(() => props.activate_scene_data));

const checkItemChange = (element: any) => {
    apiList.value = apiList.value.map((item: any) => {
        if (item.api_id === element.api_id) {
            item.checked = element.checked;
        }
        return item;
    });
    // console.log("apiListCheckItemChange", apiList.value)
};


const deleteItem = (element: any) => {
    const newList = apiList.value.filter((item: any) => item.api_id !== element.api_id);
    apiList.value = newList; // 替换整个数组以确保 Vue 能检测到变化
    // console.log("apiListAfterDelete", apiList.value);
};

watch(() => [props.modelValue, props.apiList], ([newModelValue, newApiList]) => {
    dialogApiRunVisible.value = newModelValue as boolean;
    apiList.value = newApiList as any[];
    // console.log("apiListWatch", apiList.value);
});

const scene_code = ref(generateRandomString(8))
const handleClose = () => {
    emit('update:modelValue', false) // 更新modelValue的值
    deleteSceneList(scene_code.value, "true")

}

// const state = reactive({
//     //需要拖拽的数据，拖拽后数据的顺序也会变化
//     list: apiList.value
// });

//拖拽开始的事件
const onStart = () => {
    // console.log("开始拖拽");
};

//拖拽结束的事件
const onEnd = () => {
    // console.log("结束拖拽");
    // console.log("apiList", apiList.value);
    // // 强制更新状态
    // apiList.value = [...apiList.value];
};

const select_value = ref(1); // 用于存储选中的值
const max_select_value = ref(1)

// 计算属性，生成一个从 1 到 select_value 的数组
const selectOptions = computed(() => {
    return Array.from({ length: Number(max_select_value.value) }, (_, i) => i + 1);
});

watch(() => select_value.value, (newValue) => {
    resultData(result_data.value)
});

const result_data: any = ref({})
const resultData = (data: any) => {
    // console.log("resultData_old", data);
    result_data.value = data;
    max_select_value.value = data.iterations
    // console.log("result_data111", result_data.value);
    // 确保 execution_result 存在并且是数组
    if (result_data.value && Array.isArray(result_data.value.execution_result)) {
        result_data.value.execution_result.forEach((resultItem: any) => {
            // console.log("resultItem", resultItem)
            const apiItem = apiList.value.find((api: any) => api.folder_id === resultItem.folder_id && resultItem.iteration === select_value.value);
            if (apiItem) {
                apiItem.execution_result = resultItem;
            }
        });
        // console.log("合并后的数据", apiList.value);
    }
};

const handleTabClick = (element: any, event: any) => {
    // 更新当前元素的活动标签页
    element.activeTab = event.name;
    // console.log("Tab changed for:", element.api_name, "to", event.name);
};

watch(() => apiList.value, (newApiList) => {
    newApiList.forEach(api => {
        if (api.activeTab === undefined) {
            api.activeTab = api.api_id + 'Body'; // 默认激活的tab
        }
    });
}, { deep: true, immediate: true });



watch(() => props.resultHistoryData, (newResultHistoryData) => {
    if (newResultHistoryData) {
        result_data.value = newResultHistoryData;
        // console.log("result_dataWatchwatch", result_data.value);
    }
    resultData(newResultHistoryData)
});



</script>
<style scoped lang="less">
.status-text {
    font-size: 12px;
}

:deep(.el-collapse-item__header) {
    display: flex;
    align-items: center;
    height: auto;
    line-height: 40px;
}

:deep(.el-collapse-item__title) {
    display: flex;
    align-items: center;
    width: 100%;
    flex-wrap: nowrap;
}

.itxst {
    width: 98%;
    height: 80%;
    display: flex;
    overflow-y: auto;
    /* 允许垂直滚动 */
}

.itxst>div:nth-of-type(1) {
    flex: 1;
}

.itxst>div:nth-of-type(2) {
    width: 270px;
    padding-left: 20px;
}

.item {
    border: solid 1px #eee;
    padding: 6px 10px;
    text-align: left;
}

.item:hover {
    cursor: move;
}

.item+.item {
    margin-top: 10px;
}

.ghost {
    border-top: solid 1px #409EFF;
}

.chosenClass {
    background-color: #f1f1f1;
}

.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

:deep(.el-tabs__item) {
    font-size: 12px;
    font-family: tahoma, arial, "Hiragino Sans GB", simsun, sans-serif;
}
</style>