<template>
  <el-dialog v-model="dialogVisible" title="保存为场景" width="30%" :before-close="handleClose">
    <div class="save-scene-container">
      <div class="search-container">
        <el-input 
          v-model="filterText" 
          placeholder="请输入关键词搜索" 
          prefix-icon="Search"
          clearable
          size="default" />
      </div>
      
      <div class="tree-container">
        <el-tree 
          ref="treeRef" 
          class="filter-tree" 
          :data="data" 
          :props="defaultProps"
          default-expand-all 
          highlight-current 
          check-on-click-node 
          :expand-on-click-node="false"
          :filter-node-method="filterNode" 
          @node-click="handleNodeClick">
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="tree-icon">
                <el-icon><Folder /></el-icon>
              </span>
              <span class="node-label">{{ node.label }}</span>
              <span v-if="case_tree_folder_id === data.id" class="selected-indicator">
                <el-icon><Check /></el-icon>
              </span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, watchEffect } from 'vue'
import { ElTree } from 'element-plus'
import { getSaveAsSceneTree } from "@/apis/api_test/case_test/scene_test/get_save_as_scene_tree";
import { Folder, Check, Search } from "@element-plus/icons-vue";

interface Tree {
  [key: string]: any
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

const emit = defineEmits(['update:modelValue', 'saveAsScene']) // 定义emit函数
const props = defineProps({
  projectId: Number,
  modelValue: Boolean
})

const dialogVisible = ref(false)
watchEffect(() => {
  dialogVisible.value = props.modelValue
})

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}


interface TreeNode {
  name?: string;
  children?: TreeNode[] | null;
  [key: string]: any;
}

function renameKeys(data: TreeNode[]): TreeNode[] {
  return data.map(({ name: label, children, ...rest }) => ({
    label,
    ...rest,
    children: children ? renameKeys(children) : null,
  }));
}

const data = ref<TreeNode[]>([])

const getTree = async () => {
  if (props.projectId) {
    const res = await getSaveAsSceneTree(props.projectId)
    if (res) {
      let treeData = res.data.data
      data.value = renameKeys([treeData])
    }
  }
}

getTree()

const case_tree_folder_id = ref(0)
const handleNodeClick = (data: Tree) => {
  if (data.type === '1') {
    case_tree_folder_id.value = data.id
  }
}

const submitForm = async () => {
  emit('saveAsScene', case_tree_folder_id.value)
  emit('update:modelValue', false)
}

</script>

<style scoped>
.save-scene-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-container {
  margin-bottom: 8px;
}

.tree-container {
  max-height: 350px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-icon {
  margin-right: 6px;
  color: #909399;
}

.node-label {
  flex: 1;
}

.selected-indicator {
  color: #409EFF;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>