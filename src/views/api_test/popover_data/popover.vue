<template>
  <el-popover
    placement="bottom-start"
    title=""
    :width="500"
    trigger="hover"
  >
    <child_popover :environmentId="props.environmentId" :projectId="props.projectId"></child_popover>
    <template #reference>
      <el-button type="" plain size="small">变量数据</el-button>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import child_popover from './child_popover.vue'

const props = defineProps({
  environmentId: {
    type: Number
  },
  projectId: {
    type: Number
  }
})

</script>

<style scoped>
.el-button+.el-button {
  margin-left: 8px;
}
</style>
