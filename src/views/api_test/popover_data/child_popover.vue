<template>
  <el-button :class="{ active: activeButton === 'public' }" plain size="small"
    @click="viewPublicData">项目变量</el-button>
  <el-button :class="{ active: activeButton === 'env' }" plain size="small" @click="viewEnvData">环境变量</el-button>
  <el-button :class="{ active: activeButton === 'faker' }" plain size="small"
    @click="viewFakerData">Faker变量</el-button>
  <div class="app-container">
    <el-table size="small" :data="tableData" stripe style="width: 100%">
      <el-table-column prop="key" label="变量名" />
      <el-table-column prop="type" label="类型">
        <template v-slot="{ row }">
          {{ getTypeName(row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="variable_type" label="变量值类型">
        <template v-slot="{ row }">
          <el-tag size="small">{{ getVariableTypeName(row.variable_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="变量值">
        <template v-slot="{ row }">
          <el-tooltip :content="row.value" placement="top">
            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;">
              {{ row.value }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="desc" label="描述" />
      <el-table-column prop="creator" label="创建人" /> -->
    </el-table>
  </div>
  <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
    size="small" :page-size="pageSize" layout="total, prev, pager, next" :total="totalItems">
  </el-pagination>
</template>

<script lang="ts" setup>
import { ref, watchEffect, watch } from 'vue'
import { getPublicDataInfo } from '@/apis/project/public_data/get_public_data';
import { getEnvironmentData } from '@/apis/project/environment/environment_data/get_environment_data'
import { useStore } from '@/stores'

const props = defineProps({
  projectId: Number,
  environmentId: Number
})

const projectId = ref(0)
const environmentId = ref(0)
const tableData = ref([])
const activeButton = ref('')


const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)


const dicStore = useStore()
const dicKey = 'faker_data_methond'
const fakerDataMethod: any = ref([])

watch(() => dicStore.dic_list, () => {
  const found = dicStore.dic_list.find((o: any) => o.dic_key === dicKey);
  if (found) {
    try {
      // 尝试将dic_value解析为JSON
      fakerDataMethod.value = JSON.parse(found.dic_value);
    } catch (error) {
      // 如果解析失败，就直接使用原始字符串
      fakerDataMethod.value = [];
    }
  }
}, { deep: true, immediate: true })


watchEffect(() => {
  projectId.value = props.projectId || 0
  environmentId.value = props.environmentId || 0
})


// 根据库里写死
const allTypes = [{ id: "1", name: "变量" }, { id: "2", name: "BASIC_AUTH" }, { id: "3", name: "BEARER_TOKEN" }, { id: "4", name: "BASE_URL" }]
const variableTypes = [{ id: "1", name: "string" }, { id: "2", name: "int" }, { id: "3", name: "array" }, { id: "4", name: "boolean" }, { id: "5", name: "object" }, { id: "6", name: "number" }, { id: "7", name: "sql" }]

const getTypeName = (typeId: string) => {
  const type = allTypes.find(t => t.id === typeId);
  return type ? type.name : '';
}

const getVariableTypeName = (variableTypeId: string) => {
  const variableType = variableTypes.find(t => t.id === variableTypeId);
  if (variableType) {
    return variableType ? variableType.name : '';
  } else {
    return '/'
  }

}

const viewPublicData = async () => {
  activeButton.value = 'public'
  const publicDataRes = await getPublicDataInfo(projectId.value)
  if (publicDataRes && publicDataRes.data.code === 2000) {
    // 过滤掉 variable_type 为 null 的数据
    const filteredData = publicDataRes.data.data.filter((item: any) => item.variable_type !== null)
    tableData.value = filteredData.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
    totalItems.value = filteredData.length
  }
}

const viewEnvData = async () => {
  activeButton.value = 'env'
  const res = await getEnvironmentData(environmentId.value, 100, 1)
  if (res && res.data.results.code === 2000) {
    tableData.value = res.data.results.data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
    totalItems.value = res.data.results.data.length
  }
}

const viewFakerData = async () => {
  activeButton.value = 'faker';
  tableData.value = []; // 先清空数据触发更新

  tableData.value = fakerDataMethod.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
  totalItems.value = fakerDataMethod.value.length;
}

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  if (activeButton.value === 'public') {
    viewPublicData()
  } else if (activeButton.value === 'env') {
    viewEnvData()
  } else if (activeButton.value === 'faker') {
    viewFakerData()
  }
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  if (activeButton.value === 'public') {
    viewPublicData()
  } else if (activeButton.value === 'env') {
    viewEnvData()
  } else if (activeButton.value === 'faker') {
    viewFakerData()
  }
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
  margin-top: 5px;
  margin-bottom: 11px;
}

.el-button.active {
  background-color: #409EFF;
  color: white;
}
</style>