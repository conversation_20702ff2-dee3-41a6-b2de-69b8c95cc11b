<template>
    <el-dialog :open-delay=200 v-model="dialogVisible" title="定时配置" width="500" :before-close="handleClose">
        <el-form :model="form">
            <el-form-item>
                启用：<el-switch v-model="form.enabled" inline-prompt active-text="是" inactive-text="否" />
            </el-form-item>
            <el-form-item>
                <el-radio-group v-model="form.radio" @change="handleRadioChange">
                    <el-radio value="1" border>按间隔</el-radio>
                    <el-radio value="2" border>按时间</el-radio>
                    <el-radio value="3" border>按crontab</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-model="form.interval" v-if="form.radio === '1'">
                <div style="display: flex; justify-content: space-between;">
                    <el-input type="number" v-model="form.interval.every" placeholder="数量"
                        style="flex: 1; margin-right: 10px;" />
                    <el-select v-model="form.interval.period" placeholder="单位" style="flex: 1;">
                        <el-option v-for="item in periodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item v-if="form.radio === '2'">
                <div class="block">
                    <el-date-picker v-model="form.clocked.clocked_time" type="datetime"
                        placeholder="Select date and time" :default-time="defaultTime" />
                </div>
            </el-form-item>
            <el-form-item v-if="form.radio === '3'" style="width: 350px;">
                <div style="display: flex; align-items: center;">
                    <div style="margin-right: 10px;">分钟：</div>
                    <el-input v-model="form.crontab.minute" placeholder="范围0-59；" style="flex: 1;" />
                    <el-tooltip content="输入0到59之间的数值, *表示每分钟" placement="top">
                        <el-icon>
                            <InfoFilled />
                        </el-icon>
                    </el-tooltip>
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <div style="margin-right: 10px;">小时：</div>
                    <el-input v-model="form.crontab.hour" placeholder="范围0-23；" style="flex: 1;" />
                    <el-tooltip content="输入0到23之间的数值, *表示每小时" placement="top">
                        <el-icon>
                            <InfoFilled />
                        </el-icon>
                    </el-tooltip>
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px; ">
                    <div style="margin-right: 10px;">星期：</div>
                    <el-input v-model="form.crontab.day_of_week" placeholder="范围0-6，1表示星期一；" style="flex: 1;" />
                    <el-tooltip content="0代表星期日，6代表星期六, *表示每星期" placement="top">
                        <el-icon>
                            <InfoFilled />
                        </el-icon>
                    </el-tooltip>
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px; ">
                    <div style="margin-right: 10px;">日期：</div>
                    <el-input v-model="form.crontab.day_of_month" placeholder="范围1-31；" style="flex: 1;" />
                    <el-tooltip content="输入1到31之间的数值, *表示月内每日" placement="top">
                        <el-icon>
                            <InfoFilled />
                        </el-icon>
                    </el-tooltip>
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px; ">
                    <div style="margin-right: 10px;">月份：</div>
                    <el-input v-model="form.crontab.month_of_year" placeholder="范围1-12；" style="flex: 1;" />
                    <el-tooltip content="输入1到12之间的数值, *表示每月" placement="top">
                        <el-icon>
                            <InfoFilled />
                        </el-icon>
                    </el-tooltip>
                </div>
            </el-form-item>
            <el-form-item>
                只执行最近一次：<el-switch :disabled="switch_disabled" v-model="form.one_off" inline-prompt active-text="是"
                    inactive-text="否" />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watchEffect, watch } from 'vue'
import { postSceneSchedule } from '@/apis/api_test/scene_test/post_scene_schedule'
import { ElMessage } from 'element-plus'
import { useStore } from '@/stores';
import { getSceneSchedule } from '@/apis/api_test/scene_test/get_scene_schedule'
import { putSceneSchedule } from '@/apis/api_test/scene_test/put_scene_schedule'
import { deleteSceneSchedule } from '@/apis/api_test/scene_test/delete_scene_schedule'

const dicStore = useStore()
const task = ref('')
watch(() => dicStore.dic_list, () => {
    const found = dicStore.dic_list.find((o: any) => o.dic_key === 'scene_task_name');
    if (found) {
        task.value = found.dic_value
    }
}, { deep: true, immediate: true })

const props = defineProps({
    modelValue: Boolean,
    sceneCode: {
        type: String,
        required: true
    },
    currentScheduleInfo: {
        type: Object,
        required: false
    }
})

const form = ref({
    radio: '1',
    interval: {
        every: props.currentScheduleInfo?.interval?.every || 1,
        period: props.currentScheduleInfo?.interval?.period || 'days'
    },
    clocked: {
        clocked_time: props.currentScheduleInfo?.clocked?.clocked_time || ''
    },
    one_off: props.currentScheduleInfo?.one_off || false,
    crontab: {
        minute: props.currentScheduleInfo?.crontab?.minute || "*",
        hour: props.currentScheduleInfo?.crontab?.hour || "*",
        day_of_week: props.currentScheduleInfo?.crontab?.day_of_week || "*",
        day_of_month: props.currentScheduleInfo?.crontab?.day_of_month || "*",
        month_of_year: props.currentScheduleInfo?.crontab?.month_of_year || "*"
    },
    name: props.currentScheduleInfo?.name || props.sceneCode,
    task: props.currentScheduleInfo?.task || task.value,
    args: props.currentScheduleInfo?.args || '[]',
    kwargs: props.currentScheduleInfo?.kwargs || `{"scene_code":"${props.sceneCode}"}`,
    enabled: props.currentScheduleInfo?.enabled || true
})

const defaultTime = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }))

const periodOptions = ref([
    { label: '天', value: 'days' },
    { label: '小时', value: 'hours' },
    { label: '分钟', value: 'minutes' }
])

const dialogVisible = ref(false)
watchEffect(() => {
    dialogVisible.value = props.modelValue
    form.value.name = props.sceneCode
})

const emit = defineEmits(['update:modelValue'])
const handleClose = () => {
    emit('update:modelValue', false)
}
const switch_disabled = ref(false)
const handleRadioChange = () => {
    if (form.value.radio === '2') {
        form.value.one_off = true
        switch_disabled.value = true
    } else {
        form.value.one_off = false
        switch_disabled.value = false
    }
}

const oldCurrentRadio = ref(form.value.radio)
const scheduleInfoExist = ref(false)
watch(() => props.currentScheduleInfo, (newInfo) => {
    if (newInfo && newInfo.name) {
        // console.log('newInfo', newInfo);
        scheduleInfoExist.value = true
        form.value = {
            ...form.value, // 保留原有的其他数据
            interval: newInfo.interval || form.value.interval, // 使用新数据或保留原有数据
            clocked: newInfo.clocked || form.value.clocked,
            crontab: newInfo.crontab || form.value.crontab,
            one_off: newInfo.one_off,
            name: newInfo.name,
            task: newInfo.task,
            args: newInfo.args,
            kwargs: newInfo.kwargs,
            enabled: newInfo.enabled
        };
        if (newInfo.interval) {
            form.value.radio = '1';
        } else if (newInfo.clocked) {
            form.value.radio = '2';
        } else {
            form.value.radio = '3';
        }
        oldCurrentRadio.value = form.value.radio
    } else {
        // 如果 newInfo 为空或不包含数据，重置 form
        form.value = {
            radio: '1',
            interval: {
                every: 1,
                period: 'days'
            },
            clocked: {
                clocked_time: ''
            },
            one_off: false,
            crontab: {
                minute: "*",
                hour: "*",
                day_of_week: "*",
                day_of_month: "*",
                month_of_year: "*"
            },
            name: props.sceneCode,
            task: task.value,
            args: '[]',
            kwargs: `{"scene_code":"${props.sceneCode}"}`,
            enabled: true
        };
        scheduleInfoExist.value = false
    }
}, { deep: true, immediate: true });


const handleSubmit = async () => {
    let data = {}
    // console.log('oldCurrentRadio', oldCurrentRadio)
    if (form.value.radio === '3') {
        data = {
            ...form.value,
            clocked: undefined,
            interval: undefined,
            radio: undefined
        }
    } else if (form.value.radio === '2') {
        data = {
            ...form.value,
            interval: undefined,
            crontab: undefined,
            radio: undefined
        }
    } else if (form.value.radio === '1') {
        data = {
            ...form.value,
            clocked: undefined,
            crontab: undefined,
            radio: undefined
        }
    }
    // console.log("data", data)
    if (scheduleInfoExist.value) {
        // console.log('oldCurrentRadio', oldCurrentRadio)
        // console.log('form.value.radio', form.value.radio)
        if (oldCurrentRadio.value !== form.value.radio) {
            const del_res = await deleteSceneSchedule(props.sceneCode)
            if (del_res && del_res.data.code === 2000) {
                const post_res = await postSceneSchedule(data)
                if (post_res && post_res.data.code === 2000) {
                    ElMessage.success(post_res.data.msg)
                    handleClose()
                } else {
                    ElMessage.error(post_res.data.msg)
                }
            } else {
                ElMessage.error(del_res.data.msg)
                return
            }
        } else {
            const put_res = await putSceneSchedule(props.sceneCode, data)
            if (put_res && put_res.data.code === 2000) {
                ElMessage.success(put_res.data.msg)
                handleClose()
            } else {
                ElMessage.error(put_res.data.msg)
            }
        }
    } else {
        const res = await postSceneSchedule(data)
        if (res && res.data.code === 2000) {
            ElMessage.success(res.data.msg)
            handleClose()
        } else {
            ElMessage.error(res.data.msg)
        }
    }


    // const res = await postSceneSchedule(data)
    // if (res && res.data.code === 2000) {
    //     ElMessage.success(res.data.msg)
    //     handleClose()
    // } else {
    //     ElMessage.error(res.data.msg)
    // }
}
</script>
<style scoped>
.demo-datetime-picker {
    display: flex;
    width: 100%;
    padding: 0;
    flex-wrap: wrap;
}

.demo-datetime-picker .block {
    padding: 30px 0;
    text-align: center;
    border-right: solid 1px var(--el-border-color);
    flex: 1;
}

.demo-datetime-picker .block:last-child {
    border-right: none;
}

.demo-datetime-picker .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
}
</style>