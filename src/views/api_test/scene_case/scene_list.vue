<template>
    <div class="app-container">
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="scene_name" label="场景名称" width="180">
                <template v-slot="{ row }">
                    <el-tooltip :content="row.scene_name" placement="top" :disabled="row.scene_name.length <= 25">
                        <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            {{ row.scene_name }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column prop="environment_name" label="环境" width="100">
                <template v-slot="{ row }">
                    <el-tooltip :content="row.environment_name" placement="top" :disabled="row.environment_name.length <= 10">
                        <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            {{ row.environment_name }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column prop="project_name" label="项目" width="100">
                <template v-slot="{ row }">
                    <el-tooltip :content="row.project_name" placement="top" :disabled="row.project_name.length <= 10">
                        <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            {{ row.project_name }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="iterations" label="轮数" width="60" align="center" />
            <el-table-column prop="delay_time" label="延迟" width="60" align="center" /> -->
            <!-- <el-table-column label="变量文件" width="100">
                <template v-slot="scope">
                    <el-tooltip :content="scope.row.var_file[0]?.name" placement="top" :disabled="!scope.row.var_file[0] || scope.row.var_file[0].name.length <= 10">
                        <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            {{ scope.row.var_file[0]?.name }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column> -->
            <el-table-column prop="oper_time" label="创建时间" width="160">
                <template v-slot="scope">
                    {{ formatDate(scope.row.oper_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center" >
                <template v-slot="scope">
                    <div class="action-buttons">
                        <el-button type='primary' link size="small" :loading="run_loading[scope.row.scene_code]"
                            @click="runScene(scope.row.scene_code)">运行</el-button>
                        <el-button type='primary' link size="small" @click="editScene(scope.row.scene_code)">编辑</el-button>
                        <el-button type='primary' link size="small"
                            @click="deleteScene(scope.row.scene_code)">删除</el-button>
                        <el-button type='primary' link size="small" @click="viewSceneReport(scope.row.scene_code)">下载报告</el-button>
                        <el-button type='primary' link size="small" @click="dialogSchedule(scope.row.scene_code)">定时</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    <run_api :showSaveAsButton="false" v-model="dialogApiRunVisible" :activate_scene_data="activate_scene_data" :apiList="apiList"
        :envDropList="envDropList" :resultHistoryData="result_history_data" :projectId="props.projectId"></run_api>
    <scene_schedule v-model="dialogScheduleVisible" :sceneCode="scheduleSceneCode" :currentScheduleInfo="currentScheduleInfo"></scene_schedule>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/util/format_date';
import { getAllSceneList } from '@/apis/api_test/scene_test/get_all_scene_list'
import { runSceneApi } from '@/apis/api_test/case_test/scene_test/run_scene_api';
import { deleteSceneList } from '@/apis/api_test/scene_test/delete_scene';
import run_api from '../api_case/run_api.vue'
import { getSceneInfoAndResult } from '@/apis/api_test/case_test/scene_test/get_scene_info_and_result';
import { getSceneSchedule } from '@/apis/api_test/scene_test/get_scene_schedule'
import scene_schedule from './scene_schedule.vue'
import { deleteSceneSchedule } from '@/apis/api_test/scene_test/delete_scene_schedule'
import { getSceneCaseTree } from '@/apis/api_test/scene_test/get_scene_case_tree'

const emit = defineEmits(['receiveTreeData'])

const dialogScheduleVisible = ref(false)

const props = defineProps({
    projectId: {
        type: Number,
        required: true
    },
    sceneList: {
        type: Array
    }
})


const currentScheduleInfo = ref({})
const scheduleSceneCode = ref('')
const dialogSchedule = async (sceneCode: string) => {
    dialogScheduleVisible.value = true
    scheduleSceneCode.value = sceneCode
    const res = await getSceneSchedule(sceneCode) // 直接在这里调用获取调度信息的 API
    if (res && res.data.code === 2000) {
        currentScheduleInfo.value = res.data.data
    } else {
        currentScheduleInfo.value = {}
    }
}

const loading = ref(false)

const queryParams = reactive({
    sceneNameQuery: '',
    page_size: 10,
    page: 1
})

const total = ref(0)
const tableData:any = ref([])
const getSceneList = async () => {
    const res = await getAllSceneList(queryParams.page_size, queryParams.page, props.projectId)
    if (res) {
        tableData.value = res.data.results
        total.value = res.data.count
        loading.value = false
    }
}

getSceneList()


watch(() => props.sceneList, (newSceneList) => {
    tableData.value = newSceneList;
}, { deep: true });

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getSceneList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getSceneList()
}

const run_loading = ref<Record<string, boolean>>({}) // 明确指定 run_loading 的类型
const runScene = async (sceneCode: string) => {
    run_loading.value[sceneCode] = true // 设置特定场景的 loading 状态为 true
    const res = await runSceneApi({
        "scene_code": sceneCode
    })
    if (res && res.data.code === 2000) {
        ElMessage.success('操作成功！')
    }
    run_loading.value[sceneCode] = false // 完成后设置 loading 状态为 false
}

interface TreeNode {
  name?: string;
  children?: TreeNode[] | null;
  [key: string]: any;
}

function renameKeys(data: TreeNode[]): TreeNode[] {
  return data.map(({ name: label, children, ...rest }) => ({
    label,
    ...rest,
    children: children ? renameKeys(children) : null,
  }));
}

const data = ref<TreeNode[]>([])

const getTree = async () => {
  if (props.projectId) {
    const res = await getSceneCaseTree(props.projectId)
    if (res) {
      let treeData = res.data.data
      data.value = renameKeys([treeData])
    }
  }
  emit('receiveTreeData', data.value)
}

const deleteScene = async (sceneCode: string) => {
    // 添加二次确认对话框
    const confirmResult = await ElMessageBox.confirm(
        '确定要删除这个场景吗？',
        '警告',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    );
    if (confirmResult === 'confirm') {
        const del_res: any = await deleteSceneList(sceneCode);
        if (del_res && del_res.data.code === 2000) {
            ElMessage.success(del_res.data.msg);
            getSceneList();
            getTree()
            const del_schedule_res: any = await deleteSceneSchedule(sceneCode);
            if (del_schedule_res && del_schedule_res.data.code === 2000) {
                // ElMessage.success(del_res.data.msg);
                // getSceneList();
            }
        }
    }
}

const activate_scene_data = ref({})
const activateSceneData = async (sceneCode: string) => {
    const sceneData = tableData.value.find((item: any) => item.scene_code === sceneCode)
    if (sceneData) {
        activate_scene_data.value = sceneData
    }

}

const dialogApiRunVisible = ref(false)
const envDropList = ref([])
const result_history_data: any = ref({})
const apiList = ref([])
const editScene = async (sceneCode: string) => {
    dialogApiRunVisible.value = true
    const res = await getSceneInfoAndResult(sceneCode)
    if (res && res.data.code === 2000) {
        apiList.value = res.data.data.scene_api_info
        envDropList.value = res.data.data.env_list
        result_history_data.value = res.data.data.scene_api_result || {}
        activateSceneData(sceneCode)
    } else {
        apiList.value = []
        envDropList.value = []
        result_history_data.value = {}
    }
}

const viewSceneReport = async (sceneCode: string) => {
    try {
        const res = await getSceneInfoAndResult(sceneCode)
        if (res && res.data.code === 2000 && res.data.data.scene_api_result) {
            // 构造报告数据
            const reportData = {
                scene_api_info: res.data.data.scene_api_info,
                scene_api_result: res.data.data.scene_api_result
            }

            // 获取场景名称
            const sceneData = tableData.value.find((item: any) => item.scene_code === sceneCode)
            const sceneName = sceneData?.scene_name || '未命名场景'

            // 生成HTML报告
            const { generateHtmlReport, downloadHtmlReport } = await import('@/utils/export_report')
            const htmlContent = generateHtmlReport(reportData, sceneName)
            
            // 下载报告
            const filename = `${sceneName}_测试报告_${new Date().getTime()}.html`
            downloadHtmlReport(htmlContent, filename)
            
            ElMessage.success('报告导出成功！')
        } else {
            ElMessage.warning('暂无测试结果数据')
        }
    } catch (error) {
        // console.error('查看报告失败:', error)
        ElMessage.error('查看报告失败')
    }
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}

.app-container {
    padding: 8px;
}

/* 确保表格内容不换行，使用省略号 */
:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

@media (max-width: 1200px) {
    .action-buttons {
        gap: 4px;
    }
}
</style>