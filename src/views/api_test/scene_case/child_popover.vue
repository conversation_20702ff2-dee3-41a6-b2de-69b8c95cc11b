<template>
  <div class="container">
    <div class="section">
      <h3>请求接口</h3>
      <p class="item"><el-button style="width: 220px;"><img src="@/assets/icon/scene_case/child_popover/API.png" class="icon" alt="从接口导入">从接口导入</el-button></p>
      <p class="item"><el-button style="width: 220px;"><img src="@/assets/icon/scene_case/child_popover/测试用例.png" class="icon" alt="从接口用例导入">从接口用例导入</el-button></p>
      <p class="item"><el-button style="width: 220px;"><img src="@/assets/icon/scene_case/child_popover/自定义.png" class="icon" alt="添加自定义请求">添加自定义请求</el-button></p>
    </div>
    <div class="section">
      <h3>其他</h3>
      <p class="item"><el-button disabled style="width: 220px;"><img src="@/assets/icon/scene_case/child_popover/条件分支.png" class="icon" alt="条件分支">条件分支</el-button></p>
      <p class="item"><el-button style="width: 220px;"><img src="@/assets/icon/scene_case/child_popover/循环.png" class="icon" alt="For循环">For 循环</el-button></p>
      <p class="item"><el-button style="width: 220px;"><img src="@/assets/icon/scene_case/child_popover/等待.png" class="icon" alt="等待时间">等待时间</el-button></p>
    </div>
    <!-- <div class="section">
      <h3>测试场景</h3>
      <p class="item"><el-button>从其它测试场景导入</el-button></p>
      <p class="item"><el-button>引用其它测试场景</el-button></p>
    </div> -->
  </div>
</template>

<script setup lang='ts'>
import { ref, reactive } from 'vue'

</script>

<style scoped>
.icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.section {
  margin-bottom: 20px;
}

.item {
  margin-bottom: 10px;
}

.item el-button {
  width: 150px; /* 设置按钮的宽度 */
}
</style>

