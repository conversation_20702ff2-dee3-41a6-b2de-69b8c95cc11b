<template>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="测试步骤" name="first"><popover></popover></el-tab-pane>
      <el-tab-pane label="测试数据" name="second">Config</el-tab-pane>
      <el-tab-pane label="测试报告" name="third">Role</el-tab-pane>
      <el-tab-pane label="CI/CD" name="fourth">Task</el-tab-pane>
    </el-tabs>
  </template>
  <script lang="ts" setup>
  import { ref } from 'vue'
  import type { TabsPaneContext } from 'element-plus'
  import popover from './popover.vue'
  
  const activeName = ref('first')
  
  const handleClick = (tab: TabsPaneContext, event: Event) => {
    // console.log(tab, event)
  }
  </script>
  
  <style scoped>
  </style>
  