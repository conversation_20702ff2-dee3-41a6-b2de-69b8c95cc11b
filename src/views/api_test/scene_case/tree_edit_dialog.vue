<template>
  <el-dialog v-model="dialogVisible" title="编辑" width="25%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="85px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="名称" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { getSceneCaseTree } from '@/apis/api_test/scene_test/get_scene_case_tree'
import { putSceneCaseTree } from '@/apis/api_test/scene_test/put_scene_case_tree'
import { getSceneListFromTree } from '@/apis/api_test/scene_test/get_scene_list'
import { useStore } from '@/stores';

type mainStore = {
    project_info: {
        id: number
        name: string
    }
}

const mainStore = useStore() as mainStore



// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  folderId: Number,
  folderName: String
})

const dialogVisible = ref(false)
const project_id = ref()
const folder_name = ref('')
const folder_id = ref()

watchEffect(() => {
  dialogVisible.value = props.modelValue
  folder_name.value = props.folderName || ''
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.name = folder_name.value
  }
})

watchEffect(() => {
  project_id.value = mainStore.project_info.id
})

watchEffect(() => {
  folder_id.value = props.folderId || 0
})

const emit = defineEmits(['update:modelValue', 'updateTreeData', 'nodeNameUpdated', 'updateSceneListForChild']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  name: string
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  name: props.folderName || ''
})

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ]
})


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await putSceneCaseTree(folder_id.value, ruleForm)
      if (response && response.data.code === 2000) {
        const TreeDataResponse = await getSceneCaseTree(project_id.value)
        if (TreeDataResponse && TreeDataResponse.data.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateTreeData', TreeDataResponse.data.data)  // 触发 update 事件
          emit('nodeNameUpdated', ruleForm.name)
          emit('updateSceneListForChild', folder_id.value, project_id.value)
        }
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}

</script>
<style scoped></style>