<template>
  <el-dialog v-model="dialogVisibleDel" title="提示" width="25%" :before-close="handleClose">
    <el-col style="font-size: 15px;"><el-icon size="16px" color="#E6A23C">
        <WarningFilled />
      </el-icon>确认删除吗？</el-col>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, watchEffect } from 'vue'
import { getSceneCaseTree } from '@/apis/api_test/scene_test/get_scene_case_tree'
import { deleteSceneCaseTree } from '@/apis/api_test/scene_test/delete_scene_case_tree'
import { useStore } from '@/stores';

type mainStore = {
  project_info: {
    id: number
    name: string
  }
}

const mainStore = useStore() as mainStore

const project_id = ref()
const folder_id = ref()
const dialogVisibleDel = ref(false)


// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['update:modelValue', 'updateTreeData']) // 定义emit函数

const props = defineProps({
  modelValue: Boolean,
  folderId: Number,
  showDelDialog: Boolean
})

watchEffect(() => {
  project_id.value = mainStore.project_info.id
})

watchEffect(() => {
  folder_id.value = props.folderId || 0
})

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

watchEffect(() => {
  dialogVisibleDel.value = props.modelValue
})

const submitForm = async () => {
  if (props.folderId) {
    const response = await deleteSceneCaseTree(folder_id.value)
    if (response && response.data.code === 2000) {
      const TreeDataResponse = await getSceneCaseTree(project_id.value)
      if (TreeDataResponse && TreeDataResponse.data.code === 2000) {
        ElMessage.success('操作成功！')
        // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
        emit('updateTreeData', TreeDataResponse.data.data, 'del')
      }
    }
  }
  handleClose()
}
</script>