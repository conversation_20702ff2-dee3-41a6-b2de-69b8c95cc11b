<template>
    <ECharts class="chart" :option="chartOption" autoresize />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const chartOption = ref({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    top: '5%',
    left: 'center'
  },
  series: [
    {
      name: '接口执行状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '70%'],
      // adjust the start and end angle
      startAngle: 180,
      endAngle: 360,
      data: [
        { value: 5500, name: '成功', itemStyle: { color: '#46D6A0' } },
        { value: 55, name: '失败', itemStyle: { color: '#FC97AF' } }
      ]
    }
  ]
});

onMounted(() => {
    // 可以在这里进行更复杂的操作，如动态数据处理等
});
</script>

<style>
.chart {
    width: 600px;
    height: 400px;
}
</style>