<template>
  <div class="common-layout">
    <el-container>
      <div class="aside-wrapper">
        <el-card style="height: 100%;">
          <quick_tree :treeData="treeData" :projectId='projectId' @ifShowApi="ifShowApi" @getFolderOrApi="getFolderOrApi"
            @nodeNameUpdated="handleNodeNameUpdated" @updateSceneList="updateSceneList"></quick_tree>
        </el-card>
      </div>
      <div class="main">
        <el-card style="height: 100%;">
          <!-- <scene_case_tabs></scene_case_tabs> -->
          <!-- <test_echarts></test_echarts> -->
          <!-- 如果projectId是undefined，则不显示scene_list，避免报错 -->
          <scene_list v-if="projectId" :projectId='projectId' :sceneList='sceneList' @receiveTreeData="receiveTreeData"></scene_list>
        </el-card>
      </div>
    </el-container>
  </div>
</template>


<script setup lang='ts'>
import { ref, watchEffect } from 'vue'
import quick_tree from './quick_tree.vue'
import { useStore } from '@/stores';
import scene_list from './scene_list.vue'
import { getSceneListFromTree } from '@/apis/api_test/scene_test/get_scene_list'
import { getAllSceneList } from '@/apis/api_test/scene_test/get_all_scene_list'

interface ProjectInfo {
  id: number
  // add other properties as needed
}

const projectStore = useStore()
const projectId = ref((projectStore.project_info as ProjectInfo).id)
const creatorId = ref((projectStore.user_info as any).username)


watchEffect(() => {
  projectId.value = (projectStore.project_info as ProjectInfo).id
  creatorId.value = (projectStore.user_info as any).username
})

const ifShow = ref('')
const apiId = ref(0)
const sceneList = ref([])
const ifShowApi = async (type: string, tree_node_id: number) => {
  ifShow.value = type
  apiId.value = tree_node_id

  if (type == '999') {
    const res = await getAllSceneList(10, 1, projectId.value)
    if (res) {
      sceneList.value = res.data.results
    }
  } else {
    const res = await getSceneListFromTree(tree_node_id, projectId.value)
    if (res) {
      if (res.data.results) {
        sceneList.value = res.data.results
      } else {
        sceneList.value = res.data.data
      }
    }
  }
}

const updateSceneList = async (folderId: number, projectId: number) => {
  const res = await getSceneListFromTree(folderId, projectId)
  if (res) {
    sceneList.value = res.data.data
  }
}



const folderOrApiData = ref({})
const getFolderOrApi = (data: object) => {
  folderOrApiData.value = data
}

const handleNodeNameUpdated = (newName: string) => {
  if ('label' in folderOrApiData.value) {
    (folderOrApiData.value as any).label = newName;
  }
}

const treeData:any = ref([])
const receiveTreeData = (data: object) => {
  // console.log("data", data)
  treeData.value = data
}


</script>

<style scoped>
.common-layout {
  height: 100vh;
  padding: 12px;
  box-sizing: border-box;
}

.el-container {
  height: 100%;
}

.aside-wrapper {
  width: 300px;
  min-width: 260px;
  height: 100%;
  margin-right: 12px;
}

.main {
  flex: 1;
  height: 100%;
  min-width: 0;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .aside-wrapper {
    width: 280px;
    min-width: 240px;
  }
}

@media (max-width: 992px) {
  .common-layout {
    padding: 8px;
  }
  
  .aside-wrapper {
    width: 240px;
    min-width: 220px;
    margin-right: 8px;
  }
}
</style>