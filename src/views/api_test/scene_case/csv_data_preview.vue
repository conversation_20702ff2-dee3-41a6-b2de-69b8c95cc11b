<template>
    <el-dialog v-model="dialogVisible" :before-close="handleClose">
        <el-table :data="csv_data">
            <!-- 添加固定的序号列 -->
            <el-table-column v-if="csv_data.length > 0" type="index" label="序号" width="100"></el-table-column>
            <!-- 其他列保持不变 -->
            <el-table-column v-for="key in csv_data.length > 0 ? Object.keys(csv_data[0] as object) : []" :key="key"
                :label="key" :prop="key">
            </el-table-column>
        </el-table>
    </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watch } from 'vue'
import { getSceneCsvData } from '@/apis/api_test/scene_test/get_scene_csv_data'

const props = defineProps({
    csv_data: {
        type: Array,
        default: () => []
    },
    modelValue: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue']) // 定义emit函数

const dialogVisible = ref(props.modelValue)
const csv_data = ref(props.csv_data)

watch(() => props.modelValue, (newVal) => {
    // console.log(newVal)
    dialogVisible.value = newVal
}, { deep: true })

// 监视 scene_code 的变化
watch(() => props.csv_data, async (newVal, oldVal) => {
    csv_data.value = newVal
}, { deep: true, immediate: true })

const handleClose = () => {
    emit('update:modelValue', false)
}
</script>

<style scoped></style>