<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card v-loading="loading" class="box-card">
          <div class="card-header" style="margin-bottom: 20px;">
            <span style="font-size: 20px;">项目信息</span>
          </div>
          <div class="project-info">
            <table>
              <tr>
                <td>项目名称</td>
                <td style="font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;">
                  {{projectName}}
                </td>
              </tr>
              <tr>
                <td>成员数量</td>
                <td class="info-value">{{countMembers}}</td>
              </tr>
              <tr>
                <td>环境数量</td>
                <td class="info-value">{{countEnvironments}}</td>
              </tr>
              <tr>
                <td>创建人</td>
                <td class="info-value">{{creator}}</td>
              </tr>
            </table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="box-card">
          <el-tabs v-model="activeName" class="user-center-tabs">
            <el-tab-pane label="成员管理" name="first">
              <member :projectInfo="projectInfo" :projectId="projectId" @updateData="getProject"></member>
            </el-tab-pane>
            <el-tab-pane label="公共数据" name="second">
              <public_data :projectPublicDataInfo="projectPublicDataInfo" :projectId="projectId" @updateData="getProject"></public_data>
            </el-tab-pane>
            <el-tab-pane label="环境管理" name="third">
              <environment :projectId="projectId" @updateData="getProject"></environment>
            </el-tab-pane>
            <el-tab-pane label="OpenAI配置" name="fourth">
              <openai_config :projectId="projectId"></openai_config>
            </el-tab-pane>
            <el-tab-pane label="数据源接入" name="fifth">
              <Suspense>
                <template #default>
                  <data_source :projectId="projectId" @updateData="getProject"></data_source>
                </template>
                <template #fallback>
                  <div class="app-container">
                    <el-skeleton :rows="5" animated />
                  </div>
                </template>
              </Suspense>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getProjectInfo } from '@/apis/project/get_projectinfo';
import environment from './components/environment/environment.vue';
import member from './components/member/member.vue'
import public_data from './components/public_data/public_data.vue';
import openai_config from './components/openai_config/index.vue';
import { defineAsyncComponent } from 'vue'

const data_source = defineAsyncComponent(() => import('./components/data_source/index.vue'))
import { getPublicDataInfo } from '@/apis/project/public_data/get_public_data';

const loading = ref(true)

// 路由传过来的项目id
const props = defineProps({
  project_id: String
})

const projectId = Number(props.project_id)

const activeName = ref('first')

const countMembers = ref(0)
const projectName = ref('')
const countEnvironments = ref(0)
const creator = ref('')

const projectInfo = ref({})
const projectPublicDataInfo = ref([])


const getProject =async () => {
  const res = await getProjectInfo(props.project_id)
  if ( res && res.data.code === 2000){
    countMembers.value = res.data.data.count_members
    projectName.value = res.data.data.name
    countEnvironments.value = res.data.data.count_environments
    creator.value = res.data.data.creator
    loading.value = false
    projectInfo.value = res.data.data
    const publicDataRes = await getPublicDataInfo(props.project_id)
    if ( publicDataRes && publicDataRes.data.code === 2000) {
      loading.value = false
      projectPublicDataInfo.value = publicDataRes.data.data
    }
  }
}

getProject()

</script>

<style  lang='less' scoped>
.project-info {
  display: flex;
  justify-content: center;
}

.project-info table {
  width: 100%;
}

.project-info td {
  text-align: center;
  padding: 10px;
  font-size: 16px;
}

.project-info tr {
  border-bottom: 1px dashed #ccc;
}

.info-value {
  font-weight: bold;
}
</style>