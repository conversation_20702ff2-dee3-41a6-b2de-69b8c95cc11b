<template>
    <el-drawer v-model="drawer" :direction="direction" size="90%" :show-close="false" :before-close="handleClose">
        <template #header="{ close, titleId, titleClass }">
            <span style="font-size: 24px; color: #606266;" :id="titleId" :class="titleClass">环境配置</span>
            <el-button type="success" @click="save" :loading="ifLoading">
                <el-icon class="el-icon--left">
                    <CircleCheck />
                </el-icon>
                保存
            </el-button>
            <el-button type="danger" @click="close">
                <el-icon class="el-icon--left">
                    <CircleCloseFilled />
                </el-icon>
                关闭
            </el-button>
        </template>
        <el-tabs v-model="activeName" class="user-center-tabs">
            <el-tab-pane label="变量配置" name="first">
                <env_data @updatePostData="updatePostData" :triggerUpdate="triggerUpdate"
                    :modifyEnvironmentId="modifyEnvironmentId" :EnvironmentData="EnvironmentData"></env_data>
            </el-tab-pane>
            <el-tab-pane label="HTTP配置" name="second">
                <http_config @updatePostData="updatePostUrlData" :triggerUpdate="triggerUpdate"
                    :modifyEnvironmentId="modifyEnvironmentId" :EnvironmentUrl="EnvironmentUrl"></http_config>
            </el-tab-pane>
            <el-tab-pane label="全局前置脚本" name="third">
                <before_script></before_script>
            </el-tab-pane>
            <el-tab-pane label="全局后置脚本" name="fourth">
                <after_script></after_script>
            </el-tab-pane>
            <el-tab-pane label="全局认证" name="fifth">
                <env_auth @updatePostData="updatePostAuthData" :triggerUpdate="triggerUpdate"
                    :modifyEnvironmentId="modifyEnvironmentId" :EnvironmenAtuh="EnvironmenAtuh"></env_auth>
            </el-tab-pane>
            <el-tab-pane label="全局断言" name="sixth">
                <assert></assert>
            </el-tab-pane>
        </el-tabs>
    </el-drawer>
</template>
<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import { ElMessageBox, ElMessage, ElNotification } from 'element-plus'
import env_data from './environment_data/env_data.vue'
import http_config from './environment_data/http_config.vue'
import before_script from './environment_data/before_script.vue'
import after_script from './environment_data/after_script.vue'
import env_auth from './environment_data/env_auth.vue'
import assert from './environment_data/assert.vue'
import { postAllEnvironmentData } from '@/apis/project/environment/post_environment_all_data'

const emit = defineEmits(['updateEnvironmentDataList'])


const activeName = ref('first')

const props = defineProps({
    drawerVisible: Boolean,
    drawerData: Object,
    drawerUrl: Object,
    drawerAtuh: Object,
    modifyEnvironmentId: Number,
    triggerUpdate: Boolean, // 新增的属性
})

const triggerUpdate = ref(false)
const drawer = ref(false)
const EnvironmentData: any = ref([])
const EnvironmentUrl: any = ref([])
const EnvironmenAtuh: any = ref([])
const modifyEnvironmentId = ref(0)

watchEffect(() => {
    drawer.value = props.drawerVisible
    if (props.drawerData) {
        EnvironmentData.value = props.drawerData
        EnvironmentUrl.value = props.drawerUrl
        EnvironmenAtuh.value = props.drawerAtuh
        modifyEnvironmentId.value = props.modifyEnvironmentId || 0
        triggerUpdate.value = props.triggerUpdate
    }
})


const direction = ref('rtl')
const handleClose = (done: () => void) => {
    ElMessageBox.confirm('确定退出吗？')
        .then(() => {
            done()
        })
        .catch(() => {
            // catch error
        })
}

const postEnvData = ref([])
const postUrlData = ref({})
const postAuthData = ref({})



// 更新请求参数
const updatePostData = (subTabData: any) => {
    postEnvData.value = subTabData || []
}


const updatePostUrlData = (subTabData: any) => {
    // console.log("Url变了？", subTabData);
    // 重新改
    if (subTabData) {
        if (subTabData.url === "") {
            // console.log("subTabData.url === ''");
            postUrlData.value = []
        } else {
            postUrlData.value = [subTabData]
        }

    }
}

const updatePostAuthData = (subTabData: any) => {
    // console.log("Auth变了？", subTabData);
    if (subTabData && subTabData.type !== '') {
        if (subTabData.type === '1' && subTabData.auth_username === '') {
            postAuthData.value = []
        } else if (subTabData.type === '2' && subTabData.token_value === '') {
            postAuthData.value = []
        } else {
            postAuthData.value = [subTabData]
        }
    } else {
        postAuthData.value = []
    }
}


const ifLoading = ref(false)

const save = async () => {
    let validateItem: any = ref({})
    for (validateItem of postEnvData.value) {
        // console.log("validateItem", validateItem)
        if (validateItem.id) {
            if (validateItem.key.trim() === "" || validateItem.value.trim() === "" || validateItem.type === "") {
                // ElMessage.error('[变量配置TAB页]-有未填写项，请检查！')
                ElNotification({
                    title: '[变量配置TAB页]',
                    message: '有未填写项，请检查！',
                    type: 'warning',
                    position: 'top-right',
                    offset: 50,
                })
                return false
            }
        }

    }

    const postData = {
        "envId": props.modifyEnvironmentId || 0,
        "data": [
            {
                "tabId": "data",
                "items": postEnvData.value
            },
            // 后端接口这边要列表类型
            {
                "tabId": "url",
                "items": postUrlData.value
            },
            // 后端接口这边要列表类型
            {
                "tabId": "auth",
                "items": postAuthData.value
            }
        ]
    }
    ifLoading.value = true
    const res = await postAllEnvironmentData(postData)
    if (res && res.data.code === 2000) {
        ElMessage.success('操作成功！')
        ifLoading.value = false
        emit('updateEnvironmentDataList', postData.envId)
    } else {
        return false
    }


}


</script>

<style scoped>
</style>
