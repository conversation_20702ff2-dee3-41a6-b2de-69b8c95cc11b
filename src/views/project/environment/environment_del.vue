<template>
  <el-popconfirm confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled" icon-color="#626AEF" title="确定删除吗？"
    @confirm="confirmEvent">
    <template #reference>
      <el-button type="danger" :icon="Delete" circle></el-button>
    </template>
  </el-popconfirm>
</template>
  
<script setup lang="ts">
import { InfoFilled, Delete, } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deleteEnvironmentInfo } from '@/apis/project/environment/delete_environment';
import { getAllEnvironmentInfo } from '@/apis/project/environment/get_all_environmentinfo'

// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['deleteConfirmed'])

// 定义一个 props，用于接收父组件传递的用户 id
const props = defineProps({
  environmentId: {
    type: Number,
    required: true
  },
  projectId: {
    type: Number,
    required: true
  }
})

const confirmEvent = async () => {
  if (props.environmentId) {
    const response = await deleteEnvironmentInfo(props.environmentId)
    if (response && response.data.code === 2000) {
      const environmentInfoResponse = await getAllEnvironmentInfo(props.projectId)
      if (environmentInfoResponse && environmentInfoResponse.data.code === 2000) {
        ElMessage.success('操作成功！')
        // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
        emit('deleteConfirmed', environmentInfoResponse)  // 触发 update 事件
      }
    }
  }
}
</script>