<template>
    <div class="scale-down">
        <el-form ref="ruleFormRef" :label-position="labelPosition" :inline="true" :model="ruleForm" :rules="rules"
            label-width="100px" class="demo-ruleForm" :size="formSize" status-icon>
            <el-form-item style="width: 15%;" label="URL" prop="url">
                <el-input v-model="ruleForm.url" :disabled="!isEditable" />
            </el-form-item>
            <el-form-item style="width: 20%;" label="描述" prop="desc">
                <el-input v-model="ruleForm.desc" :disabled="!isEditable" />
            </el-form-item>
            <el-form-item style="width: 20%;" label="操作">
                <el-button type="info" color="#626aef" :icon="Edit" @click="openInput"
                    v-if="(isEditable == false)"></el-button>
                <el-button type="info" color="#626aef" :icon="Check" @click="closeInput"
                    v-if="(isEditable == true)"></el-button>
                <el-button type="info" color="#626aef" :icon="Close" @click="reset" v-if="(isEditable == true)"></el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { FormProps } from 'element-plus'
import { Edit, Check, Close } from '@element-plus/icons-vue';
import { useStore } from '@/stores';

type mainStore = {
    user_info: {
        username: string
    }
}

const mainStore = useStore() as mainStore
const createName = mainStore.user_info.username

// label置顶
const labelPosition = ref<FormProps['labelPosition']>('top')

// modelValue是v-modle的默认属性名
const props = defineProps({
    modifyEnvironmentId: {
        type: Number,
        required: true
    },
    EnvironmentUrl: Array,
    triggerUpdate: Boolean
})

const emit = defineEmits(['updatePostData']) // 定义emit函数

interface RuleForm {
    id: number
    url: string
    desc: string
    creator: string
}

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
    id: -1,
    url: '',
    desc: '',
    creator: createName || ''
})

const rules = reactive<FormRules<RuleForm>>({
    url: [
        { required: true, message: '请输入url', trigger: 'blur' },
    ]
})

const updateData = () => {
    emit('updatePostData', ruleForm)
}

watchEffect(() => {
    if (props.modifyEnvironmentId && props.EnvironmentUrl && props.EnvironmentUrl.length > 0) {
        const environmentUrl = props.EnvironmentUrl as RuleForm[];
        // 给表单赋值
        ruleForm.id = environmentUrl[0].id
        ruleForm.url = environmentUrl[0].url;
        ruleForm.desc = environmentUrl[0].desc;
        ruleForm.creator = environmentUrl[0].creator;
    } else {
        ruleForm.id = -1;
        ruleForm.url = '';
        ruleForm.desc = '';
        ruleForm.creator = createName || ''
    }
})


watch(ruleForm, () => {
    updateData();
}, { deep: true, immediate: true });


const isEditable = ref(false)
const ifHttpValid = ref(true)
const openInput = () => {
    isEditable.value = true
}

const closeInput = () => {
    ruleFormRef.value?.validate((valid) => {
        if (valid) {
            isEditable.value = false
        } else {
            // console.log('URL验证失败')
            ifHttpValid.value = false
        }
    })
}

const reset = () => {
    ruleForm.id = -1;
    ruleForm.url = '';
    ruleForm.desc = '';
    ruleForm.creator = createName || ''
    isEditable.value = false
    ifHttpValid.value = false
}

</script>