<template>
  <div class="scale-down">
    <el-row :gutter="20" class="header">
      <!-- <el-col :span="4">
        <el-input placeholder="请输入变量名..." v-model="queryParams.envVarNameQuery" clearable></el-input>
      </el-col> -->
      <el-col :span="4">
        <el-button v-if="currentPageData.length === 0" type="info" color="#626aef" @click="addRow(0)">添加一行
        </el-button>
      </el-col>
    </el-row>
    <div v-for="(form, index) in currentPageData" :key="index">
      <el-form ref="ruleFormRef" :label-position="labelPosition" :inline="true" :model="form" :rules="rules"
               label-width="100px" class="demo-ruleForm" :size="formSize" status-icon>
        <el-form-item style="width: 15%;" label="变量名" prop="key">
          <el-input v-model="form.key" @input="updateData"/>
        </el-form-item>
        <el-form-item style="width: 10%;" label="类型" prop="type">
          <el-select v-model="form.variable_type" placeholder="请选择" @change="updateData">
            <el-option v-for="type in allTypes" :label="type.name" :value="type.id" :key="type.id"/>
          </el-select>
        </el-form-item>
        <el-form-item style="width: 20%;" label="值" prop="value" v-if="ruleForm.type !== '2'">
          <el-input v-model="form.value" @input="updateData"/>
        </el-form-item>
        <el-form-item style="width: 20%;" label="描述" prop="desc">
          <el-input v-model="form.desc" @input="updateData"/>
        </el-form-item>
        <el-form-item style="width: 15%;" label="操作" prop="desc">
          <el-button type="info" color="#626aef" :icon="Plus" @click="addRow(index)"
                     :disabled="index !== currentPageData.length - 1 || currentPage * pageSize < total"></el-button>
          <el-button type="info" color="#626aef" :icon="Minus" @click="removeRow(index)"></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="demo-pagination-block">
      <el-pagination v-model:current-page="currentPage" :page-size="pageSize" @current-change="changePage"
                     layout="prev, pager, next, total" :total="total" class="mt-4"/>
    </div>
  </div>
</template>

<script setup lang='ts'>
import {ref, watchEffect, reactive, toRaw, watch} from 'vue'
import type {FormInstance, FormRules} from 'element-plus'
import type {FormProps} from 'element-plus'
import {Plus, Minus} from '@element-plus/icons-vue';
import {useStore} from '@/stores';

type mainStore = {
  user_info: {
    username: string
  }
}

const mainStore = useStore() as mainStore
const createName = mainStore.user_info.username

// label置顶
const labelPosition = ref<FormProps['labelPosition']>('top')

const currentPageData: any = ref([])
const currentPage = ref(1)
const pageSize = 6 // 每页显示的数据条数
const total = ref(0)
// modelValue是v-modle的默认属性名
const props = defineProps({
  modifyEnvironmentId: {
    type: Number,
    required: true
  },
  EnvironmentData: Array,
  triggerUpdate: Boolean
})

const emit = defineEmits(['updatePostData']) // 定义emit函数


interface RuleForm {
  id: number
  key: string
  type: string
  value: string
  desc: string
  creator: string
  variable_type?: string | null
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  id: -1,
  key: '',
  type: '1',
  value: '',
  desc: '',
  creator: createName || '',
  variable_type: ''
})

// 前端搜索
const queryParams: any = reactive({
  envVarNameQuery: '',
})

const rules = reactive<FormRules<RuleForm>>({
  key: [
    {required: true, message: '请输入变量名', trigger: 'blur'},
    {validator: (rule, value) => value.trim() !== '', message: '变量名不能只包含空格', trigger: 'blur'},
  ],
  type: [
    {required: true, message: '请选择类型', trigger: 'blur'},
  ],
  value: [
    {required: true, message: '请输入变量值', trigger: 'blur'},
    {validator: (rule, value) => value.trim() !== '', message: '变量名不能只包含空格', trigger: 'blur'},
  ]
})

const allTypes = [{id: "1", name: "string"}, {id: "2", name: "int"}, {id: "3", name: "array"}, {id: "4", name: "boolean"}, {id: "5", name: "object"}, { id: "6", name: "number" }]
const ruleForms = ref<RuleForm[]>([])

const changePage = (newPage: number) => {
  currentPage.value = newPage
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  currentPageData.value = ruleForms.value.slice(start, end)
}

const previousTriggerUpdate: any = ref(null)

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
  emit('updatePostData', toRaw(ruleForms.value))
}


watchEffect(() => {
  if (props.triggerUpdate !== previousTriggerUpdate.value) {
    // console.log("触发啦triggerUpdate进来了");

    if (props.modifyEnvironmentId && props.EnvironmentData) {

      if (props.EnvironmentData.length === 0) {
        // 清空ruleForms.value
        ruleForms.value = []
        // 当没有拿到表单数据时，添加一条空记录
        ruleForms.value.push({
          id: -1,
          key: '',
          type: '1',
          value: '',
          desc: '',
          creator: createName || '',
          variable_type: ''
        })
        total.value = 1
      } else {
        ruleForms.value = props.EnvironmentData.map((data: any) => ({
          id: data.id || '',
          key: data.key || '',
          type: '1',
          value: data.value || '',
          desc: data.desc || '',
          creator: data.creator || '',
          variable_type: data.variable_type || ''
        }))
        total.value = props.EnvironmentData.length
      }
      // 首次打开分页的第一页
      changePage(1)
    }

    previousTriggerUpdate.value = props.triggerUpdate
    updateData()
  }
})


watch(() => props.EnvironmentData, () => {
  if (props.EnvironmentData) {
    // 更新表单数据
    // console.log('更新表单数据了。。。', props.EnvironmentData);

    ruleForms.value = props.EnvironmentData.map((data: any) => ({
      id: data.id || '',
      key: data.key || '',
      type: '1',
      value: data.value || '',
      desc: data.desc || '',
      creator: data.creator || '',
      variable_type: data.variable_type || ''
    }))
    total.value = props.EnvironmentData.length
    changePage(1)
  }
})

const addRow = (index: number) => {
  // 当点击添加一行按钮，或者点击+号
  if ((index === 0) || (index === currentPageData.value.length - 1 && currentPage.value * pageSize >= total.value)) {
    const newRow = {
      id: -1,
      key: '',
      type: '1',
      value: '',
      desc: '',
      creator: createName || '',
      variable_type: ''
    }
    ruleForms.value.push(newRow) // 先更新ruleForms
    total.value += 1
    if (total.value > currentPage.value * pageSize) {
      currentPage.value += 1
    }
    changePage(currentPage.value) // 然后更新currentPageData
  }
  updateData()
}

const removeRow = (index: number) => {
  const globalIndex = (currentPage.value - 1) * pageSize + index
  ruleForms.value.splice(globalIndex, 1) // 先更新ruleForms
  total.value -= 1
  if (currentPageData.value.length === 0 && currentPage.value > 1) {
    currentPage.value -= 1
  }
  changePage(currentPage.value) // 然后更新currentPageData
  updateData()
}

</script>
<style scoped>
/* .scale-down {
    transform: scale(1);
} */
</style>