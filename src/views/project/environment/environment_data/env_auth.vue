<template>
    <div class="scale-down">
        <el-form ref="ruleFormRef" :label-position="labelPosition" :inline="true" :model="ruleForm" :rules="rules"
            label-width="100px" class="demo-ruleForm" :size="formSize" status-icon>
            <el-form-item label="类型" prop="type">
                <el-select style="width: 180px;" v-model="ruleForm.type" placeholder="请选择类型" :disabled="!isEditable">
                    <el-option v-for="type in allTypes" :label="type.name" :value="type.id" :key="type.id"
                        @click="change" />
                </el-select>
            </el-form-item>
            <el-form-item label="账号" prop="auth_username" v-if="ruleForm.type !== '2'">
                <el-input v-model="ruleForm.auth_username" :disabled="!isEditable" />
            </el-form-item>
            <el-form-item label="密码" prop="auth_password" v-if="ruleForm.type !== '2'">
                <el-input v-model="ruleForm.auth_password" :disabled="!isEditable" />
            </el-form-item>
            <el-form-item label="token值" prop="token_value" v-if="ruleForm.type === '2'">
                <el-input v-model="ruleForm.token_value" :disabled="!isEditable" />
            </el-form-item>
            <el-form-item label="描述" prop="desc">
                <el-input v-model="ruleForm.desc" :disabled="!isEditable" />
            </el-form-item>
            <el-form-item style="width: 20%;" label="操作">
                <el-button type="info" color="#626aef" :icon="Edit" @click="openInput"
                    v-if="(isEditable == false)"></el-button>
                <el-button type="info" color="#626aef" :icon="Check" @click="closeInput"
                    v-if="(isEditable == true)"></el-button>
                <el-button type="info" color="#626aef" :icon="Close" @click="reset" v-if="(isEditable == true)"></el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { FormProps } from 'element-plus'
import { Edit, Check, Close } from '@element-plus/icons-vue';
import { useStore } from '@/stores';

type mainStore = {
    user_info: {
        username: string
    }
}

const mainStore = useStore() as mainStore
const createName = mainStore.user_info.username

// label置顶
const labelPosition = ref<FormProps['labelPosition']>('top')

// modelValue是v-modle的默认属性名
const props = defineProps({
    modifyEnvironmentId: {
        type: Number,
        required: true
    },
    EnvironmenAtuh: Array,
    triggerUpdate: Boolean
})

const emit = defineEmits(['updatePostData']) // 定义emit函数

interface RuleForm {
    id: number
    auth_username: string
    type: string
    auth_password: string
    token_value: string
    desc: string
    creator: string
}

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
    id: -1,
    auth_username: '',
    type: '',
    auth_password: '',
    token_value: '',
    desc: '',
    creator: createName || ''
})

const rules = reactive<FormRules<RuleForm>>({
    auth_username: [
        { required: true, message: '请输入账号', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
    auth_password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
    ],
    token_value: [
        { required: true, message: '请输入token', trigger: 'blur' },
    ]
})

// 根据库里写死
const allTypes = [{ id: "1", name: "BASIC_AUTH" }, { id: "2", name: "BEARER_TOKEN" }]

const updateData = () => {
    emit('updatePostData', ruleForm)
}

watchEffect(() => {
    if (props.modifyEnvironmentId && props.EnvironmenAtuh && props.EnvironmenAtuh.length > 0) {
        const environmentAuth = props.EnvironmenAtuh as RuleForm[];
        // 给表单赋值
        ruleForm.id = environmentAuth[0].id;
        ruleForm.auth_username = environmentAuth[0].auth_username;
        ruleForm.type = environmentAuth[0].type;
        ruleForm.auth_password = environmentAuth[0].auth_password;
        ruleForm.token_value = environmentAuth[0].token_value;
        ruleForm.desc = environmentAuth[0].desc;
        ruleForm.creator = environmentAuth[0].creator;

    } else {
        ruleForm.id = -1;
        ruleForm.auth_username = '';
        ruleForm.type = '';
        ruleForm.auth_password = '';
        ruleForm.token_value = '';
        ruleForm.desc = '';
        ruleForm.creator = createName || '';
    }
})


watch(ruleForm, () => {
    updateData();
}, { deep: true, immediate: true });

// watch(()=>props.EnvironmenAtuh,() => {
//   if (props.EnvironmenAtuh) {
//     // 更新表单数据
//     console.log('更新表单数据了。。。', props.EnvironmenAtuh);

//     ruleForm = props.EnvironmenAtuh.map((data: any) => ({
//       id: data.id || '',
//       key: data.key || '',
//       type: data.type || '',
//       value: data.value || '',
//       desc: data.desc || '',
//       creator: data.creator || ''
//     }))
//   }
// })


const isEditable = ref(false)
const ifAuthValid = ref(true)
const openInput = () => {
    isEditable.value = true
}

const closeInput = () => {
    ruleFormRef.value?.validate((valid) => {
        if (valid) {
            isEditable.value = false
        } else {
            // console.log('AUTH验证失败')
            ifAuthValid.value = false
        }
    })
}

// 重置表单
const reset = () => {
    ruleForm.id = -1;
    ruleForm.auth_username = '';
    ruleForm.type = '';
    ruleForm.auth_password = '';
    ruleForm.token_value = '';
    ruleForm.desc = '';
    ruleForm.creator = createName || '';
    isEditable.value = false
    ifAuthValid.value = false
}

let descBackup = '';
let operationCount = 0;
let lastType = '';

const change = () => {
    if (ruleForm.type !== lastType) {
        operationCount++;

        if (operationCount % 2 === 1) {
            // 如果是奇数次操作，保存当前的desc并清空它
            descBackup = ruleForm.desc;
            ruleForm.desc = '';
        } else {
            // 如果是偶数次操作，恢复desc的原值
            ruleForm.desc = descBackup;
        }
    }

    // 更新lastType
    lastType = ruleForm.type;

    emit('updatePostData', ruleForm)
}


</script>