<template>
    <div class="app-container">
        <el-row>
            <el-select style="width: 200px;" v-model="value" filterable clearable @change="change" @clear="clear" placeholder="请先筛选项目">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button style="margin-left: 10px;" type="primary" :icon="Plus" @click="dialogAdd" :disabled="disabledAddButton">新增</el-button>
        </el-row>
        <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column prop="name" label="环境名称">
            </el-table-column>
            <el-table-column prop="creator" label="创建人" />
            <el-table-column prop="desc" label="描述" />
            <el-table-column prop="create_time" label="创建时间">
                <template v-slot="scope">
                    {{ formatDate(scope.row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button type="primary" :icon="Setting" circle @click="drawerSetting(scope.row.id)"></el-button>
                    <el-button type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <environment_del :projectId="projectId" :environmentId="scope.row.id"
                        @deleteConfirmed="updateEnvironmentList">
                    </environment_del>
                </template>
            </el-table-column>
            <template v-slot:empty>
                <el-empty v-if="tableData.length === 0" description="暂无数据" />
            </template>
        </el-table>
    </div>
    <environment_setting_drawer @updateEnvironmentDataList="updateEnvironmentDataList" v-model="drawerVisible" :drawerAtuh="drawerAtuh" :drawerUrl="drawerUrl" :drawerData="drawerData" :triggerUpdate="triggerUpdate" :modifyEnvironmentId='modifyEnvironmentId'></environment_setting_drawer>
    <environment_add_dialog :projectId="projectId" v-model="dialogVisible" @updateEnvironmentList="updateEnvironmentList">
    </environment_add_dialog>
    <environment_modify_dialog :projectId="projectId" v-model="modifyDialogVisible"
        :modifyEnvironmentId='modifyEnvironmentId' :environmentInfo="currentEnvironmentInfo"
        @updateEnvironmentList="updateEnvironmentList"></environment_modify_dialog>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getAllEnvironmentInfo } from '@/apis/project/environment/get_all_environmentinfo'
import { getEnvironmentData } from '@/apis/project/environment/environment_data/get_environment_data'
import { getAllProjectInfo } from '@/apis/project/get_all_projectinfo'
import { getEnvironmentUrl } from '@/apis/project/environment/environment_url/get_env_url'
import { getEnvironmentAuth } from '@/apis/project/environment/environment_auth/get_env_auth'
import { Setting, Edit, Plus } from '@element-plus/icons-vue';
import environment_add_dialog from './environment_add_dialog.vue'
import environment_del from './environment_del.vue';
import environment_modify_dialog from './environment_modify_dialog.vue';
import environment_setting_drawer from './environment_setting_drawer.vue';
import { useStore } from '@/stores';
import { formatDate } from '@/util/format_date';

const loading = ref(true)

type mainStore = {
    user_info: {
        id: number
        username: string
        group: []
    }
}

const mainStore = useStore() as mainStore

const groupId = mainStore.user_info.group
const userId = mainStore.user_info.id

const value = ref('')

const options: any = ref([])

// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['updateData'])


const projectId = ref(0)

const tableData = ref([])

const dialogVisible = ref(false)
// console.log('dialogAddValue1',environmentAddDialogVisible)
const modifyDialogVisible = ref(false)

const drawerVisible = ref(false)
const drawerData = ref([])
const drawerUrl = ref([])
const drawerAtuh = ref([])

// 标志drawer页是否打开
const triggerUpdate = ref(false)

const queryParams = reactive({
    projectNameQuery: '',
    page_size: 30,
    page: 1,
    groupId: groupId,
    userId: userId
})

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
}

const modifyEnvironmentId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentEnvironmentInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyEnvironmentId.value = id
    const environment = tableData.value.find((environment: any) => environment.id === id) // 在 tableData 中查找用户的详细信息
    if (environment) { currentEnvironmentInfo.value = environment } // 将找到的用户信息存储在 currentEnvironmentInfo 中
}

const drawerSetting = async (id: number) => {
    drawerVisible.value = true
    modifyEnvironmentId.value = id
    // 确保 Promise 解析完成
    await getEnvironmentDataList(id)
    triggerUpdate.value = !triggerUpdate.value // 改变triggerUpdate的值
}
const getProjectList = async () => {
    const res = await getAllProjectInfo(queryParams.page_size, queryParams.page, queryParams.groupId, queryParams.userId, queryParams.projectNameQuery)
    if (res && res.data.results.code === 2000) {
        let projectList = res.data.results.data
        // 排序
        const optionsValue = projectList.map((item: any) => {
            return {
                value: item.id,
                label: item.name
            }
        })
        options.value = optionsValue
        loading.value = false
    }
}

getProjectList()

const getEnvironmentList = async (val: any) => {
    const res = await getAllEnvironmentInfo(val)
    if (res && res.data.code === 2000) {
        let environmentList = res.data.data
        // 排序
        tableData.value = environmentList
    }
}

// 获取单个环境数据
const getEnvironmentDataList = async (envId: number) => {
    const res = await getEnvironmentData(envId, 100, 1)
    if (res && res.data.results.code === 2000) {
        drawerData.value = res.data.results.data
        const urlRes = await getEnvironmentUrl(envId)
        if ( urlRes && urlRes.data.code === 2000) {
            drawerUrl.value = urlRes.data.data
        }
        const envAuthRes = await getEnvironmentAuth(envId)
        if ( envAuthRes && envAuthRes.data.code === 2000) {
            drawerAtuh.value = envAuthRes.data.data
        }
    }
}

// 更新tab页数据
const updateEnvironmentDataList = (envId: number) => {
    getEnvironmentDataList(envId)
}


const updateEnvironmentList = (newEnvironments: any) => {
    if (newEnvironments) {
        tableData.value = newEnvironments.data.data
    }
    emit('updateData')
}


const disabledAddButton = ref(true)

const change = (val: any) => {
    if (val === undefined) {
        return
    }
    getEnvironmentList(val)
    projectId.value = val
    disabledAddButton.value = false
}

const clear = () => {
    // getEnvironmentList(0)
    tableData.value = []
    disabledAddButton.value = true
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>