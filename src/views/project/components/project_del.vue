<template>
  <el-popconfirm confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled" icon-color="#626AEF" title="确定删除吗？"
    @confirm="confirmEvent">
    <template #reference>
      <el-button type="danger" :icon="Delete" circle></el-button>
    </template>
  </el-popconfirm>
</template>
  
<script setup lang="ts">
import { InfoFilled, Delete, } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deleteProjectInfo } from '@/apis/project/delete_project';
import { getAllProjectInfo } from '@/apis/project/get_all_projectinfo';

// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['deleteConfirmed'])

// 定义一个 props，用于接收父组件传递的用户 id
const props = defineProps({
  projectId: {
    type: Number,
    required: true
  },
  groupId: {
    type: Array,
    required: true
  },
  userId: {
    type: Number,
    required: true
  }
})

const confirmEvent = async () => {
  if (props.projectId) {
    const response = await deleteProjectInfo(props.projectId)
    if (response && response.data.code === 2000) {
      const projectInfoResponse = await getAllProjectInfo(10, 1, props.groupId, props.userId, "")
      if (projectInfoResponse && projectInfoResponse.data.results.code === 2000) {
        ElMessage.success('操作成功！')
        // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
        emit('deleteConfirmed', projectInfoResponse)  // 触发 update 事件
      }
    }
  }
}
</script>