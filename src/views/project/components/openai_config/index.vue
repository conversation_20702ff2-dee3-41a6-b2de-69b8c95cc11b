<template>
  <div class="app-container">
    <el-row>
      <el-button type="primary" :icon="Plus" @click="createConfig">新增</el-button>
    </el-row>

    <el-table 
      ref="tableRef"
      :data="configList" 
      stripe
      style="width: 100%" 
      v-loading="loading">
      <el-table-column prop="name" label="名称" show-overflow-tooltip />
      <el-table-column label="类型">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.type === '1'">接口测试</el-tag>
          <el-tag type="warning" v-else-if="scope.row.type === '2'">页面测试</el-tag>
          <el-tag v-else>{{scope.row.type}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="值" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ getConfigValue(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="creator" label="创建人"/>
      <el-table-column label="创建时间">
        <template #default="scope">
          {{ formatDate(scope.row.create_time || scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <div class="operation-buttons">
            <el-button type="primary" :icon="Edit" link @click="editConfig(scope.row)" :disabled="scope.row.id === 1">编辑</el-button>
            <el-button type="danger" :icon="Delete" link @click="handleDelete(scope.row)" :disabled="scope.row.id === 1">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination demo-pagination-block">
      <el-pagination 
        v-model:current-page="currentPage" 
        v-model:page-size="pageSize" 
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next" 
        :total="total" 
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" 
      />
    </div>

    <!-- 新增/编辑配置对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑配置' : '新建配置'" 
      width="60%"
      destroy-on-close>
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="rules" 
        label-width="80px"
        label-position="right">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择类型">
            <el-option label="接口测试" value="1" />
            <el-option label="页面测试" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="值" prop="value">
          <el-input 
            v-model="valueString" 
            type="textarea" 
            :rows="8" 
            placeholder="请输入配置值(JSON格式)" 
            @input="handleValueChange" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useStore } from '@/stores'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { getAllOpenaiConfig } from '@/apis/project/openai_config/get_all_openai_config'
import { deleteOpenaiConfig } from '@/apis/project/openai_config/delete_openai_config'
import { getOpenaiConfig } from '@/apis/project/openai_config/get_openai_config'
import { postOpenaiConfig } from '@/apis/project/openai_config/post_openai_config'
import { putOpenaiConfig } from '@/apis/project/openai_config/put_openai_config'

// 接收projectId参数
const props = defineProps({
  projectId: {
    type: Number,
    required: true
  }
})

interface ProjectInfo {
  id: number;
  [key: string]: any;
}

interface UserInfo {
  username: string;
  [key: string]: any;
}

const store = useStore()
// 优先使用传入的projectId,如果没有则使用store中的
const projectId = ref(props.projectId || (store.project_info as ProjectInfo).id)

// 表格数据相关
const loading = ref(false)
const configList = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedConfigs = ref<any[]>([])
const tableRef = ref()

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const formData = reactive({
  id: 0,
  name: '',
  type: '2',
  value: '{}',  // 默认为空JSON对象字符串
  project: 0,
  creator: ''
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  value: [{
    validator: (rule: any, value: string, callback: any) => {
      if (!value) {
        callback(new Error('请输入配置值'))
        return
      }
      try {
        JSON.parse(value)
        callback()
      } catch (e) {
        callback(new Error('请输入合法的JSON格式'))
      }
    },
    trigger: 'blur'
  }]
}

// 使用computed处理值的字符串转换
const valueString = computed({
  get: () => {
    try {
      // 如果是对象，格式化显示
      if (typeof formData.value === 'object') {
        return JSON.stringify(formData.value, null, 2)
      }
      // 如果是字符串，尝试格式化显示
      const parsed = JSON.parse(formData.value)
      return JSON.stringify(parsed, null, 2)
    } catch (e) {
      return formData.value || '{}'
    }
  },
  set: (val) => {
    try {
      // 尝试解析输入的内容
      JSON.parse(val)
      formData.value = val
    } catch (e) {
      // 如果不是合法的JSON，保持原值
      console.warn('输入的不是合法的JSON格式')
    }
  }
})

// 处理值变化
const handleValueChange = (val: string) => {
  formData.value = val
}

// 加载配置列表
const loadConfigs = async () => {
  loading.value = true
  try {
    const response = await getAllOpenaiConfig(projectId.value)
    if (response?.data) {
      configList.value = response.data.data
      total.value = response.data.total
      // 清空选中状态
      if (tableRef.value) {
        tableRef.value.clearSelection()
      }
      selectedConfigs.value = []
    }
  } catch (error) {
    console.error('加载配置列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 分页相关方法
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadConfigs()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadConfigs()
}

// 获取配置值（隐藏显示）
const getConfigValue = (row: any) => {
  if (!row.value) return '******'
  
  try {
    const valueObj = typeof row.value === 'string' ? JSON.parse(row.value) : row.value
    return '******'
  } catch (e) {
    return '******'
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 创建新配置
const createConfig = () => {
  isEdit.value = false
  // 重置表单数据
  Object.assign(formData, {
    id: 0,
    name: '',
    type: '2',
    value: '{}',  // 默认空JSON对象字符串
    project: Number(projectId.value),
    creator: (store.user_info as UserInfo)?.username || ''
  })
  dialogVisible.value = true
}

// 编辑配置
const editConfig = async (row: any) => {
  isEdit.value = true
  try {
    const response = await getOpenaiConfig(row.id)
    if (response?.data?.data) {
      const data = response.data.data
      
      // 重置表单数据
      Object.assign(formData, {
        id: data.id,
        name: data.name,
        type: data.type,
        value: typeof data.value === 'object' ? JSON.stringify(data.value, null, 2) : data.value || '{}',
        project: data.project,
        creator: data.creator
      })

      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    ElMessage.error('获取配置详情失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 解析value为JSON对象
    const valueData = JSON.parse(formData.value)
    
    const submitData = {
      name: formData.name,
      type: formData.type,
      value: valueData,  // 提交JSON对象
      project: Number(projectId.value),
      creator: formData.creator
    }
    
    if (isEdit.value) {
      await putOpenaiConfig(formData.id, submitData)
      ElMessage.success('更新配置成功')
    } else {
      await postOpenaiConfig(submitData)
      ElMessage.success('创建配置成功')
    }
    
    dialogVisible.value = false
    loadConfigs()
  } catch (error) {
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      console.error('提交表单失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 删除配置
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    '确定要删除这个配置吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteOpenaiConfig(row.id)
      ElMessage.success('删除成功')
      loadConfigs()
    } catch (error) {
      console.error('删除失败:', error)
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (!selectedConfigs.value.length) {
    ElMessage.warning('请选择要删除的配置')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedConfigs.value.length} 个配置吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      for (const config of selectedConfigs.value) {
        await deleteOpenaiConfig(config.id)
      }
      ElMessage.success('批量删除成功')
      loadConfigs()
    } catch (error) {
      console.error('批量删除失败:', error)
    }
  })
}

onMounted(() => {
  loadConfigs()
})
</script>

<style lang="less" scoped>

</style>