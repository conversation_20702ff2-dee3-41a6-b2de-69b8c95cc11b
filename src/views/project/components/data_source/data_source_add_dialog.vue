<template>
    <el-dialog v-model="dialogVisible" title="新增数据源" width="800px" :before-close="handleClose">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="数据源名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入数据源名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="数据库类型" prop="database_type">
                        <el-select v-model="form.database_type" placeholder="请选择数据库类型" style="width: 100%">
                            <el-option label="MySQL" value="mysql"></el-option>
                            <el-option label="PostgreSQL" value="postgresql"></el-option>
                            <!-- <el-option label="Oracle" value="oracle"></el-option>
                            <el-option label="SQL Server" value="sqlserver"></el-option>
                            <el-option label="MongoDB" value="mongodb"></el-option> -->
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="主机地址" prop="host">
                        <el-input v-model="form.host" placeholder="请输入主机地址" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="端口" prop="port">
                        <el-input-number v-model="form.port" :min="1" :max="65535" 
                            placeholder="请输入端口号" style="width: 100%" />
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="数据库名" prop="database_name">
                        <el-input v-model="form.database_name" placeholder="请输入数据库名" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model="form.username" placeholder="请输入用户名" />
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="form.password" type="password" placeholder="请输入密码" 
                            show-password />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="状态">
                        <el-switch v-model="form.is_active" active-text="启用" inactive-text="禁用" />
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row :gutter="20" v-if="form.database_type === 'mysql'">
                <el-col :span="12">
                    <el-form-item label="字符集">
                        <el-select v-model="form.charset" placeholder="请选择字符集" style="width: 100%">
                            <el-option label="utf8mb4" value="utf8mb4"></el-option>
                            <el-option label="utf8" value="utf8"></el-option>
                            <el-option label="latin1" value="latin1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-form-item label="描述">
                <el-input v-model="form.desc" type="textarea" :rows="3" 
                    placeholder="请输入描述（可选）" />
            </el-form-item>
        </el-form>
        
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="info" @click="testConnection" :loading="testing">
                    测试连接
                </el-button>
                <el-button type="primary" @click="submitForm" :loading="submitting">
                    确定
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { postDataSource } from '@/apis/project/data_source/post_data_source'
import { testDataSource } from '@/apis/project/data_source/test_data_source'
import { getAllDataSource } from '@/apis/project/data_source/get_all_data_source'

// 定义props
const props = defineProps({
    projectId: Number,
    modelValue: Boolean
})

// 定义emit
const emit = defineEmits(['update:modelValue', 'updateDataSourceList'])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const testing = ref(false)
const submitting = ref(false)

// 表单数据
const form = reactive({
    name: '',
    database_type: '',
    host: '',
    port: 3306,
    database_name: '',
    username: '',
    password: '',
    desc: '',
    charset: 'utf8mb4',
    is_active: true,
    project: 0
})

// 表单验证规则
const rules: FormRules = {
    name: [
        { required: true, message: '请输入数据源名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    database_type: [
        { required: true, message: '请选择数据库类型', trigger: 'change' }
    ],
    host: [
        { required: true, message: '请输入主机地址', trigger: 'blur' }
    ],
    port: [
        { required: true, message: '请输入端口号', trigger: 'blur' },
        { type: 'number', min: 1, max: 65535, message: '端口号必须在 1-65535 之间', trigger: 'blur' }
    ],
    database_name: [
        { required: true, message: '请输入数据库名', trigger: 'blur' }
    ],
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
    ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
    dialogVisible.value = val
    if (val) {
        resetForm()
        form.project = props.projectId || 0
    }
})

watch(dialogVisible, (val) => {
    emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields()
    }
    Object.assign(form, {
        name: '',
        database_type: '',
        host: '',
        port: 3306,
        database_name: '',
        username: '',
        password: '',
        desc: '',
        charset: 'utf8mb4',
        is_active: true,
        project: props.projectId || 0
    })
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
}

// 测试连接
const testConnection = async () => {
    if (!formRef.value) return
    
    // 验证必填字段
    const validateResult = await formRef.value.validate().catch(() => false)
    if (!validateResult) {
        ElMessage.warning('请先完善连接信息')
        return
    }
    
    testing.value = true
    try {
        // 注意：在新增时，我们需要先创建数据源才能测试连接
        // 这是因为测试连接API需要数据源ID
        ElMessage.info('正在创建临时数据源并测试连接...')
        
        const tempData = { ...form }
        const createRes = await postDataSource(tempData)
        
        if (createRes && createRes.data.code === 2000) {
            const dataSourceId = createRes.data.data.id
            
            // 测试连接
            const testRes = await testDataSource(dataSourceId)
            if (testRes && testRes.data.code === 2000) {
                ElMessage.success('连接测试成功！数据源已创建')
                
                // 测试成功后刷新列表并关闭对话框
                emit('updateDataSourceList')
                handleClose()
            } else {
                ElMessage.error(testRes?.data?.msg || '连接测试失败')
            }
        }
    } catch (error) {
        // ElMessage.error('连接测试失败')
    } finally {
        testing.value = false
    }
}

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return
    
    const validateResult = await formRef.value.validate().catch(() => false)
    if (!validateResult) return
    
    submitting.value = true
    try {
        const res = await postDataSource(form)
        if (res && res.data.code === 2000) {
            ElMessage.success('新增数据源成功')
            
            // 通知父组件刷新列表
            emit('updateDataSourceList')
            
            handleClose()
        } else {
            ElMessage.error(res?.data?.msg || '新增数据源失败')
        }
    } catch (error) {
        ElMessage.error('新增数据源失败')
    } finally {
        submitting.value = false
    }
}
</script>

<style lang="less" scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style> 