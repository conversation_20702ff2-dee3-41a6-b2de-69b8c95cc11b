<template>
    <el-popconfirm
        title="确定要删除这个数据源吗？删除后无法恢复！"
        confirm-button-text="确定"
        cancel-button-text="取消"
        confirm-button-type="danger"
        @confirm="confirmDelete"
        width="280"
    >
        <template #reference>
            <el-button type="danger" :icon="Delete" circle size="small" 
                :loading="deleting" title="删除"></el-button>
        </template>
    </el-popconfirm>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { deleteDataSource } from '@/apis/project/data_source/delete_data_source'
import { getAllDataSource } from '@/apis/project/data_source/get_all_data_source'

// 定义props
const props = defineProps({
    dataSourceId: Number
})

// 定义emit
const emit = defineEmits(['deleteConfirmed'])

const deleting = ref(false)

// 确认删除
const confirmDelete = async () => {
    if (!props.dataSourceId) {
        ElMessage.error('数据源ID不存在')
        return
    }
    
    deleting.value = true
    try {
        const res = await deleteDataSource(props.dataSourceId)
        if (res && res.data.code === 2000) {
            ElMessage.success('删除数据源成功')
            
            // 通知父组件刷新列表
            emit('deleteConfirmed', res)
        } else {
            ElMessage.error(res?.data?.msg || '删除数据源失败')
        }
    } catch (error) {
        ElMessage.error('删除数据源失败')
    } finally {
        deleting.value = false
    }
}
</script> 