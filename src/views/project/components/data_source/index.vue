<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-col :span="4">
                <el-input placeholder="请输入数据源名称..." v-model="queryParams.search_name" clearable
                    @clear="handleSearch"></el-input>
            </el-col>
            <el-col :span="3">
                <el-select v-model="queryParams.database_type" placeholder="数据库类型" clearable
                    @clear="handleSearch">
                    <el-option label="MySQL" value="mysql"></el-option>
                    <el-option label="PostgreSQL" value="postgresql"></el-option>
                    <!-- <el-option label="Oracle" value="oracle"></el-option>
                    <el-option label="SQL Server" value="sqlserver"></el-option>
                    <el-option label="MongoDB" value="mongodb"></el-option> -->
                </el-select>
            </el-col>
            <el-col :span="3">
                <el-select v-model="queryParams.is_active" placeholder="状态" clearable @clear="handleSearch">
                    <el-option label="启用" :value="true"></el-option>
                    <el-option label="禁用" :value="false"></el-option>
                </el-select>
            </el-col>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button type="primary" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%" empty-text="暂无数据">
            <el-table-column prop="name" label="数据源名称" min-width="150" />

            <el-table-column prop="database_type" label="数据库类型" width="120">
                <template v-slot="{ row }">
                    <el-tag :type="getDatabaseTypeColor(row.database_type)" size="small">
                        {{ getDatabaseTypeName(row.database_type) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="host" label="主机地址" width="150" />
            <el-table-column prop="port" label="端口" width="80" />
            <el-table-column prop="database_name" label="数据库名" width="120" />
            <el-table-column prop="is_active" label="状态" width="80">
                <template v-slot="{ row }">
                    <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
                        {{ row.is_active ? '启用' : '禁用' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="desc" label="描述" min-width="150">
                <template v-slot="{ row }">
                    <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;">
                        <el-tooltip v-if="(row.desc || row.description) && (row.desc || row.description).length > 15" :teleported="false" 
                            :content="row.desc || row.description" placement="top">
                            {{ row.desc || row.description }}
                        </el-tooltip>
                        <template v-else>
                            {{ row.desc || row.description || '-' }}
                        </template>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="creator" label="创建人" width="100" />
            <el-table-column prop="action" label="操作" align="center" width="200">
                <template v-slot="scope">
                    <el-button type="primary" :icon="Connection" circle size="small" 
                        @click="testConnection(scope.row.id)" :loading="testingId === scope.row.id"
                        title="测试连接"></el-button>
                    <el-button type="info" :icon="Search" circle size="small" 
                        @click="openSqlTest(scope.row)" title="SQL测试"></el-button>
                    <el-button type="primary" :icon="Edit" circle size="small" 
                        @click="dialogModify(scope.row.id)"></el-button>
                    <data_source_del :dataSourceId="scope.row.id" @deleteConfirmed="handleRefresh">
                    </data_source_del>
                </template>
            </el-table-column>
        </el-table>
        
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    
    <!-- 新增对话框 -->
    <data_source_add_dialog :projectId="currentProjectId" v-model="dialogVisible" 
        @updateDataSourceList="handleRefresh">
    </data_source_add_dialog>
    
    <!-- 修改对话框 -->
    <data_source_modify_dialog :projectId="currentProjectId" v-model="modifyDialogVisible"
        :modifyDataSourceId='modifyDataSourceId' :dataSourceInfo="currentDataSourceInfo" 
        @updateDataSourceList="handleRefresh">
    </data_source_modify_dialog>
    
    <!-- SQL测试对话框 -->
    <sql_test_dialog v-model="sqlTestVisible" :dataSourceInfo="currentDataSourceInfo">
    </sql_test_dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Edit, Plus, Connection } from '@element-plus/icons-vue'
import { getAllDataSource } from '@/apis/project/data_source/get_all_data_source'
import { testDataSource } from '@/apis/project/data_source/test_data_source'
import data_source_add_dialog from './data_source_add_dialog.vue'
import data_source_modify_dialog from './data_source_modify_dialog.vue'
import data_source_del from './data_source_del.vue'
import sql_test_dialog from './sql_test_dialog.vue'

// 定义props
const props = defineProps({
    projectId: {
        type: Number,
        default: 0
    }
})

// 定义emit
const emit = defineEmits(['updateData'])

// 响应式数据
const loading = ref(false)
const testingId = ref(0)
const currentProjectId = computed(() => props.projectId || 0)

const queryParams = reactive({
    search_name: '',
    database_type: '',
    is_active: null as boolean | null,
    page_size: 10,
    page: 1
})

const total = ref(0)
const tableData = ref<any[]>([])

const dialogVisible = ref(false)
const modifyDialogVisible = ref(false)
const sqlTestVisible = ref(false)

const modifyDataSourceId = ref(0)
const currentDataSourceInfo = ref({})

// 获取数据源列表
const getDataSourceList = async () => {
    if (!currentProjectId.value || currentProjectId.value <= 0) {
        loading.value = false
        return
    }
    
    loading.value = true
    try {
        // 调试信息已移除
        
        const res = await getAllDataSource(
            currentProjectId.value, 
            queryParams.database_type || '', 
            queryParams.search_name || '', 
            queryParams.is_active !== null && queryParams.is_active !== undefined ? queryParams.is_active : '',
            queryParams.page, 
            queryParams.page_size
        )
        
        if (res && res.data) {
            // 根据实际接口返回的数据结构解析
            if (res.data.results && res.data.results.code === 2000) {
                const data = res.data.results.data
                // console.log('数据源列表:', data)
                if (Array.isArray(data)) {
                    tableData.value = data
                    total.value = res.data.count || data.length
                } else {
                    tableData.value = []
                    total.value = 0
                }
            } else {
                tableData.value = []
                total.value = 0
            }
        } else {
            tableData.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取数据源列表失败:', error)
        tableData.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    queryParams.page = 1
    getDataSourceList()
}

// 刷新列表
const handleRefresh = () => {
    getDataSourceList()
    emit('updateData')
}

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
}

// 控制修改对话框
const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyDataSourceId.value = id
    const dataSource = tableData.value.find((item: any) => item.id === id)
    if (dataSource) {
        currentDataSourceInfo.value = dataSource
    }
}

// 测试连接
const testConnection = async (id: number) => {
    testingId.value = id
    try {
        const res = await testDataSource(id)
        if (res && res.data.code === 2000) {
            ElMessage.success('连接测试成功！')
        } else {
            ElMessage.error(res?.data?.msg || '连接测试失败')
        }
    } catch (error) {
        // ElMessage.error('连接测试失败')
    } finally {
        testingId.value = 0
    }
}

// 打开SQL测试
const openSqlTest = (dataSource: any) => {
    currentDataSourceInfo.value = dataSource
    sqlTestVisible.value = true
}

// 分页处理
const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getDataSourceList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getDataSourceList()
}

// 获取数据库类型显示名称
const getDatabaseTypeName = (type: string) => {
    const typeMap: { [key: string]: string } = {
        'mysql': 'MySQL',
        'postgresql': 'PostgreSQL',
        'oracle': 'Oracle',
        'sqlserver': 'SQL Server',
        'mongodb': 'MongoDB'
    }
    return typeMap[type] || type
}

// 获取数据库类型颜色
const getDatabaseTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
        'mysql': 'primary',
        'postgresql': 'success',
        'oracle': 'warning',
        'sqlserver': 'info',
        'mongodb': 'danger'
    }
    return colorMap[type] || 'default'
}

// 组件挂载后初始化
onMounted(() => {
    if (currentProjectId.value > 0) {
        getDataSourceList()
    }
})
</script>

<style lang="less" scoped>
.app-container {
    .header {
        margin-bottom: 20px;
        align-items: center;
    }
}

.demo-pagination-block {
    margin-top: 20px;
    margin-bottom: 11px;
}
</style>
