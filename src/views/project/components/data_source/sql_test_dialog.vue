<template>
    <el-dialog v-model="dialogVisible" title="SQL查询测试" width="1000px" :before-close="handleClose">
        <div class="sql-test-container">
            <!-- 数据源信息展示 -->
            <el-card class="datasource-info" shadow="never">
                <div class="info-header">
                    <h4>数据源信息</h4>
                </div>
                <div class="info-content">
                    <el-descriptions :column="4" size="small">
                        <el-descriptions-item label="数据源名称">
                            <el-tag type="primary">{{ dataSourceInfo?.name || '-' }}</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="数据库类型">
                            <el-tag :type="getDatabaseTypeColor(dataSourceInfo?.database_type || '')">
                                {{ getDatabaseTypeName(dataSourceInfo?.database_type || '') }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="主机地址">
                            {{ dataSourceInfo?.host || '-' }}:{{ dataSourceInfo?.port || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="数据库名">
                            {{ dataSourceInfo?.database_name || '-' }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-card>
            
            <!-- SQL编辑器 -->
            <el-card class="sql-editor" shadow="never">
                <div class="editor-header">
                    <h4>SQL查询</h4>
                    <div class="editor-actions">
                        <el-button type="primary" @click="executeSql" :loading="executing" 
                            :disabled="!sqlText.trim()">
                            <el-icon><VideoPlay /></el-icon>
                            执行查询
                        </el-button>
                        <el-button @click="clearSql">清空</el-button>
                    </div>
                </div>
                <el-input v-model="sqlText" type="textarea" :rows="8" 
                    placeholder="请输入SQL查询语句，例如：SELECT * FROM table_name LIMIT 10"
                    class="sql-textarea" />
            </el-card>
            
            <!-- 查询结果 -->
            <el-card class="query-result" shadow="never" v-if="showResult">
                <div class="result-header">
                    <h4>查询结果</h4>
                    <div class="result-info">
                        <el-tag v-if="resultData.length > 0" type="success">
                            共 {{ resultData.length }} 条记录
                        </el-tag>
                        <el-tag v-else type="info">无数据</el-tag>
                        <span class="execution-time" v-if="executionTime">
                            执行时间: {{ executionTime }}ms
                        </span>
                    </div>
                </div>
                
                <!-- 结果表格 -->
                <el-table v-if="resultData.length > 0" :data="resultData" 
                    stripe border height="300" size="small">
                    <el-table-column v-for="column in resultColumns" :key="column"
                        :prop="column" :label="column" min-width="120" show-overflow-tooltip />
                </el-table>
                
                <!-- 无数据提示 -->
                <div v-else class="no-data">
                    <el-empty description="查询结果为空" :image-size="100" />
                </div>
            </el-card>
            
            <!-- 错误信息 -->
            <el-card class="error-info" shadow="never" v-if="errorMessage">
                <div class="error-header">
                    <h4>执行错误</h4>
                </div>
                <el-alert :title="errorMessage" type="error" show-icon :closable="false" />
            </el-card>
        </div>
        
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay } from '@element-plus/icons-vue'
import { execSql } from '@/apis/project/data_source/exec_sql'

// 定义props
const props = defineProps({
    modelValue: Boolean,
    dataSourceInfo: Object
})

// 定义emit
const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const executing = ref(false)
const showResult = ref(false)
const sqlText = ref('')
const resultData = ref<any[]>([])
const resultColumns = ref<string[]>([])
const errorMessage = ref('')
const executionTime = ref(0)

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
    dialogVisible.value = val
    if (val) {
        resetData()
    }
})

watch(dialogVisible, (val) => {
    emit('update:modelValue', val)
})

// 重置数据
const resetData = () => {
    sqlText.value = ''
    resultData.value = []
    resultColumns.value = []
    errorMessage.value = ''
    showResult.value = false
    executionTime.value = 0
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
}

// 执行SQL
const executeSql = async () => {
    if (!sqlText.value.trim()) {
        ElMessage.warning('请输入SQL查询语句')
        return
    }
    
    const dataSourceInfo = props.dataSourceInfo as any
    if (!dataSourceInfo || !dataSourceInfo.id) {
        ElMessage.error('数据源信息不存在')
        return
    }
    
    executing.value = true
    errorMessage.value = ''
    showResult.value = false
    
    const startTime = Date.now()
    
    try {
        const res = await execSql(dataSourceInfo.id, { sql: sqlText.value.trim() })
        
        executionTime.value = Date.now() - startTime
        
        if (res && res.data.code === 2000) {
            const data = res.data.data
            
            // 根据API文档，查询结果在data.result中
            const queryResult = data?.result || data
            
            if (queryResult && Array.isArray(queryResult) && queryResult.length > 0) {
                resultData.value = queryResult
                resultColumns.value = Object.keys(queryResult[0])
                showResult.value = true
                ElMessage.success('查询执行成功')
            } else {
                resultData.value = []
                resultColumns.value = []
                showResult.value = true
                ElMessage.info('查询执行成功，但无返回数据')
            }
        } else {
            errorMessage.value = res?.data?.msg || 'SQL执行失败'
            showResult.value = false
        }
    } catch (error: any) {
        errorMessage.value = error?.response?.data?.msg || error?.message || 'SQL执行失败'
        showResult.value = false
    } finally {
        executing.value = false
    }
}

// 清空SQL
const clearSql = () => {
    sqlText.value = ''
    resetData()
}

// 获取数据库类型显示名称
const getDatabaseTypeName = (type: string) => {
    const typeMap: { [key: string]: string } = {
        'mysql': 'MySQL',
        'postgresql': 'PostgreSQL',
        'oracle': 'Oracle',
        'sqlserver': 'SQL Server',
        'mongodb': 'MongoDB'
    }
    return typeMap[type] || type
}

// 获取数据库类型颜色
const getDatabaseTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
        'mysql': 'primary',
        'postgresql': 'success',
        'oracle': 'warning',
        'sqlserver': 'info',
        'mongodb': 'danger'
    }
    return colorMap[type] || 'default'
}
</script>

<style lang="less" scoped>
.sql-test-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.datasource-info {
    .info-header {
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
    }
}

.sql-editor {
    .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
        
        .editor-actions {
            display: flex;
            gap: 8px;
        }
    }
    
    .sql-textarea {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        
        :deep(.el-textarea__inner) {
            font-family: inherit;
        }
    }
}

.query-result {
    .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
        
        .result-info {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .execution-time {
                color: #909399;
                font-size: 12px;
            }
        }
    }
    
    .no-data {
        text-align: center;
        padding: 40px 0;
    }
}

.error-info {
    .error-header {
        margin-bottom: 12px;
        
        h4 {
            margin: 0;
            color: #F56C6C;
            font-size: 16px;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style> 