<template>
  <el-dialog v-model="dialogVisible" title="添加成员" width="65%" :before-close="handleClose">
    <div class="app-container">
      <el-row :gutter="20" class="header">
        <el-col :span="7">
          <el-input placeholder="请输入用户名..." v-model="queryParams.query_name" @clear="getCanAddMembersList"></el-input>
        </el-col>
        <el-button type="primary" :icon="Search" @click="getCanAddMembersList">搜索</el-button>
      </el-row>
      <el-table ref="multipleTableRef" :row-key="(row) => row.id" v-loading="loading" :data="tableData"
        @selection-change="handleSelectionChange" stripe style="width: 100%">
        <el-table-column type="selection" :reserve-selection="true" width="55" />
        <el-table-column prop="username" label="用户名" width="auto" />
        <el-table-column prop="email" label="电子邮箱" width="auto" />
        <el-table-column prop="name" label="姓名" width="auto" />
        <el-table-column prop="job_title" label="岗位" width="auto" />
      </el-table>
      <div class="demo-pagination-block">
        <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus'
import { getCanAddMembers } from '@/apis/project/member/get_can_add_members'
import { postMemberInfo } from '@/apis/project/member/post_member'
import { getProjectInfo } from '@/apis/project/get_projectinfo';
import { ElTable } from 'element-plus'


const loading = ref(true)

const queryParams = reactive({
  page_size: 10,
  page: 1,
  query_name: ''
})

interface User {
  name: string,
  disabled?: boolean
}
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const total = ref(0)
const tableData = ref([])

const multipleSelection = ref<User[]>([])

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  projectInfo: Object,
  projectId: {
    type: Number,
    required: true
  }
})

const projectInfo:any = ref({})
const dialogVisible = ref(false)

const getCanAddMembersList = async () => {
  const memberIds = projectInfo.value.user_info ? projectInfo.value.user_info.map((member: any) => member.member_id).join(',') : '';
  const res = await getCanAddMembers(queryParams.page_size, queryParams.page, memberIds, queryParams.query_name)
  if (res) {
    tableData.value = res.data.results.data
    total.value = res.data.count
    loading.value = false
  }
}

watchEffect(() => {
  dialogVisible.value = props.modelValue
  projectInfo.value = props.projectInfo
  // 当对话框打开再请求
  if (dialogVisible.value === true) {
    getCanAddMembersList()
  }
})


const emit = defineEmits(['update:modelValue', 'updateMemberList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


const submitForm = async () => {

  // 只需要传数组id给后端
  const ids = multipleSelection.value.map((item: any) => item.id)

  const response = await postMemberInfo(props.projectId ,ids)
  if (response && response.data.code === 2000) {
    const memberInfoResponse = await getProjectInfo(props.projectId)
    if (memberInfoResponse && memberInfoResponse.data.code === 2000) {
      ElMessage.success('操作成功！')
      // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
      emit('updateMemberList', memberInfoResponse)  // 触发 update 事件
    }
  }
  handleClose()


}

const handleSizeChange = (pageSize: number) => {
  queryParams.page = 1
  queryParams.page_size = pageSize
  getCanAddMembersList()
}

const handleCurrentChange = (pageNum: number) => {
  queryParams.page = pageNum
  getCanAddMembersList()
}

const handleSelectionChange = (val: User[]) => {
  multipleSelection.value = val
}

</script>
<style scoped>
</style>