<template>
  <el-popconfirm confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled" icon-color="#626AEF" title="确定移除吗？"
    @confirm="confirmEvent">
    <template #reference>
      <el-button type="danger" size="small" :disabled="isLastMember" :title="isLastMember ? '至少保留一名成员' : ''">移除</el-button>
    </template>
  </el-popconfirm>
</template>
  
<script setup lang="ts">
import { InfoFilled, Minus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deleteMemberInfo } from '@/apis/project/member/delete_member';
import { getProjectInfo } from '@/apis/project/get_projectinfo';

// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['deleteConfirmed'])

// 定义一个 props，用于接收父组件传递的用户 id
const props = defineProps({
  memberId: {
    type: Number,
    required: true
  },
  projectId: {
    type: Number,
    required: true
  },
  isLastMember: {
    type: Boolean,
    default: false
  }
})

const confirmEvent = async () => {
  if (props.isLastMember) {
    ElMessage.warning('至少保留一名成员，不能移除最后一名成员。')
    return
  }
  if (props.memberId) {
    const response = await deleteMemberInfo(props.projectId, props.memberId)
    if (response && response.data.code === 2000) {
      const MemberInfoResponse = await getProjectInfo(props.projectId)
      if (MemberInfoResponse && MemberInfoResponse.data.code === 2000) {
        ElMessage.success('操作成功！')
        // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
        emit('deleteConfirmed', MemberInfoResponse)  // 触发 update 事件
      }
    }
  }
}
</script>