<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-col :span="4">
                <el-input placeholder="请输入账号名..." v-model="queryParams.projectNameQuery" clearable></el-input>
            </el-col>
            <!-- <el-button type="primary" :icon="Search" @click="getUserList">搜索</el-button> -->
            <el-button type="primary" :icon="Plus" @click="dialogAdd">添加</el-button>
        </el-row>
        <el-table :data="queryParams.filteredData" stripe style="width: 100%">
            <el-table-column prop="member_username" label="账号">
            </el-table-column>
            <el-table-column prop="member_name" label="姓名" />
            <el-table-column prop="member_job" label="岗位" />
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <member_del :projectId="projectId" :memberId="scope.row.member_id" @deleteConfirmed="updateMemberList" :isLastMember="tableData.length === 1">
                    </member_del>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <member_add_dialog :projectInfo="projectInfo" v-model="dialogVisible" :projectId="projectId" @updateMemberList="updateMemberList">
    </member_add_dialog>
</template>
  
<script lang="ts" setup>
import { ref, watchEffect, reactive, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue';
import member_del from './member_del.vue';
import member_add_dialog from './member_add_dialog.vue'
import { getProjectInfo } from '@/apis/project/get_projectinfo';


// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['updateData'])

const props = defineProps({
    projectInfo: Object,
    projectId: Number

})

const projectInfo: any = ref({})
const projectId: any = ref(0)
const tableData = ref([])

watchEffect(() => {
    projectInfo.value = props.projectInfo
    projectId.value = props.projectId
    tableData.value = projectInfo.value.user_info || []
})

// 前端搜索
const queryParams: any = reactive({
    projectNameQuery: '',
    filteredData: computed(() => {
        if (!queryParams.projectNameQuery) {
            return tableData.value;
        }
        return tableData.value.filter((item: any) => item.member_username.includes(queryParams.projectNameQuery));
    }),
})

const dialogVisible = ref(false)

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
}


const getProject = async () => {
    const res = await getProjectInfo(projectId)
    if (res && res.data.code === 2000) {
        projectInfo.value = res.data.data
    }
    emit('updateData')
}

const updateMemberList = (newMembers: any) => {
    if (newMembers) {
        tableData.value = newMembers.data.data.user_info;
    }
    emit('updateData')
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>