<template>
  <el-dialog v-model="modifyDialogVisible" title="编辑项目" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="80px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item label="项目描述" prop="desc">
        <el-input v-model="ruleForm.desc" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { putProject } from '@/apis/project/put_project'
import { getAllProjectInfo } from '@/apis/project/get_all_projectinfo'


// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  modifyProjectId: Number,
  projectInfo: Object,
  groupId:{
    type:Array,
    required: true
  },
  userId: {
    type: Number,
    required: true
  }
})

const modifyDialogVisible = ref(false)

const emit = defineEmits(['update:modelValue', 'updateProjectList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

interface RuleForm {
  name: string
  desc: string
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  name: '',
  desc: ''
})

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入项目名', trigger: 'blur' },
  ],
  desc: [
    { message: '请输入项目描述', trigger: 'blur' },
  ],
})

watchEffect(() => {
  modifyDialogVisible.value = props.modelValue
  Object.assign(ruleForm, props.projectInfo)
})


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await putProject(props.modifyProjectId as any, ruleForm)
      if (response && response.data.code === 2000) {
        const projectInfoResponse = await getAllProjectInfo(10, 1, props.groupId , props.userId, "")
        if (projectInfoResponse && projectInfoResponse.data.results.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateProjectList', projectInfoResponse)  // 触发 update 事件
        }
      }
    handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}


</script>
<style scoped></style>