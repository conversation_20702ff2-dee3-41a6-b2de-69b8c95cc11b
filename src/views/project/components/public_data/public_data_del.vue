<template>
  <el-popconfirm confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled" icon-color="#626AEF" title="确定删除吗？"
    @confirm="confirmEvent">
    <template #reference>
      <el-button type="danger" :icon="Delete" circle></el-button>
    </template>
  </el-popconfirm>
</template>
  
<script setup lang="ts">
import { InfoFilled, Delete, } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deletePublicDataInfo } from '@/apis/project/public_data/delete_public_data';
import { getPublicDataInfo } from '@/apis/project/public_data/get_public_data'


// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['deleteConfirmed'])

// 定义一个 props，用于接收父组件传递的用户 id
const props = defineProps({
  public_dataId: {
    type: Number,
    required: true
  },
  projectId: {
    type: Number,
    required: true
  }
})

const confirmEvent = async () => {
  if (props.public_dataId) {
    const response = await deletePublicDataInfo(props.public_dataId, props.projectId)
    if (response && response.data.code === 2000) {
      const public_dataInfoResponse = await getPublicDataInfo(props.projectId)
      if (public_dataInfoResponse && public_dataInfoResponse.data.code === 2000) {
        ElMessage.success('操作成功！')
        // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
        emit('deleteConfirmed', public_dataInfoResponse)  // 触发 update 事件
      }
    }
  }
}
</script>