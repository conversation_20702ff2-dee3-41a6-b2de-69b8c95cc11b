<template>
    <div class="app-container">
        <el-row>
            <el-button type="primary" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column prop="key" label="变量名" />
            <el-table-column prop="type" label="类型">
                <template v-slot="{ row }">
                    {{ getTypeName(row.type) }}
                </template>
            </el-table-column>
            <el-table-column prop="variable_type" label="变量值类型">
                <template v-slot="{ row }">
                    <el-tag size="small">{{ getVariableTypeName(row.variable_type) }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="变量值">
                <template v-slot="{ row }">
                    <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;">
                        <el-tooltip v-if="row.value.length > 15" :teleported="false" :content="row.value" placement="top">
                            {{ row.value }}
                        </el-tooltip>
                        <template v-else>
                            {{ row.value }}
                        </template>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="desc" label="描述" />
            <el-table-column prop="creator" label="创建人" />
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <public_data_del :projectId="projectId" :public_dataId="scope.row.id"
                        @deleteConfirmed="updatePublicDataList">
                    </public_data_del>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <public_data_add_dialog :projectId="projectId" v-model="dialogVisible" @updatePublicDataList="updatePublicDataList">
    </public_data_add_dialog>
    <public_data_modify_dialog :projectId="projectId" v-model="modifyDialogVisible"
        :modifyPublicDataId='modifyPublicDataId' :public_dataInfo="currentPublicDataInfo"
        @updatePublicDataList="updatePublicDataList"></public_data_modify_dialog>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import { Edit, Plus } from '@element-plus/icons-vue';
import public_data_add_dialog from './public_data_add_dialog.vue'
import public_data_del from './public_data_del.vue';
import public_data_modify_dialog from './public_data_modify_dialog.vue';



// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['updateData'])

const props = defineProps({
    projectId: Number,
    projectPublicDataInfo: Object
})
const projectId = ref(0)
const publicData: any = ref([])
const tableData = ref([])


watchEffect(() => {
    projectId.value = props.projectId || 0
    publicData.value = props.projectPublicDataInfo
    tableData.value = publicData.value
})


const dialogVisible = ref(false)
// console.log('dialogAddValue1',public_dataAddDialogVisible)
const modifyDialogVisible = ref(false)


// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
}

const modifyPublicDataId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentPublicDataInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyPublicDataId.value = id
    const public_data = tableData.value.find((public_data: any) => public_data.id === id) // 在 tableData 中查找用户的详细信息
    if (public_data) { currentPublicDataInfo.value = public_data } // 将找到的用户信息存储在 currentPublicDataInfo 中
}

const updatePublicDataList = (newPublicDatas: any) => {
    if (newPublicDatas) {
        tableData.value = newPublicDatas.data.data
    }
    // emit('updateData')
}

// 根据库里写死
const allTypes = [{ id: "1", name: "变量" }, { id: "2", name: "BASIC_AUTH" }, { id: "3", name: "BEARER_TOKEN" }, { id: "4", name: "BASE_URL" }, { id: "5", name: "UI_TEST" }]
const variableTypes = [{ id: "1", name: "string" }, { id: "2", name: "int" }, { id: "3", name: "array" }, { id: "4", name: "boolean" }, { id: "5", name: "object" }, { id: "6", name: "number" }, { id: "7", name: "sql" }]

const getTypeName = (typeId: string) => {
    const type = allTypes.find(t => t.id === typeId);
    return type ? type.name : '';
}

const getVariableTypeName = (variableTypeId: string) => {
    const variableType = variableTypes.find(t => t.id === variableTypeId);
    if (variableType) {
        return variableType ? variableType.name : '';
    } else {
        return '/'
    }

}
</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>