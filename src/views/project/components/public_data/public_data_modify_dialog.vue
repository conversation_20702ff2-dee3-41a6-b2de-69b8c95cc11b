<template>
  <el-dialog v-model="modifyDialogVisible" title="编辑变量" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="80px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="变量名" prop="key">
        <el-input v-model="ruleForm.key" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="ruleForm.type" placeholder="请选择类型" :disabled="true">
          <el-option v-for="type in allTypes" :label="type.name" :value="type.id" :key="type.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="值类型" prop="variable_type" v-if="ruleForm.type === '1'">
        <el-select v-model="ruleForm.variable_type" placeholder="请选择值类型">
          <el-option v-for="type in variable_types" :label="type.name" :value="type.id" :key="type.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="账号" prop="username" v-if="ruleForm.type === '2'">
        <el-input v-model="ruleForm.username" />
      </el-form-item>
      <el-form-item label="密码" prop="password" v-if="ruleForm.type === '2'">
        <el-input v-model="ruleForm.password" />
      </el-form-item>
      <el-form-item label="变量值" prop="value" v-if="ruleForm.type !== '2'">
        <el-input v-model="ruleForm.value" />
      </el-form-item>
      <el-form-item label="描述" prop="desc">
        <el-input v-model="ruleForm.desc" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup lang='ts'>
import { ref, watch, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { putPublicData } from '@/apis/project/public_data/put_public_data'
import { getPublicDataInfo } from '@/apis/project/public_data/get_public_data'
import { useStore } from '@/stores'

const mainStore = useStore()
const userName: string = (mainStore.user_info as any).username

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  modifyPublicDataId: {
    type: Number,
    required: true
  },
  public_dataInfo: Object,
  projectId: {
    type: Number,
    required: true
  }
})

const modifyDialogVisible = ref(false)

const emit = defineEmits(['update:modelValue', 'updatePublicDataList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}

interface RuleForm {
  key: string
  type: string
  value: string
  desc: string
  creator: string
  project: number
  username: string
  password: string
  variable_type?: string | null
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  key: '',
  type: '',
  value: '',
  desc: '',
  username: '',
  password: '',
  creator: '',
  project: props.projectId
})

const rules = reactive<FormRules<RuleForm>>({
  key: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'blur' },
  ],
  variable_type: [
    { required: true, message: '请选择变量类型', trigger: 'blur' },
  ],
  value: [
    { required: true, message: '请输入变量值', trigger: 'blur' },
  ],
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
})

// 根据库里写死
const allTypes = [{ id: "1", name: "变量" }, { id: "2", name: "BASIC_AUTH" }, { id: "3", name: "BEARER_TOKEN" }, { id: "4", name: "BASE_URL" }, { id: "5", name: "UI_TEST" }]
const variable_types = [{ id: "1", name: "string" }, { id: "2", name: "int" }, { id: "3", name: "array" }, { id: "4", name: "boolean" }, { id: "5", name: "object" }, { id: "6", name: "number" }, { id: "7", name: "sql" }]

watch(() => props.modelValue, (newValue, oldValue) => {
  modifyDialogVisible.value = newValue
  Object.assign(ruleForm, props.public_dataInfo)

  // 检查类型是否为2，并且确保只有在类型为2时才解析value
  if (Number(ruleForm.type) === 2 && ruleForm.value && typeof ruleForm.value === 'string') {
    const valueObj = JSON.parse(ruleForm.value)
    const keys = Object.keys(valueObj)
    if (keys.length > 0) {
      ruleForm.username = keys[0]
      ruleForm.password = valueObj[keys[0]]
    }
  } else {
    // 当类型不是2时，确保value字段从public_dataInfo中正确恢复，但仅当类型实际发生变化时
    if (oldValue && Number(ruleForm.type) !== 2) {
      ruleForm.value = props.public_dataInfo?.value || ''
    }
    ruleForm.username = ''
    ruleForm.password = ''
  }
})
const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      if (ruleForm.username && ruleForm.password) {
        ruleForm.value = `{"${ruleForm.username}":"${ruleForm.password}"}`
      }
      const response = await putPublicData(props.modifyPublicDataId, ruleForm)
      if (response && response.data.code === 2000) {
        const public_dataInfoResponse = await getPublicDataInfo(props.projectId)
        if (public_dataInfoResponse && public_dataInfoResponse.data.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updatePublicDataList', public_dataInfoResponse)  // 触发 update 事件
        }
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}


</script>
<style scoped></style>