<template>
  <el-dialog v-model="dialogVisible" title="新增变量" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="80px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="变量名" prop="key">
        <el-input v-model="ruleForm.key" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="ruleForm.type" placeholder="请选择类型">
          <el-option v-for="type in allTypes" :label="type.name" :value="type.id" :key="type.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="值类型" prop="variable_type" v-if="ruleForm.type === '1'">
        <el-select v-model="ruleForm.variable_type" placeholder="请选择值类型">
          <el-option v-for="type in variable_types" :label="type.name" :value="type.id" :key="type.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="账号" prop="username" v-if="ruleForm.type === '2'">
        <el-input v-model="ruleForm.username"/>
      </el-form-item>
      <el-form-item label="密码" prop="password" v-if="ruleForm.type === '2'">
        <el-input v-model="ruleForm.password"/>
      </el-form-item>
      <el-form-item label="变量值" prop="value" v-if="ruleForm.type !== '2'">
        <el-input v-model="ruleForm.value" />
      </el-form-item>
      <el-form-item label="描述" prop="desc">
        <el-input v-model="ruleForm.desc" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postPublicData } from '@/apis/project/public_data/post_public_data'
import { getPublicDataInfo } from '@/apis/project/public_data/get_public_data'
import { useStore } from '@/stores'

const mainStore = useStore()
const userName: string = (mainStore.user_info as any).username

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  projectId: {
    type: Number,
    required: true
  }
})
// 根据库里写死
const allTypes = [{ id: "1", name: "变量" }, { id: "2", name: "BASIC_AUTH" }, { id: "3", name: "BEARER_TOKEN" }, { id: "4", name: "BASE_URL" }, { id: "5", name: "UI_TEST" }]
const variable_types = [{ id: "1", name: "string" }, { id: "2", name: "int" }, { id: "3", name: "array" }, { id: "4", name: "boolean" }, { id: "5", name: "object" }, { id: "6", name: "number" }]
const dialogVisible = ref(false)
watchEffect(() => {
  dialogVisible.value = props.modelValue
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.key = ''
    ruleForm.type = ''
    ruleForm.desc = ''
    ruleForm.value = ''
    ruleForm.username = ''
    ruleForm.password = ''
    ruleForm.variable_type = null
  }
})

const emit = defineEmits(['update:modelValue', 'updatePublicDataList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  key: string
  type: string
  value: string
  desc: string
  creator: string
  project: number
  username: string
  password: string
  variable_type?: string | null
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  key: '',
  type: '',
  value: '',
  desc: '',
  username: '',
  password: '',
  creator: userName,
  project: props.projectId
})

const rules = reactive<FormRules<RuleForm>>({
  key: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'blur' },
  ],
  variable_type: [
    { required: true, message: '请选择变量类型', trigger: 'blur' },
  ],
  value: [
    { required: true, message: '请输入变量值', trigger: 'blur' },
  ],
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
})



const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      // 如果2类型，拼成value传到后端
      if (ruleForm.username && ruleForm.password) {
        ruleForm.value = `{"${ruleForm.username}":"${ruleForm.password}"}`
      }
      const response = await postPublicData(ruleForm)
      if (response && response.data.code === 2000) {
        const publicDataInfoResponse = await getPublicDataInfo(props.projectId)
        if (publicDataInfoResponse && publicDataInfoResponse.data.code === 2000) {
          ElMessage.success(publicDataInfoResponse.data.msg)
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updatePublicDataList', publicDataInfoResponse)  // 触发 update 事件
        }
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}

</script>
<style scoped></style>