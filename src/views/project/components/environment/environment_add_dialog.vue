<template>
  <el-dialog v-model="dialogVisible" title="新增环境" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="80px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="环境名称" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item label="环境描述" prop="desc">
        <el-input v-model="ruleForm.desc" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postEnvironment } from '@/apis/project/environment/post_environment'
import { getAllEnvironmentInfo } from '@/apis/project/environment/get_all_environmentinfo'
import { useStore } from '@/stores'

const mainStore = useStore()
const userName:string = (mainStore.user_info as any).username

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean,
  projectId: {
    type: Number,
    required: true
  }
})

const dialogVisible = ref(false)

watchEffect(() => {
  dialogVisible.value = props.modelValue
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.name = ''
    ruleForm.desc = ''
  }
})

const emit = defineEmits(['update:modelValue', 'updateEnvironmentList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  name: string
  desc: string
  creator: string
  project: number
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  name: '',
  desc: '',
  creator: userName || '',
  project: props.projectId
})

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入环境名', trigger: 'blur' },
  ]
})


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await postEnvironment(ruleForm)
      if (response && response.data.code === 2000) {
        const environmentInfoResponse = await getAllEnvironmentInfo(props.projectId)
        if (environmentInfoResponse && environmentInfoResponse.data.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateEnvironmentList', environmentInfoResponse)  // 触发 update 事件
        }
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}

</script>
<style scoped></style>