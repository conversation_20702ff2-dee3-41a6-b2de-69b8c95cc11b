<template>
    <div class="app-container">
        <el-row>
            <el-button type="primary" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column prop="name" label="环境名称">
            </el-table-column>
            <el-table-column prop="creator" label="创建人" />
            <el-table-column prop="desc" label="描述" />
            <el-table-column prop="create_time" label="创建时间">
                <template v-slot="scope">
                    {{ formatDate(scope.row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button type="primary" :icon="Setting" circle @click="drawerSetting(scope.row.id)"></el-button>
                    <el-button type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <environment_del :projectId="projectId" :environmentId="scope.row.id"
                        @deleteConfirmed="updateEnvironmentList">
                    </environment_del>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <environment_setting_drawer @updateEnvironmentDataList="updateEnvironmentDataList" v-model="drawerVisible" :drawerAtuh="drawerAtuh" :drawerUrl="drawerUrl" :drawerData="drawerData" :triggerUpdate="triggerUpdate" :modifyEnvironmentId='modifyEnvironmentId'></environment_setting_drawer>
    <environment_add_dialog :projectId="projectId" v-model="dialogVisible" @updateEnvironmentList="updateEnvironmentList">
    </environment_add_dialog>
    <environment_modify_dialog :projectId="projectId" v-model="modifyDialogVisible"
        :modifyEnvironmentId='modifyEnvironmentId' :environmentInfo="currentEnvironmentInfo"
        @updateEnvironmentList="updateEnvironmentList"></environment_modify_dialog>
</template>
  
<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import { getAllEnvironmentInfo } from '@/apis/project/environment/get_all_environmentinfo'
import { getEnvironmentData } from '@/apis/project/environment/environment_data/get_environment_data'
import { getEnvironmentUrl } from '@/apis/project/environment/environment_url/get_env_url'
import { getEnvironmentAuth } from '@/apis/project/environment/environment_auth/get_env_auth'
import { Setting, Edit, Plus } from '@element-plus/icons-vue';
import environment_add_dialog from './environment_add_dialog.vue'
import environment_del from './environment_del.vue';
import environment_modify_dialog from './environment_modify_dialog.vue';
// 复用独立那边的
import environment_setting_drawer from '../../environment/environment_setting_drawer.vue'
import { formatDate } from '@/util/format_date';


// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['updateData'])

const props = defineProps({
    projectId: Number
})
const projectId = ref(0)


watchEffect(() => {
    projectId.value = props.projectId || 0
})

const tableData = ref([])

const dialogVisible = ref(false)
// console.log('dialogAddValue1',environmentAddDialogVisible)
const modifyDialogVisible = ref(false)

const drawerVisible = ref(false)
const drawerData = ref([])
const drawerUrl = ref([])
const drawerAtuh = ref([])
// 标志drawer页是否打开
const triggerUpdate = ref(false)
// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
}

const modifyEnvironmentId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentEnvironmentInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyEnvironmentId.value = id
    const environment = tableData.value.find((environment: any) => environment.id === id) // 在 tableData 中查找用户的详细信息
    if (environment) { currentEnvironmentInfo.value = environment } // 将找到的用户信息存储在 currentEnvironmentInfo 中
}

const drawerSetting = async (id: number) => {
    drawerVisible.value = true
    modifyEnvironmentId.value = id
    // 确保 Promise 解析完成
    await getEnvironmentDataList(id)
    triggerUpdate.value = !triggerUpdate.value // 改变triggerUpdate的值
}

const getEnvironmentList = async () => {
    const res = await getAllEnvironmentInfo(projectId.value)
    if (res && res.data.code === 2000) {
        let environmentList = res.data.data
        // 排序
        tableData.value = environmentList
    }
}

getEnvironmentList()

// 获取单个环境数据
// 获取单个环境数据
const getEnvironmentDataList = async (envId: number) => {
    const res = await getEnvironmentData(envId, 100, 1)
    if (res && res.data.results.code === 2000) {
        drawerData.value = res.data.results.data
        const urlRes = await getEnvironmentUrl(envId)
        if ( urlRes && urlRes.data.code === 2000) {
            drawerUrl.value = urlRes.data.data
        }
        const envAuthRes = await getEnvironmentAuth(envId)
        if ( envAuthRes && envAuthRes.data.code === 2000) {
            drawerAtuh.value = envAuthRes.data.data
        }
    }
}


const updateEnvironmentList = (newEnvironments: any) => {
    if (newEnvironments) {
        tableData.value = newEnvironments.data.data
    }
    emit('updateData')
}



// 更新tab页数据
const updateEnvironmentDataList = (envId: number) => {
    getEnvironmentDataList(envId)
}


</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>