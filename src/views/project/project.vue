<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-col :span="7">
                <el-input placeholder="请输入项目名..." v-model="queryParams.projectNameQuery" clearable
                    @clear="getProjectList"></el-input>
            </el-col>
            <el-button type="primary" :icon="Search" @click="getProjectList">搜索</el-button>
            <el-button type="primary" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="name" label="项目名称" width="320px">
                <template v-slot="scope">
                    <router-link class="link-style" :to="`/project/config/${scope.row.id}`">{{ scope.row.name
                    }}</router-link>
                </template>
            </el-table-column>
            <el-table-column prop="creator" label="创建人" width="120px" />
            <el-table-column prop="desc" label="描述" />
            <el-table-column label="项目成员">
                <template v-slot="scope">
                    <div v-for="(member, index) in scope.row.member_info" :key="index"
                        style="display: inline-block; margin-right: 2px;">
                        <el-tag size="small" v-if="index < 3"> {{ member.member_username }} </el-tag>
                        <el-tag size="small" v-else-if="index === 3">...</el-tag>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="create_time" label="创建时间">
                <template v-slot="scope">
                    {{ formatDate(scope.row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <project_del :groupId="groupId" :userId="userId" :projectId="scope.row.id"
                        @deleteConfirmed="updateProjectList">
                    </project_del>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    <project_add_dialog :userId="userId" :groupId="groupId" :userName="userName" v-model="dialogVisible"
        @updateProjectList="updateProjectList"></project_add_dialog>
    <project_modify_dialog :userId="userId" :groupId="groupId" v-model="modifyDialogVisible"
        :modifyProjectId='modifyProjectId' :projectInfo="currentProjectInfo" @updateProjectList="updateProjectList">
    </project_modify_dialog>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getAllProjectInfo } from '@/apis/project/get_all_projectinfo'
import { Search, Edit, Plus } from '@element-plus/icons-vue';
import project_add_dialog from './components/project_add_dialog.vue';
import project_del from './components/project_del.vue';
import project_modify_dialog from './components/project_modify_dialog.vue';
import { useStore } from '@/stores';
import { formatDate } from '@/util/format_date';

const loading = ref(true)

type mainStore = {
    user_info: {
        id: number
        username: string
        group: []
    }
}

const mainStore = useStore() as mainStore

const groupId = mainStore.user_info.group
const userId = mainStore.user_info.id
const userName = mainStore.user_info.username


const queryParams = reactive({
    projectNameQuery: '',
    page_size: 10,
    page: 1,
    groupId: groupId,
    userId: userId
})

const total = ref(0)
const tableData = ref([])

const dialogVisible = ref(false)
// console.log('dialogAddValue1',projectAddDialogVisible)
const modifyDialogVisible = ref(false)

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
    // console.log('dialogAddValue2',projectAddDialogVisible)
}

const modifyProjectId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentProjectInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyProjectId.value = id
    const project = tableData.value.find((project: any) => project.id === id) // 在 tableData 中查找用户的详细信息
    if (project) { currentProjectInfo.value = project } // 将找到的用户信息存储在 currentProjectInfo 中
}


const getProjectList = async () => {
    const res = await getAllProjectInfo(queryParams.page_size, queryParams.page, queryParams.groupId, queryParams.userId, queryParams.projectNameQuery)
    if (res) {
        let projectList = res.data.results.data
        // 排序
        tableData.value = projectList
        total.value = res.data.count
        loading.value = false
    }
}

getProjectList()

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getProjectList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getProjectList()
}

// 定义一个事件函数，让子组件触发，需要传递的数据作为事件的参数，这里是子组件直接传整个响应，父组件监听，并处理
// 监听 update 事件只要在上方的子组件引入中加入 @updateProjectList="updateProjectList"
const updateProjectList = (newProjects: any) => {
    if (newProjects) {
        tableData.value = newProjects.data.results.data
        total.value = newProjects.data.count
    }
}


</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}

.link-style {
    color: #409EFF;
    /* 链接颜色 */
    text-decoration: none;
    /* 移除下划线 */
    transition: color 0.3s ease;
    /* 颜色过渡效果 */
}

.link-style:hover {
    color: #0a62b9;
    /* 鼠标悬停时的颜色 */
    text-decoration: underline;
    /* 鼠标悬停时添加下划线 */
}
</style>