<template>
  <div class="create-task-container">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本配置 -->
      <el-card class="mb-4 base-config-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">基本配置</span>
          </div>
        </template>
        <el-form-item label="任务名称" prop="target.name">
          <el-input v-model="formData.target.name" placeholder="请输入任务名称"></el-input>
        </el-form-item>
        <el-form-item label="任务描述" prop="target.description">
          <el-input v-model="formData.target.description" type="textarea" :rows="3" placeholder="请输入任务描述"></el-input>
        </el-form-item>
        <el-form-item label="任务类型">
          <el-tag :type="formData.target.taskType === 'ai' ? 'primary' : 'success'">
            {{ formData.target.taskType === 'ai' ? 'AI 智能测试' : 'Playwright 脚本测试' }}
          </el-tag>
        </el-form-item>

        <!-- AI类型的配置字段 -->
        <template v-if="formData.target.taskType === 'ai'">
          <el-form-item label="目标URL" prop="target.url">
            <div class="url-input-container">
              <el-radio-group v-model="urlInputType" @change="handleUrlTypeChange" class="mb-2">
                <el-radio :label="'input'">直接输入</el-radio>
                <el-radio :label="'select'">选择公共数据</el-radio>
              </el-radio-group>
              <div v-if="urlInputType === 'input'">
                <el-input v-model="formData.target.url" placeholder="请输入目标URL"></el-input>
              </div>
              <div v-else>
                <el-select v-model="selectedUrlKey" filterable placeholder="请选择公共数据变量" class="w-full"
                  @change="handleUrlKeyChange">
                  <el-option v-for="item in publicDataList" :key="item.id" :label="item.key" :value="item.key">
                    <div class="public-data-option">
                      <span>{{ item.key }}</span>
                      <span class="text-gray-400 text-sm ml-2">({{ item.value }})</span>
                    </div>
                  </el-option>
                </el-select>
                <div v-if="selectedUrlKey" class="variable-preview mt-2">
                  <el-tag type="info" class="variable-tag">
                    <span v-text="'{{' + selectedUrlKey + '}}'"></span>
                    <el-tooltip v-if="getPublicDataValue(selectedUrlKey)" :content="getPublicDataValue(selectedUrlKey)"
                      placement="top">
                      <el-icon class="ml-1">
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </el-tag>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="桥接模式">
            <span class="text-gray">不开启</span>
          </el-form-item>
          <el-form-item label="忽略HTTPS证书">
            <el-switch v-model="formData.target.acceptInsecureCerts" />
          </el-form-item>
        </template>
      </el-card>

      <!-- Playwright 脚本编辑器 -->
      <el-card v-if="formData.target.taskType === 'playwright'" class="mb-4 script-config-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">Playwright 脚本</span>
          </div>
        </template>
        <el-form-item label="脚本编码" prop="target.scriptCode">
          <el-input v-model="formData.target.scriptCode" placeholder="脚本编码" readonly></el-input>
        </el-form-item>
        <el-form-item label="脚本内容" prop="target.script" class="script-editor-form-item">
          <PlaywrightScriptEditor
            :script-data="formData.target.script"
            @update-script-data="handleScriptUpdate"
          />
        </el-form-item>
      </el-card>

      <!-- 任务列表 (仅AI类型显示) -->
      <el-card v-if="formData.target.taskType === 'ai'" class="tasks-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">测试任务</span>
          </div>
        </template>

        <draggable v-model="formData.tasks" item-key="tempId" handle=".drag-handle" @start="drag = true"
          @end="drag = false" class="task-list">
          <template #item="{ element: task, index: taskIndex }">
            <div class="task-item">
              <el-card class="task-card" :body-style="{ padding: '20px' }">
                <div class="task-header">
                  <div class="task-header-left">
                    <el-icon class="drag-handle">
                      <Rank />
                    </el-icon>
                    <h3 class="task-title">任务 {{ taskIndex + 1 }}</h3>
                  </div>
                  <div class="task-header-right">
                    <el-button-group class="order-buttons">
                      <el-button type="primary" link :disabled="taskIndex === 0" @click="moveTask(taskIndex, 'up')">
                        <el-icon>
                          <ArrowUp />
                        </el-icon>
                      </el-button>
                      <el-button type="primary" link :disabled="taskIndex === formData.tasks.length - 1"
                        @click="moveTask(taskIndex, 'down')">
                        <el-icon>
                          <ArrowDown />
                        </el-icon>
                      </el-button>
                    </el-button-group>
                    <el-button type="danger" link @click="removeTask(taskIndex)" class="delete-btn">
                      <el-icon>
                        <Delete />
                      </el-icon> 删除任务
                    </el-button>
                  </div>
                </div>

                <el-form-item :label="'任务名称'" :prop="'tasks.' + taskIndex + '.name'" :rules="rules.taskName">
                  <el-input v-model="task.name" placeholder="请输入任务名称"></el-input>
                </el-form-item>

                <el-form-item label="出错是否继续">
                  <el-switch v-model="task.continueOnError" />
                </el-form-item>

                <!-- 流程步骤 -->
                <div class="flow-steps">
                  <div class="steps-header">
                    <h4 class="steps-title">流程步骤</h4>
                  </div>

                  <draggable v-model="task.flow" item-key="tempId" handle=".step-drag-handle" @start="dragStep = true"
                    @end="dragStep = false" class="step-list">
                    <template #item="{ element: step, index: stepIndex }">
                      <div class="step-item">
                        <el-card class="step-card" shadow="hover">
                          <div class="step-header">
                            <div class="step-header-left">
                              <el-icon class="step-drag-handle">
                                <Rank />
                              </el-icon>
                              <span class="step-title">步骤 {{ stepIndex + 1 }}</span>
                            </div>
                            <div class="step-header-right">
                              <el-button-group class="order-buttons">
                                <el-button type="primary" link :disabled="stepIndex === 0"
                                  @click="moveStep(taskIndex, stepIndex, 'up')">
                                  <el-icon>
                                    <ArrowUp />
                                  </el-icon>
                                </el-button>
                                <el-button type="primary" link :disabled="stepIndex === task.flow.length - 1"
                                  @click="moveStep(taskIndex, stepIndex, 'down')">
                                  <el-icon>
                                    <ArrowDown />
                                  </el-icon>
                                </el-button>
                              </el-button-group>
                              <el-button type="danger" link @click="removeStep(taskIndex, stepIndex)"
                                class="delete-btn">
                                <el-icon>
                                  <Delete />
                                </el-icon> 删除步骤
                              </el-button>
                            </div>
                          </div>

                          <el-form-item label="步骤类型">
                            <el-select v-model="step.type" placeholder="请选择步骤类型"
                              @change="handleStepTypeChange($event, taskIndex, stepIndex)" class="step-type-select">
                              <el-option label="AI动作" value="aiAction" />
                              <el-option label="点击元素" value="aiTap" />
                              <el-option label="悬停元素" value="aiHover" />
                              <el-option label="输入文本" value="aiInput" />
                              <el-option label="按键操作" value="aiKeyboardPress" />
                              <el-option label="滚动操作" value="aiScroll" />
                              <el-option label="数据查询" value="aiQuery" />
                              <el-option label="等待条件" value="aiWaitFor" />
                              <el-option label="断言验证" value="aiAssert" />
                              <el-option label="等待时间" value="sleep" />
                            </el-select>
                          </el-form-item>

                          <!-- 根据步骤类型显示不同的表单项 -->
                          <template v-if="step.type === 'aiAction'">
                            <el-form-item label="AI动作描述" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <!-- 常用文案快捷按钮 -->
                              <div class="quick-action-buttons">
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '点击')">点击</el-button>
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '点击中间')">点击中间</el-button>
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '点击上面')">点击上面</el-button>
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '点击下面')">点击下面</el-button>
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '点击左面')">点击左面</el-button>
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '点击右面')">点击右面</el-button>
                                <el-button size="small" type="info" plain
                                  @click="addQuickTag(taskIndex, stepIndex, '输入:')">输入</el-button>
                              </div>
                              <el-input-tag v-model="step.value" delimiter="," draggable :max="8"
                                placeholder="请输入动作标签，最多8个"></el-input-tag>
                              <el-button size="small" type="primary" plain style="margin-top: 10px;"
                                @click="editTags(taskIndex, stepIndex)">编辑标签</el-button>
                            </el-form-item>
                          </template>

                          <template v-if="['aiTap', 'aiHover'].includes(step.type)">
                            <el-form-item label="元素描述" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <el-input v-model="step.value" placeholder="请描述要操作的元素"></el-input>
                            </el-form-item>
                            <el-form-item label="深度思考">
                              <el-switch v-model="step.deepThink" />
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'aiInput'">
                            <el-form-item label="输入内容" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <el-input v-model="step.value" placeholder="请输入要填写的内容"></el-input>
                            </el-form-item>
                            <el-form-item label="元素定位" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.locate'"
                              :rules="rules.stepLocate">
                              <el-input v-model="step.locate" placeholder="请描述输入框位置"></el-input>
                            </el-form-item>
                            <el-form-item label="深度思考">
                              <el-switch v-model="step.deepThink" />
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'aiScroll'">
                            <el-form-item label="滚动方向">
                              <el-select v-model="step.direction">
                                <el-option label="向上" value="up" />
                                <el-option label="向下" value="down" />
                                <el-option label="向左" value="left" />
                                <el-option label="向右" value="right" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="滚动类型">
                              <el-select v-model="step.scrollType">
                                <el-option label="滚动一次" value="once" />
                                <el-option label="滚动到顶部" value="untilTop" />
                                <el-option label="滚动到底部" value="untilBottom" />
                                <el-option label="滚动到最左" value="untilLeft" />
                                <el-option label="滚动到最右" value="untilRight" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="滚动距离">
                              <el-input-number v-model="step.distance" :min="0"></el-input-number>
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'sleep'">
                            <el-form-item label="等待时间(ms)">
                              <el-input-number v-model="step.value" :min="0" :step="100"></el-input-number>
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'aiWaitFor'">
                            <el-form-item label="等待条件" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <el-input v-model="step.value" placeholder="请描述等待条件"></el-input>
                            </el-form-item>
                            <el-form-item label="超时时间(ms)">
                              <el-input-number v-model="step.timeout" :min="0" :step="1000"></el-input-number>
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'aiAssert'">
                            <el-form-item label="断言条件" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <el-input v-model="step.value" placeholder="请描述断言条件"></el-input>
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'aiQuery'">
                            <el-form-item label="查询描述" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <el-input v-model="step.value" type="textarea" placeholder="请描述查询内容"></el-input>
                            </el-form-item>
                            <el-form-item label="结果名称" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.name'"
                              :rules="rules.stepName">
                              <el-input v-model="step.name" placeholder="查询结果的键名"></el-input>
                            </el-form-item>
                          </template>

                          <template v-if="step.type === 'aiKeyboardPress'">
                            <el-form-item label="按键选择" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.value'"
                              :rules="rules.stepValue">
                              <el-select v-model="step.value" placeholder="请选择按键">
                                <el-option label="回车" value="Enter" />
                                <el-option label="空格" value="Space" />
                                <el-option label="退格" value="Backspace" />
                                <el-option label="删除" value="Delete" />
                                <el-option label="制表" value="Tab" />
                                <el-option label="上箭头" value="ArrowUp" />
                                <el-option label="下箭头" value="ArrowDown" />
                                <el-option label="左箭头" value="ArrowLeft" />
                                <el-option label="右箭头" value="ArrowRight" />
                                <el-option label="Esc" value="Escape" />
                                <el-option label="Home" value="Home" />
                                <el-option label="End" value="End" />
                                <el-option label="PageUp" value="PageUp" />
                                <el-option label="PageDown" value="PageDown" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="元素定位" :prop="'tasks.' + taskIndex + '.flow.' + stepIndex + '.locate'"
                              :rules="rules.stepLocate">
                              <el-input v-model="step.locate" placeholder="请描述要操作的元素位置"></el-input>
                            </el-form-item>
                          </template>
                        </el-card>
                      </div>
                    </template>
                  </draggable>

                  <div class="add-step-button">
                    <el-button type="primary" plain @click="addStep(taskIndex)" class="add-step-btn">
                      <el-icon>
                        <Plus />
                      </el-icon> 添加步骤
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </template>
        </draggable>
      </el-card>

      <!-- 悬浮按钮 -->
      <div class="floating-buttons">
        <el-button type="primary" round @click="addTask" class="floating-btn add-task-btn">
          <el-icon>
            <Plus />
          </el-icon> 添加任务
        </el-button>
        <el-button type="info" round @click="showVariablesDialog" class="floating-btn query-var-btn">
          <el-icon>
            <Search />
          </el-icon> 查询变量
        </el-button>
      </div>

      <div class="form-actions">
        <el-button type="primary" @click="submitForm" size="large">保存</el-button>
        <el-button @click="cancel" size="large">取消</el-button>
      </div>
    </el-form>

    <!-- 添加变量查询对话框 -->
    <el-dialog v-model="variablesDialogVisible" title="公共数据变量" width="600px">
      <el-table :data="filteredPublicDataList" style="width: 100%" stripe size="small">
        <el-table-column prop="key" label="变量名" />
        <el-table-column prop="value" label="变量值">
          <template #default="{ row }">
            <el-tooltip :content="row.value" placement="top">
              <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 300px;">
                {{ row.value }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="copyVariable(row.key)">复制变量</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 添加标签编辑对话框 -->
    <el-dialog v-model="tagEditDialogVisible" title="编辑标签" width="600px">
      <el-form>
        <el-form-item label="标签列表">
          <div v-for="(tag, index) in editingTags" :key="index" class="edit-tag-item">
            <el-input v-model="editingTags[index]" placeholder="请输入标签内容"></el-input>
            <el-button type="danger" link size="small" @click="removeEditingTag(index)">
              删除
            </el-button>
          </div>
          <div>
            <el-button type="primary" size="small" @click="addEditingTag" :disabled="editingTags.length >= 8">
              添加
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="tagEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEditingTags">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { getTask } from '@/apis/ui_test/get_task'
import { putTask } from '@/apis/ui_test/put_task'
import { Delete, Plus, Rank, ArrowUp, ArrowDown, InfoFilled, Search } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { useStore } from '@/stores'
import { getPublicDataInfo } from '@/apis/project/public_data/get_public_data'
import PlaywrightScriptEditor from '@/components/PlaywrightScriptEditor.vue'

interface ProjectInfo {
  id: number;
  [key: string]: any;
}

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const taskId = ref(parseInt(route.params.id as string))
const mainStore = useStore()
const project_id = ref((mainStore.project_info as ProjectInfo).id)
const folder_id = ref<number | null>(null)

// URL输入类型相关变量
const urlInputType = ref('input') // 默认为直接输入
const selectedUrlKey = ref('')
const publicDataList = ref<any[]>([])

interface Target {
  url: string
  bridgeMode: string
  acceptInsecureCerts: boolean
  name: string
  description: string
  project: number
  folder?: number | null
  taskType: string
  script?: string
  scriptCode?: string
}

interface Step {
  type: string
  value?: string | number | string[]
  deepThink?: boolean
  direction?: string
  scrollType?: string
  distance?: number
  timeout?: number
  locate?: string
  name?: string
  [key: string]: any
}

interface Task {
  name: string
  continueOnError: boolean
  flow: Step[]
  tempId: number
}

interface FormData {
  target: Target
  tasks: Task[]
}

const formData = reactive<FormData>({
  target: {
    url: '',
    bridgeMode: 'false',
    acceptInsecureCerts: true,
    name: '',
    description: '',
    project: project_id.value,
    folder: null,
    taskType: 'ai',
    script: '',
    scriptCode: ''
  },
  tasks: []
})

const rules = reactive<FormRules>({
  'target.name': [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  'target.url': [
    { required: true, message: '请输入目标URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  stepValue: [
    { required: true, message: '该字段不能为空', trigger: 'blur' }
  ],
  stepLocate: [
    { required: true, message: '请描述元素位置', trigger: 'blur' }
  ],
  stepName: [
    { required: true, message: '请输入结果名称', trigger: 'blur' }
  ]
})

const drag = ref(false)
const dragStep = ref(false)

const loadTaskData = async () => {
  try {
    const response = await getTask(taskId.value)
    if (response?.data) {
      const taskData = response.data
      formData.target.name = taskData.name
      formData.target.description = taskData.description
      formData.target.url = taskData.url
      formData.target.bridgeMode = taskData.bridge_mode
      formData.target.acceptInsecureCerts = taskData.accept_insecure_certs
      formData.target.project = taskData.project || project_id.value
      formData.target.taskType = taskData.task_type || 'ai'
      formData.target.script = taskData.script || ''
      formData.target.scriptCode = taskData.script_code || ''

      // 初始化URL输入模式
      initUrlInputMode()

      // 转换任务数据
      formData.tasks = taskData.tasks.map((task: any) => {
        return {
          name: task.name,
          continueOnError: task.continueOnError,
          flow: task.flow.map((step: any) => {
            const newStep: Step = { type: '', value: '' }

            if (step.aiAction) {
              newStep.type = 'aiAction'
              // 处理字符串格式的aiAction，尝试将其转换为数组
              if (typeof step.aiAction === 'string') {
                // 使用正则表达式提取带引号的部分
                const matches = step.aiAction.match(/"([^"]*)"/g) || []
                if (matches.length > 0) {
                  // 移除引号后作为数组存储
                  newStep.value = matches.map((match: string) => match.replace(/^"|"$/g, ''))
                } else {
                  // 如果没有发现引号格式，则作为单个标签处理
                  newStep.value = [step.aiAction]
                }
              } else {
                newStep.value = []
              }
            } else if (step.aiTap) {
              newStep.type = 'aiTap'
              newStep.value = step.aiTap
              newStep.deepThink = step.deepThink
            } else if (step.aiHover) {
              newStep.type = 'aiHover'
              newStep.value = step.aiHover
              newStep.deepThink = step.deepThink
            } else if (step.aiInput) {
              newStep.type = 'aiInput'
              newStep.value = step.aiInput
              newStep.locate = step.locate
              newStep.deepThink = step.deepThink
            } else if (step.aiKeyboardPress) {
              newStep.type = 'aiKeyboardPress'
              newStep.value = step.aiKeyboardPress
              newStep.locate = step.locate
            } else if (step.aiScroll) {
              newStep.type = 'aiScroll'
              newStep.direction = step.aiScroll.direction
              newStep.scrollType = step.aiScroll.scrollType
              newStep.distance = step.aiScroll.distance
            } else if (step.aiWaitFor) {
              newStep.type = 'aiWaitFor'
              newStep.value = step.aiWaitFor
              newStep.timeout = step.timeout
            } else if (step.aiQuery) {
              newStep.type = 'aiQuery'
              newStep.value = step.aiQuery
              newStep.name = step.name
            } else if (step.aiAssert) {
              newStep.type = 'aiAssert'
              newStep.value = step.aiAssert
            } else if (step.sleep) {
              newStep.type = 'sleep'
              newStep.value = step.sleep
            }

            return newStep
          }),
          tempId: Date.now()
        }
      })
    }
    return Promise.resolve()
  } catch (error) {
    console.error('加载任务数据失败:', error)
    ElMessage.error('加载任务数据失败')
    return Promise.reject(error)
  }
}

const moveTask = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1
  if (newIndex >= 0 && newIndex < formData.tasks.length) {
    const tasks = formData.tasks
    const temp = tasks[index]
    tasks[index] = tasks[newIndex]
    tasks[newIndex] = temp
  }
}

const moveStep = (taskIndex: number, stepIndex: number, direction: 'up' | 'down') => {
  const steps = formData.tasks[taskIndex].flow
  const newIndex = direction === 'up' ? stepIndex - 1 : stepIndex + 1
  if (newIndex >= 0 && newIndex < steps.length) {
    const temp = steps[stepIndex]
    steps[stepIndex] = steps[newIndex]
    steps[newIndex] = temp
  }
}

const addTask = () => {
  formData.tasks.push({
    name: '',
    continueOnError: true,
    flow: [],
    tempId: Date.now()
  })
}

const removeTask = (index: number) => {
  formData.tasks.splice(index, 1)
}

const addStep = (taskIndex: number) => {
  formData.tasks[taskIndex].flow.push({
    type: 'aiAction',
    value: [],
    tempId: Date.now()
  })
}

const removeStep = (taskIndex: number, stepIndex: number) => {
  formData.tasks[taskIndex].flow.splice(stepIndex, 1)
}

const handleStepTypeChange = (type: string, taskIndex: number, stepIndex: number) => {
  const step = formData.tasks[taskIndex].flow[stepIndex]

  // 根据类型设置默认值
  switch (type) {
    case 'aiAction':
      step.value = []
      break
    case 'aiScroll':
      step.value = ''
      step.direction = 'down'
      step.scrollType = 'once'
      step.distance = 100
      break
    case 'aiWaitFor':
      step.value = ''
      step.timeout = 30000
      break
    default:
      step.value = ''
      break
  }
}

const addQuickTag = (taskIndex: number, stepIndex: number, tag: string) => {
  // 获取特定任务和步骤的 el-input-tag 组件
  setTimeout(() => {
    // 使用更精确的选择器找到当前步骤的输入框
    const selector = `.task-list > div:nth-child(${taskIndex + 1}) .step-list > div:nth-child(${stepIndex + 1}) .el-input-tag input[type="text"]`
    const tagInput = document.querySelector(selector) as HTMLInputElement
    if (tagInput) {
      // 设置输入框的值为我们想要添加的标签
      tagInput.value = tag
      // 模拟用户输入事件
      tagInput.dispatchEvent(new Event('input', { bubbles: true }))
      // 聚焦到输入框
      tagInput.focus()
    }
  }, 0)
}

// 处理脚本更新
const handleScriptUpdate = (scriptContent: string) => {
  formData.target.script = scriptContent
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构造提交数据
    const submitData: any = {
      name: formData.target.name,
      description: formData.target.description,
      project: formData.target.project,
      task_type: formData.target.taskType
    }

    if (formData.target.taskType === 'playwright') {
      // Playwright类型的数据
      submitData.script_code = formData.target.scriptCode
      submitData.script = formData.target.script
    } else {
      // AI类型的数据
      submitData.url = formData.target.url
      submitData.bridge_mode = formData.target.bridgeMode
      submitData.accept_insecure_certs = formData.target.acceptInsecureCerts
      submitData.tasks = formData.tasks.map(task => ({
        name: task.name,
        continueOnError: task.continueOnError,
        flow: task.flow.map(step => {
          const flowStep: any = {}
          switch (step.type) {
            case 'aiAction':
              // 如果是数组，将其转换为带引号的字符串
              if (Array.isArray(step.value)) {
                const formattedTags = step.value.map(tag => `"${tag}"`).join(', ')
                flowStep.aiAction = formattedTags
              } else {
                flowStep.aiAction = step.value
              }
              break
            case 'aiTap':
            case 'aiHover':
              flowStep[step.type] = step.value
              flowStep.deepThink = step.deepThink
              break
            case 'aiInput':
              flowStep.aiInput = step.value
              flowStep.locate = step.locate
              flowStep.deepThink = step.deepThink
              break
            case 'aiKeyboardPress':
              flowStep.aiKeyboardPress = step.value
              flowStep.locate = step.locate
              break
            case 'aiScroll':
              flowStep.aiScroll = {
                direction: step.direction,
                scrollType: step.scrollType,
                distance: step.distance
              }
              break
            case 'aiWaitFor':
              flowStep.aiWaitFor = step.value
              flowStep.timeout = step.timeout
              break
            case 'aiQuery':
              flowStep.aiQuery = step.value
              flowStep.name = step.name
              break
            case 'aiAssert':
              flowStep.aiAssert = step.value
              break
            case 'sleep':
              flowStep.sleep = step.value
              break
          }
          return flowStep
        })
      }))
    }

    // 调用更新API
    await putTask(taskId.value, submitData)
    ElMessage.success('保存成功')
    router.push('/ui_test')
  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入')
  }
}

const cancel = () => {
  router.push('/ui_test')
}

// 处理URL类型变更
const handleUrlTypeChange = (type: string) => {
  if (type === 'input') {
    // 如果切换到输入模式，且当前URL是变量格式，则清空
    if (formData.target.url.startsWith('{{') && formData.target.url.endsWith('}}')) {
      formData.target.url = ''
    }
    selectedUrlKey.value = ''
  } else {
    // 如果切换到选择模式，加载公共数据
    loadPublicData()
  }
}

// 处理选择URL变量
const handleUrlKeyChange = (key: string) => {
  selectedUrlKey.value = key
  formData.target.url = `{{${key}}}`
}

// 获取公共数据值（用于提示）
const getPublicDataValue = (key: string) => {
  const item = publicDataList.value.find(item => item.key === key)
  return item ? item.value : ''
}

// 加载公共数据
const loadPublicData = async () => {
  try {
    const response = await getPublicDataInfo(project_id.value)
    if (response?.data?.code === 2000 && response.data.data) {
      // 只过滤type为5的数据（UI_TEST_URL类型）
      publicDataList.value = response.data.data.filter((item: any) => item.type === '5')
    }
  } catch (error) {
    console.error('加载公共数据失败:', error)
  }
}

// 初始化时检查URL是否为变量格式
const initUrlInputMode = () => {
  const url = formData.target.url
  if (url && url.startsWith('{{') && url.endsWith('}}')) {
    urlInputType.value = 'select'
    // 提取变量名
    const key = url.substring(2, url.length - 2)
    selectedUrlKey.value = key
    // 加载公共数据以供选择
    loadPublicData()
  } else {
    urlInputType.value = 'input'
  }
}

const variablesDialogVisible = ref(false)
const filteredPublicDataList = ref<any[]>([])

const showVariablesDialog = async () => {
  variablesDialogVisible.value = true
  // 如果还没有加载过数据，先加载
  if (publicDataList.value.length === 0) {
    await loadPublicData()
  }
  filteredPublicDataList.value = publicDataList.value.filter((item: any) => item.type === '5')
}

const copyVariable = (key: string) => {
  try {
    // 创建一个临时的文本区域元素
    const textArea = document.createElement('textarea')
    textArea.value = `{{${key}}}`
    // 确保元素在视觉上不可见
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    // 选择文本并执行复制命令
    textArea.select()
    document.execCommand('copy')
    // 清理元素
    document.body.removeChild(textArea)
    ElMessage.success(`变量 {{${key}}} 已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

const tagEditDialogVisible = ref(false)
const editingTags = ref<string[]>([])
const currentEditingTaskIndex = ref<number>(0)
const currentEditingStepIndex = ref<number>(0)

const editTags = (taskIndex: number, stepIndex: number) => {
  currentEditingTaskIndex.value = taskIndex
  currentEditingStepIndex.value = stepIndex
  const step = formData.tasks[taskIndex].flow[stepIndex]
  editingTags.value = Array.isArray(step.value) ? [...step.value] : []
  tagEditDialogVisible.value = true
}

const removeEditingTag = (index: number) => {
  editingTags.value.splice(index, 1)
}

const addEditingTag = () => {
  if (editingTags.value.length < 8) {
    editingTags.value.push('')
  }
}

const saveEditingTags = () => {
  // 过滤掉空标签
  const filteredTags = editingTags.value.filter(tag => tag.trim() !== '')
  const taskIndex = currentEditingTaskIndex.value
  const stepIndex = currentEditingStepIndex.value
  formData.tasks[taskIndex].flow[stepIndex].value = filteredTags
  tagEditDialogVisible.value = false
}

onMounted(() => {
  // 在编辑页面中不需要获取和处理folder_id参数
  /*
  // 获取query参数中的folder_id
  const folderIdParam = route.query.folder_id
  if (folderIdParam) {
    folder_id.value = parseInt(folderIdParam as string)
    formData.target.folder = folder_id.value
  }
  */

  loadTaskData()
  // 加载公共数据
  loadPublicData()
})
</script>

<style scoped>
.create-task-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  min-height: calc(100vh - 48px);
}

.base-config-card,
.script-config-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.script-editor-form-item {
  :deep(.el-form-item__content) {
    width: 100% !important;
    max-width: none !important;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.task-item {
  margin-bottom: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.task-card {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.step-card {
  margin-bottom: 16px;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.delete-btn {
  padding: 4px 8px;
  font-size: 13px;
}

.delete-btn :deep(.el-icon) {
  margin-right: 4px;
}

.add-step-button {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.add-step-btn {
  width: 140px;
}

.floating-buttons {
  position: fixed;
  right: 40px;
  bottom: 120px;
  /* 调整位置，使按钮更靠上 */
  z-index: 1000;
  flex-direction: column;
}

.floating-btn {
  width: 90px;
  /* 减小按钮尺寸 */
  height: 36px;
  font-size: 12px;
}

.floating-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.form-actions {
  margin-top: 40px;
  padding-bottom: 60px;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-input),
:deep(.el-select) {
  width: 100%;
}

:deep(.el-card__body) {
  padding: 24px;
}

.step-type-select {
  width: 100%;
}

/* 添加过渡动画 */
.task-item-enter-active,
.task-item-leave-active,
.step-item-enter-active,
.step-item-leave-active {
  transition: all 0.3s ease;
}

.task-item-enter-from,
.task-item-leave-to,
.step-item-enter-from,
.step-item-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .create-task-container {
    padding: 16px;
  }

  .floating-buttons {
    right: 20px;
    bottom: 100px;
  }

  .floating-btn {
    width: 100px;
    height: 32px;
  }
}

.task-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle,
.step-drag-handle {
  cursor: move;
  color: #909399;
  font-size: 16px;
  padding: 4px;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.drag-handle:hover,
.step-drag-handle:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.order-buttons {
  display: flex;
  gap: 4px;
}

.order-buttons .el-button {
  padding: 4px;
  transition: all 0.3s ease;
}

.order-buttons .el-button:hover {
  transform: scale(1.1);
  background-color: var(--el-color-primary-light-9);
}

.order-buttons .el-button:active {
  transform: scale(0.95);
}

.task-list,
.step-list {
  min-height: 10px;
}

/* 拖拽时的样式 */
.sortable-ghost {
  opacity: 0.5;
  background: #f0f9ff;
  border: 2px dashed var(--el-color-primary);
  border-radius: 8px;
  transform: scale(1.02);
}

.sortable-drag {
  opacity: 0.8;
  transform: scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 移动效果 */
@keyframes moveHighlight {
  0% {
    background-color: transparent;
  }

  50% {
    background-color: var(--el-color-primary-light-9);
  }

  100% {
    background-color: transparent;
  }
}

.task-move,
.step-move {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.task-item.moving,
.step-item.moving {
  animation: moveHighlight 1s ease;
  z-index: 10;
}

.steps-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.steps-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.quick-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.quick-action-buttons .el-button {
  margin-right: 0;
}

/* URL输入相关样式 */
.url-input-container {
  width: 100%;
}

.variable-preview {
  display: flex;
  align-items: center;
}

.variable-tag {
  display: inline-flex;
  align-items: center;
  padding: 0 10px;
  height: 32px;
  font-size: 14px;
  border-radius: 4px;
  background-color: #f0f9ff;
  border: 1px solid #a0cfff;
  color: #409eff;
}

.variable-tag .el-icon {
  cursor: pointer;
  margin-left: 5px;
}

.public-data-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-400 {
  color: #909399;
}

.text-sm {
  font-size: 12px;
}

.add-tag-button {
  margin-top: 8px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}
</style>