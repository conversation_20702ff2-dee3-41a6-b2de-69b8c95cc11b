<template>
  <div class="ui-test-container">
    <div class="header">
      <div class="header-row">
        <el-input v-model="searchQuery" placeholder="请输入名称或描述" class="compact-input">
          <template #append>
            <el-button @click="handleSearch" size="small">搜索</el-button>
          </template>
        </el-input>

        <el-select
          v-model="selectedModel"
          placeholder="选择模型"
          class="compact-select">
          <el-option
            v-for="item in filteredModelOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>

        <el-select
          v-model="selectedStatus"
          placeholder="选择状态"
          class="compact-select">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>

        <div class="button-group">
          <el-dropdown @command="handleCreateTask" trigger="click">
            <el-button type="primary" size="small">
              新建任务 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="ai">
                  <el-icon style="color: #409eff;"><Cpu /></el-icon> AI 智能测试
                </el-dropdown-item>
                <el-dropdown-item command="playwright">
                  <el-icon style="color: #67c23a;"><DocumentCopy /></el-icon> Playwright 脚本测试
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="success" size="small" @click="handleGlobalRun" :disabled="!selectedTasks.length">批量运行</el-button>
          <el-button type="info" size="small" @click="handleBatchReport" :disabled="!selectedTasks.length">批量查看报告</el-button>
          <el-button type="warning" size="small" @click="handleExport" :disabled="!selectedTasks.length">导出</el-button>
          <el-button type="primary" size="small" @click="handleImport">导入</el-button>
          <el-button type="danger" size="small" @click="handleBatchDelete" :disabled="!selectedTasks.length">批量删除</el-button>
        </div>
      </div>
      
      <div class="selected-info" v-if="selectedTasks.length">
        <el-tag size="small" type="info">已选择 {{ selectedTasks.length }} 项</el-tag>
        <el-button type="text" size="small" @click="clearSelection">清除选择</el-button>
      </div>
    </div>

    <div class="table-container">
      <el-table 
        ref="tableRef"
        :data="taskList" 
        style="width: 100%" 
        v-loading="loading" 
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" fixed />
        <!-- <el-table-column prop="id" label="ID" width="80" fixed /> -->
        <el-table-column prop="name" label="任务名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="url" label="目标URL" min-width="200" show-overflow-tooltip />
        <el-table-column prop="task_type" label="任务类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.task_type === 'ai' ? 'primary' : 'success'">
              {{ scope.row.task_type === 'ai' ? 'AI 智能测试' : 'Playwright 脚本测试' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="bridge_mode" label="桥接模式" width="120">
          <template #default>
            不开启
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(taskStatusMap[scope.row.id]?.status)">
              {{ getStatusText(taskStatusMap[scope.row.id]?.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建者" width="120" show-overflow-tooltip />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="运行" placement="top">
                <el-button type="success" circle @click="handleRun(scope.row)">
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="编辑" placement="top">
                <el-button type="primary" circle @click="editTask(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="报告" placement="top">
                <el-button type="info" circle @click="viewReport(scope.row)">
                  <el-icon><Document /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="日志" placement="top">
                <el-button type="warning" circle @click="viewLog(scope.row)">
                  <el-icon><List /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="复制" placement="top">
                <el-button type="primary" circle @click="handleCopy(scope.row)">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="删除" placement="top">
                <el-button type="danger" circle @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination">
      <el-pagination 
        v-model:current-page="currentPage" 
        v-model:page-size="pageSize" 
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next" 
        :total="total" 
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" 
      />
    </div>

    <!-- 批量运行对话框 -->
    <el-dialog v-model="batchRunDialogVisible" title="批量运行任务" width="30%">
      <div class="batch-run-content">
        <p>您选择了 {{ selectedTasks.length }} 个任务进行运行：</p>
        <ul>
          <li v-for="task in selectedTasks" :key="task.id">
            {{ task.name }}
          </li>
        </ul>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchRunDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchRun">
            确认运行
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量查看报告对话框 -->
    <el-dialog 
      v-model="batchReportDialogVisible" 
      title="批量查看报告" 
      width="90%" 
      :fullscreen="true"
      :destroy-on-close="true"
      class="report-dialog">
      <div class="batch-report-content">
        <el-tabs v-model="activeReportTab" type="card" class="report-tabs">
          <el-tab-pane v-for="task in selectedTasks" :key="task.id" :label="task.name" :name="String(task.id)">
            <div v-loading="reportLoading[task.id]" class="iframe-container">
              <iframe v-if="reportData[task.id]?.report_url" 
                :src="reportData[task.id].report_url" 
                class="report-iframe"
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
                referrerpolicy="no-referrer">
              </iframe>
              <div v-else>
                暂无报告数据 (Task ID: {{ task.id }}, Data: {{ reportData[task.id] }})
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 添加日志查看对话框 -->
    <el-dialog v-model="logDialogVisible" title="任务日志" width="80%">
      <div class="log-content">
        <pre>{{ currentTaskLog }}</pre>
      </div>
    </el-dialog>

    <!-- 添加目录选择对话框 -->
    <el-dialog v-model="folderSelectDialogVisible" title="选择目录" width="40%">
      <div class="folder-select-content">
        <p class="select-tip">请选择一个目录来存放测试任务：</p>
        <el-tree
          ref="folderTreeRef"
          :data="folderTreeData"
          :props="{ 
            children: 'children',
            label: 'label'
          }"
          node-key="id"
          default-expand-all
          highlight-current
          @node-click="handleFolderNodeClick"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <el-icon v-if="data.type === '1'"><Folder /></el-icon>
              <span class="node-label">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="folderSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmFolderSelection" :disabled="!selectedFolder">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入任务对话框 -->
    <el-dialog v-model="importDialogVisible" title="导入任务" width="50%" destroy-on-close>
      <div class="import-content">
        <el-form :model="importForm" label-width="120px">
          <el-form-item label="选择文件">
            <el-upload
              ref="uploadRef"
              v-model:file-list="importFileList"
              :auto-upload="false"
              :limit="1"
              accept=".json"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将JSON文件拖到此处，或<em>点击上传</em>
              </div>
              <!-- <template #tip>
                <div class="el-upload__tip">
                  只能上传JSON文件
                </div>
              </template> -->
            </el-upload>
          </el-form-item>
          
          <el-form-item label="目标目录">
            <el-tree-select
              v-model="importForm.folder_id"
              :data="importFolderTreeData"
              :props="{
                children: 'children',
                label: 'label',
                value: 'id'
              }"
              placeholder="请选择目标目录"
              check-strictly
              :render-after-expand="false"
              style="width: 100%"
              :disabled-node="isNotFolder"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport" :loading="importLoading" :disabled="!importForm.file || !importForm.folder_id">
            确认导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useStore } from '@/stores'
import { useUiTestStore } from '@/stores/ui_test'
import { getAllTask } from '@/apis/ui_test/get_all_task'
import { deleteTask } from '@/apis/ui_test/delete_task'
import { runTask } from '@/apis/ui_test/run_task'
import { getExecutions } from '@/apis/ui_test/get_executions'
import { getAllExecutions } from '@/apis/ui_test/post_all_executions'
import { getTaskStatus } from '@/apis/ui_test/get_task_status'
import { getTaskLog } from '@/apis/ui_test/get_task_log'
import { getAllOpenaiConfig } from '@/apis/project/openai_config/get_all_openai_config'
import { formatDate } from '@/util/format_date'
import { getUiTestTree } from '@/apis/ui_test/tree/get_all_tree'
import { Folder, Edit, VideoPlay, Document, List, CopyDocument, Delete, UploadFilled, ArrowDown, Cpu, DocumentCopy } from '@element-plus/icons-vue'
import { postCopyTask } from '@/apis/ui_test/post_copy_task'
import { postExportTask, postImportTask } from '@/apis/ui_test/import_export'

// 定义props
const props = defineProps({
  folderId: {
    type: Number,
    default: undefined
  }
})

// 定义emit
const emit = defineEmits(['refresh-tree'])

interface ProjectInfo {
  id: number;
  [key: string]: any;
}

const router = useRouter()
const mainStore = useStore()
const uiTestStore = useUiTestStore()
const loading = ref(false)
const taskList = ref<Task[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')
const selectedTasks = ref<Task[]>([])
const project_id = ref((mainStore.project_info as ProjectInfo).id)
const folder_id = ref<number | undefined>(props.folderId)
const selectedStatus = ref<string>('')

// 定义一个变量，用于存储当前选中节点的类型
const current_node_type = ref<string>('')

// 状态选项
const statusOptions = [
  { label: '全部', value: 'all' },
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' }
]

// 监听状态选择变化
watch(selectedStatus, () => {
  currentPage.value = 1
  loadTasks()
})

// 监听props变化
watch(() => props.folderId, (newValue) => {
  folder_id.value = newValue === 0 ? undefined : newValue
  // 不在这里调用loadTasks，避免可能的循环
}, { immediate: true })

// 批量运行相关
const batchRunDialogVisible = ref(false)

// 批量报告相关
const batchReportDialogVisible = ref(false)
const activeReportTab = ref('')
const reportLoading = ref<Record<number, boolean>>({})
const reportData = ref<Record<number, any>>({})

// 日志相关
const logDialogVisible = ref(false)
const currentTaskLog = ref('')
const taskStatusMap = ref<Record<number, any>>({})

const tableRef = ref()

// 定时器相关
let statusRefreshTimer: number | null = null

// 添加类型定义
interface Task {
  id: number;
  name: string;
  description: string;
  url: string;
  bridge_mode: boolean;
  create_time: string;
  creator: string;
  folder_id?: number;
}

interface ModelOption {
  id: number;
  name: string;
  type: string;
  value: Record<string, any>;
  creator: string;
  create_time: string;
  project: number;
}

const modelOptions = ref<ModelOption[]>([])
const selectedModel = ref<number | null>(null)

// 监听模型选择变化
watch(selectedModel, (newValue) => {
  if (newValue !== null) {
    const newData = {
      ...uiTestStore.ui_test_data,
      selectedModel: newValue
    }
    uiTestStore.setUiTestData(newData)
  }
}, { immediate: true })

// 过滤计算属性
const filteredModelOptions = computed(() => {
  return modelOptions.value.filter(model => model.type === '2')
})

const loadTasks = async () => {
  loading.value = true
  try {
    const response = await getAllTask(
      pageSize.value, 
      currentPage.value, 
      searchQuery.value, 
      project_id.value, 
      folder_id.value,
      selectedStatus.value
    )
    if (response?.data?.results?.data) {
      taskList.value = response.data.results.data
      total.value = response.data.count
      // 清空选中状态
      if (tableRef.value) {
        tableRef.value.clearSelection()
      }
      selectedTasks.value = []
      
      // 加载每个任务的状态
      const statusPromises = taskList.value.map(task => loadTaskStatus(task.id))
      await Promise.all(statusPromises)
      
      // 重启定时器（确保定时器针对新的任务列表）
      startStatusTimer()
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadTasks()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadTasks()
}

const handleSearch = () => {
  currentPage.value = 1
  loadTasks()
}

// 添加目录选择对话框相关功能
const folderSelectDialogVisible = ref(false)
const selectedFolder = ref<number | null>(null)
const folderTreeData = ref<any[]>([])
const folderTreeRef = ref<any>(null)

// 导入导出相关
const importDialogVisible = ref(false)
const importLoading = ref(false)
const importFileList = ref<any[]>([])
const uploadRef = ref<any>(null)
const importFolderTreeData = ref<any[]>([])
const importForm = ref({
  file: null as File | null,
  folder_id: null as number | null
})

const currentTaskType = ref('ai') // 添加当前任务类型状态

const showFolderSelectDialog = async (taskType: string = 'ai') => {
  // 保存当前任务类型
  currentTaskType.value = taskType

  // 重置选择
  selectedFolder.value = null

  // 获取树形目录结构
  try {
    const response = await getUiTestTree(project_id.value)
    if (response?.data?.data) {
      // 转换为树组件需要的格式
      folderTreeData.value = renameTreeKeys([response.data.data])
      folderSelectDialogVisible.value = true
    } else {
      ElMessage.warning('没有可用的目录，请先创建目录')
    }
  } catch (error) {
    console.error('获取目录列表失败:', error)
    ElMessage.error('获取目录列表失败，请稍后重试')
  }
}

// 转换后端数据格式为树组件需要的格式
function renameTreeKeys(data: any[]): any[] {
  return data.map(({ name, children, ...rest }) => ({
    label: name,
    ...rest,
    children: children ? renameTreeKeys(children) : null,
  }));
}

// 处理目录节点点击
const handleFolderNodeClick = (data: any) => {
  if (data.type === '1') { // 只允许选择目录类型
    selectedFolder.value = data.id
  } else {
    selectedFolder.value = null
  }
}

const confirmFolderSelection = () => {
  if (!selectedFolder.value) {
    ElMessage.warning('请选择一个目录')
    return
  }
  
  navigateToCreatePage(selectedFolder.value)
  folderSelectDialogVisible.value = false
}

const navigateToCreatePage = (folderId: number) => {
  if (!folderId) {
    ElMessage.warning('请选择一个目录')
    return
  }

  router.push({
    path: '/ui_test/smart_ui/create',
    query: {
      folder_id: folderId,
      task_type: currentTaskType.value
    }
  })
}

const editTask = (row: any) => {
  router.push({
    path: '/ui_test/smart_ui/create',
    query: {
      mode: 'edit',
      id: row.id
    }
  })
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    '确定要删除这个测试任务吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteTask(row.id)
      ElMessage.success('删除成功')
      loadTasks()
      // 通知父组件刷新树视图
      emit('refresh-tree')
    } catch (error) {
      console.error('删除失败:', error)
    }
  })
}

const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

const loadModelOptions = async () => {
  try {
    const response = await getAllOpenaiConfig(project_id.value)
    if (response?.data?.data) {
      modelOptions.value = response.data.data
      const savedModel = uiTestStore.ui_test_data.selectedModel
      
      if (savedModel && modelOptions.value.some(option => option.id === savedModel)) {
        selectedModel.value = savedModel
      } else {
        const firstVlmModel = modelOptions.value.find(model => model.type === '2')
        if (firstVlmModel) {
          selectedModel.value = firstVlmModel.id
        }
      }
    } else {
      modelOptions.value = []
      selectedModel.value = null
    }
  } catch (error) {
    console.error('加载模型配置失败:', error)
    modelOptions.value = []
    selectedModel.value = null
  }
}

// 修改运行相关方法
const handleRun = async (row: any) => {
  if (!selectedModel.value) {
    ElMessage.warning('请先选择模型')
    return
  }
  
  // 先检查任务状态
  try {
    const response = await getTaskStatus(row.id)
    if (response?.data?.data?.status === 'running') {
      ElMessage.warning('任务正在运行中，请稍后再试')
      return
    }
  } catch (error) {
    console.debug('获取任务状态失败:', error)
    // 如果获取状态失败，继续执行运行逻辑
  }
  
  try {
    await runTask({ 
      task_ids: [row.id],
      openai_config_id: selectedModel.value 
    })
    ElMessage.success('任务已开始运行')
    
    // 运行成功后延迟500ms再刷新该任务的状态
    setTimeout(async () => {
      await loadTaskStatus(row.id)
    }, 500)
  } catch (error) {
    console.error('运行失败:', error)
  }
}

const handleGlobalRun = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要运行的任务')
    return
  }
  batchRunDialogVisible.value = true
}

const confirmBatchRun = async () => {
  if (!selectedModel.value) {
    ElMessage.warning('请先选择模型')
    return
  }
  
  try {
    const taskIds = selectedTasks.value.map(task => task.id)
    await runTask({ 
      task_ids: taskIds,
      openai_config_id: selectedModel.value 
    })
    ElMessage.success('批量任务已开始运行')
    batchRunDialogVisible.value = false
    
    // 批量运行成功后延迟500ms再刷新所有选中任务的状态
    setTimeout(async () => {
      const statusPromises = selectedTasks.value.map(task => loadTaskStatus(task.id))
      await Promise.all(statusPromises)
    }, 500)
  } catch (error) {
    console.error('批量运行失败:', error)
  }
}

// 报告相关方法
const loadTaskReport = async (taskId: number) => {
  reportLoading.value[taskId] = true
  try {
    const response = await getExecutions(taskId)
    if (response?.data?.data) {
      reportData.value[taskId] = response.data.data
    }
  } catch (error) {
    console.error('加载报告失败:', error)
  } finally {
    reportLoading.value[taskId] = false
  }
}

const viewReport = async (row: any) => {
  selectedTasks.value = [row]
  batchReportDialogVisible.value = true
  activeReportTab.value = String(row.id)
  await loadTaskReport(row.id)
}

const handleBatchReport = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要查看报告的任务')
    return
  }

  batchReportDialogVisible.value = true
  activeReportTab.value = String(selectedTasks.value[0].id)

  // 加载所有选中任务的报告
  const taskIds = selectedTasks.value.map(task => task.id)
  try {
    const response = await getAllExecutions({ task_ids: taskIds })
    if (response?.data?.data) {
      taskIds.forEach((taskId, index) => {
        reportData.value[taskId] = response.data.data[index]
      })
    }
  } catch (error) {
    console.error('加载批量报告失败:', error)
  }
}

const handleBatchDelete = () => {
  if (!selectedTasks.value.length) {
    ElMessage.warning('请选择要删除的任务')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedTasks.value.length} 个测试任务吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      for (const task of selectedTasks.value) {
        await deleteTask(task.id)
      }
      ElMessage.success('批量删除成功')
      loadTasks()
      // 通知父组件刷新树视图
      emit('refresh-tree')
    } catch (error) {
      console.error('批量删除失败:', error)
    }
  })
}

// 修改获取任务状态的方法
const loadTaskStatus = async (taskId: number) => {
  try {
    const response = await getTaskStatus(taskId)
    if (response?.data?.data) {
      taskStatusMap.value[taskId] = response.data.data
    }
  } catch (error) {
    // 静默处理错误，将状态设置为未知
    console.debug('获取任务状态失败:', error)
    taskStatusMap.value[taskId] = { status: 'unknown' }
  }
}

// 定时刷新所有任务状态
const refreshAllTaskStatus = async () => {
  if (taskList.value.length > 0) {
    const statusPromises = taskList.value.map(task => loadTaskStatus(task.id))
    await Promise.all(statusPromises)
  }
}

// 启动定时器
const startStatusTimer = () => {
  if (statusRefreshTimer) {
    clearInterval(statusRefreshTimer)
  }
  statusRefreshTimer = window.setInterval(refreshAllTaskStatus, 20000) // 每20秒刷新一次
}

// 停止定时器
const stopStatusTimer = () => {
  if (statusRefreshTimer) {
    clearInterval(statusRefreshTimer)
    statusRefreshTimer = null
  }
}

// 修改状态相关的工具方法
const getStatusType = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'running':
      return 'warning'
    case 'success':
      return 'success'
    case 'failed':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'running':
      return '运行中'
    case 'success':
      return '成功'
    case 'failed':
      return '失败'
    default:
      return '待运行'
  }
}

// 修改查看日志的方法
const viewLog = async (row: any) => {
  try {
    const response = await getTaskLog(row.id)
    if (response?.data?.data) {
      currentTaskLog.value = response.data.data.execution_output || '暂无日志'
      logDialogVisible.value = true
    } else {
      currentTaskLog.value = '暂无日志'
      logDialogVisible.value = true
    }
  } catch (error) {
    // 静默处理错误
    console.debug('加载任务日志失败:', error)
    currentTaskLog.value = '暂无日志'
    logDialogVisible.value = true
  }
}

// 批量命令处理
const handleBatchCommand = (command: string) => {
  switch (command) {
    case 'report':
      handleBatchReport()
      break
    case 'delete':
      handleBatchDelete()
      break
  }
}

// 清除选择
const clearSelection = () => {
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 添加一个方法来接收从父组件传递过来的节点类型
const setCurrentNodeType = (type: string) => {
  current_node_type.value = type
}

// 处理创建任务命令
const handleCreateTask = (taskType: string) => {
  createTask(taskType)
}

// 在创建任务时检查当前节点类型
const createTask = (taskType: string = 'ai') => {
  // 如果存在folder_id且当前节点类型是目录(type === '1')，则直接传递
  if (folder_id.value && current_node_type.value === '1') {
    router.push({
      path: '/ui_test/smart_ui/create',
      query: {
        folder_id: folder_id.value,
        task_type: taskType
      }
    })
  } else {
    // 否则弹出对话框让用户选择目录
    showFolderSelectDialog(taskType)
  }
}

// 复制任务方法
const handleCopy = (row: any) => {
  ElMessageBox.confirm(
    '确认复制该测试任务？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(async () => {
    try {
      await postCopyTask({ task_id: row.id })
      ElMessage.success('任务复制成功')
      loadTasks()
      // 通知父组件刷新树视图
      emit('refresh-tree')
    } catch (error) {
      console.error('复制失败:', error)
    }
  })
}

// 导出任务方法
const handleExport = async () => {
  if (!selectedTasks.value.length) {
    ElMessage.warning('请选择要导出的任务')
    return
  }

  try {
    const taskIds = selectedTasks.value.map(task => task.id)
    const response = await postExportTask({ task_ids: taskIds })
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `ui_test_tasks_${new Date().getTime()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('任务导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 导入任务方法
const handleImport = async () => {
  // 重置表单
  importForm.value = {
    file: null,
    folder_id: null
  }
  importFileList.value = []
  
  // 加载目录树数据
  try {
    const response = await getUiTestTree(project_id.value)
    if (response?.data?.data) {
      // 转换并过滤树数据，只保留目录节点
      importFolderTreeData.value = renameTreeKeys([response.data.data])
    }
  } catch (error) {
    console.error('获取目录列表失败:', error)
  }
  
  importDialogVisible.value = true
}

// 处理文件选择
const handleFileChange = (file: any) => {
  importForm.value.file = file.raw
}

// 处理文件移除
const handleFileRemove = () => {
  importForm.value.file = null
}

// 判断节点是否不是目录（用于禁用非目录节点）
const isNotFolder = (node: any) => {
  return node.type !== '1' // 只有type为1的节点是目录，其他都禁用
}

// 确认导入
const confirmImport = async () => {
  if (!importForm.value.file) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  if (!importForm.value.folder_id) {
    ElMessage.warning('请选择目标目录')
    return
  }
  
  importLoading.value = true
  
  try {
    const formData = new FormData()
    formData.append('file', importForm.value.file)
    formData.append('project_id', project_id.value.toString())
    formData.append('folder_id', importForm.value.folder_id.toString())
    
    await postImportTask(formData)
    importDialogVisible.value = false
    loadTasks()
    // 通知父组件刷新树视图
    emit('refresh-tree')
  } catch (error) {
    console.error('导入失败:', error)
  } finally {
    importLoading.value = false
  }
}

onMounted(() => {
  loadTasks()
  loadModelOptions()
  startStatusTimer() // 启动定时器
})

onUnmounted(() => {
  stopStatusTimer() // 清理定时器
})

// 将loadTasks和setCurrentNodeType暴露给父组件
defineExpose({
  folder_id,
  loadTasks,
  setCurrentNodeType
})
</script>

<style scoped>
.ui-test-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  gap: 8px;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  width: 100%;
}

.compact-input {
  width: 220px;
  flex-shrink: 0;
}

/* .compact-select {
  width: 50px;
  flex-shrink: 0;
} */

.button-group {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 确保按钮文字不会换行 */
:deep(.button-group .el-button) {
  white-space: nowrap;
  padding-left: 8px;
  padding-right: 8px;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 3px 8px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  font-size: 12px;
  width: fit-content;
}

/* 添加滚动容器 */
.table-container {
  flex: 1;
  overflow: auto;
  min-height: 200px;
}

.pagination {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 - 仅在非常小的屏幕上生效 */
@media (max-width: 920px) {
  .header-row {
    flex-wrap: wrap;
  }
  
  .compact-input, 
  .compact-select {
    flex-grow: 1;
  }
  
  .button-group {
    margin-top: 8px;
    width: 100%;
    justify-content: flex-end;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  --el-table-header-bg-color: #ffffff;
  height: 100%;
}

:deep(.el-table__inner-wrapper) {
  height: 100%;
}

:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  padding: 8px 0;
}

:deep(.el-table td) {
  padding: 6px 0;
}

/* URL列的特殊处理 */
:deep(.el-table .cell a) {
  color: var(--el-color-primary);
  text-decoration: none;
}

/* 修复表格操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: nowrap;
}

:deep(.operation-buttons .el-button) {
  padding: 4px;
  margin: 0;
  height: 24px;
  width: 24px;
}

:deep(.operation-buttons .el-button .el-icon) {
  font-size: 12px;
}

/* 弹窗样式优化 */
.batch-run-content {
  margin: 20px 0;
  max-height: 400px;
  overflow-y: auto;
}

.batch-run-content ul {
  margin: 12px 0;
  padding-left: 20px;
  list-style-type: circle;
}

.batch-run-content li {
  margin: 8px 0;
  color: #606266;
}

/* iframe相关样式 */
.batch-report-content {
  height: calc(100vh - 120px);
}

/* 删除之前添加的复杂样式，只保留最简单必要的样式 */
:deep(.report-dialog .el-dialog__body) {
  height: calc(100vh - 60px); /* 减去对话框标题的高度 */
  padding: 10px;
}

.iframe-container {
  height: 100%;
}

.report-iframe {
  width: 100%;
  height: 100vh; /* 设置为视窗高度，确保显示完整 */
  border: none;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 简化模型选择相关样式 */
.compact-select {
  width: 200px;
}

/* 添加日志相关样式 */
.log-content {
  max-height: 600px;  
  overflow-y: auto;
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 16px;
}

.log-content pre {
  margin: 0;
  color: #fff;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 状态标签样式 */
:deep(.el-tag) {
  min-width: 60px;
  text-align: center;
}

/* 添加目录选择对话框样式 */
.folder-select-content {
  margin: 20px 0;
  max-height: 400px;
  overflow-y: auto;
}

.folder-select-content .select-tip {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.folder-select-content .el-tree {
  margin-top: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.folder-select-content .tree-node {
  display: flex;
  align-items: center;
}

.folder-select-content .node-label {
  margin-left: 5px;
}

/* 导入对话框样式 */
.import-content {
  margin: 20px 0;
}

.import-content .el-form-item {
  margin-bottom: 24px;
}

.import-content .el-upload {
  width: 100%;
}

.import-content .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.import-content .el-upload-dragger:hover {
  border-color: #409eff;
}

.import-content .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.import-content .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

.import-content .el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
}
</style>