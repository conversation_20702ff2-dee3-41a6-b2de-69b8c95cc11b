<template>
    <div class="smart-api-container">
      <div class="content-wrapper">
        <div class="content-container">
          <!-- 创建用例 -->
          <div class="api-section">
            <div class="icon-wrapper">
              <img src="@/assets/icon/smart_api/1.png" alt="创建用例" class="section-icon">
            </div>
            <h3 class="section-title">创建用例</h3>
            <p class="section-description">
              零代码可视化操作界面，产品经理、测试人员、业务人员都能轻松上手。支持手动编写、批量导入、AI智能生成等多种方式，让测试用例创建更简单高效。
            </p>
          </div>
  
          <!-- 执行用例 -->
          <div class="api-section">
            <div class="icon-wrapper">
              <img src="@/assets/icon/smart_api/2.png" alt="执行用例" class="section-icon">
            </div>
            <h3 class="section-title">执行用例</h3>
            <p class="section-description">
              一键执行，无需编程基础。内置智能化多模态测试引擎，任何角色都能轻松开展UI自动化测试，快速发现产品体验问题。
            </p>
          </div>
  
          <!-- 查看报告 -->
          <div class="api-section">
            <div class="icon-wrapper">
              <img src="@/assets/icon/smart_api/3.png" alt="查看报告" class="section-icon">
            </div>
            <h3 class="section-title">查看报告</h3>
            <p class="section-description">
              自动生成清晰直观的可视化测试报告，包含详细的执行视频回放。测试、产品、开发等各角色都能快速理解问题，协同提升产品质量。
            </p>
          </div>
        </div>
        
        <div class="button-container">
          <el-button type="primary" size="large" @click="handleCreate">新增测试任务</el-button>
        </div>
      </div>

      <!-- 添加目录选择对话框 -->
      <el-dialog v-model="folderSelectDialogVisible" title="选择目录" width="40%">
        <div class="folder-select-content">
          <p class="select-tip">请选择一个目录来存放测试任务：</p>
          <el-tree
            ref="folderTreeRef"
            :data="folderTreeData"
            :props="{ 
              children: 'children',
              label: 'label'
            }"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleFolderNodeClick"
            class="folder-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="tree-icon">
                  <el-icon v-if="data.type === '1'" class="folder-icon"><Folder /></el-icon>
                </span>
                <span class="node-label">{{ node.label }}</span>
              </div>
            </template>
          </el-tree>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="folderSelectDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmFolderSelection" :disabled="!selectedFolder">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup lang='ts'>
  import { ref, reactive } from 'vue'
  import { ElMessage } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useStore } from '@/stores'
  import { Folder } from '@element-plus/icons-vue'
  import { getUiTestTree } from '@/apis/ui_test/tree/get_all_tree'
  
  interface ProjectInfo {
    id: number;
    [key: string]: any;
  }
  
  const router = useRouter()
  const mainStore = useStore()
  const project_id = ref((mainStore.project_info as ProjectInfo).id)
  
  // 添加目录选择对话框相关数据
  const folderSelectDialogVisible = ref(false)
  const selectedFolder = ref<number | null>(null)
  const folderTreeData = ref<any[]>([])
  const folderTreeRef = ref<any>(null)
  
  const handleCreate = () => {
    showFolderSelectDialog()
  }
  
  // 显示目录选择对话框
  const showFolderSelectDialog = async () => {
    // 重置选择
    selectedFolder.value = null
    
    // 获取树形目录结构
    try {
      const response = await getUiTestTree(project_id.value)
      if (response?.data?.data) {
        // 转换为树组件需要的格式
        folderTreeData.value = renameTreeKeys([response.data.data])
        folderSelectDialogVisible.value = true
      } else {
        ElMessage.warning('没有可用的目录，请先创建目录')
      }
    } catch (error) {
      console.error('获取目录列表失败:', error)
      ElMessage.error('获取目录列表失败，请稍后重试')
    }
  }
  
  // 转换后端数据格式为树组件需要的格式
  function renameTreeKeys(data: any[]): any[] {
    return data.map(({ name, children, ...rest }) => ({
      label: name,
      ...rest,
      children: children ? renameTreeKeys(children) : null,
    }))
  }
  
  // 处理目录节点点击
  const handleFolderNodeClick = (data: any) => {
    if (data.type === '1') { // 只允许选择目录类型
      selectedFolder.value = data.id
    } else {
      selectedFolder.value = null
    }
  }
  
  // 确认选择目录
  const confirmFolderSelection = () => {
    if (!selectedFolder.value) {
      ElMessage.warning('请选择一个目录')
      return
    }
    
    navigateToCreatePage(selectedFolder.value)
    folderSelectDialogVisible.value = false
  }
  
  // 跳转到创建页面
  const navigateToCreatePage = (folderId: number) => {
    if (!folderId) {
      ElMessage.warning('请选择一个目录')
      return
    }

    router.push({
      path: '/ui_test/smart_ui/create',
      query: {
        folder_id: folderId
      }
    })
  }
  </script>
  
  <style scoped>
  :deep(body),
  :deep(html) {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
  }
  
  .smart-api-container {
    height: 100%;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    overflow: hidden;
    box-sizing: border-box;
  }
  
  .content-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .content-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-wrap: nowrap;
    gap: 20px;
    justify-content: center;
    padding: 0 20px;
    box-sizing: border-box;
  }
  
  .api-section {
    flex: 1;
    min-width: 260px;
    max-width: 320px;
    padding: 24px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
  }
  
  .icon-wrapper {
    width: 60px;
    height: 60px;
    margin-bottom: 16px;
  }
  
  .section-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
  }
  
  .section-description {
    font-size: 13px;
    color: #606266;
    line-height: 1.5;
    margin: 0;
  }
  
  .button-container {
    margin-top: 30px;
  }
  
  .button-container .el-button {
    padding: 12px 40px;
    font-size: 14px;
  }

  /* 添加目录选择对话框样式 */
  .folder-select-content {
    margin: 20px 0;
    max-height: 400px;
    overflow-y: auto;
  }

  .folder-select-content .select-tip {
    margin-bottom: 15px;
    color: #606266;
    font-size: 14px;
  }

  .folder-select-content .folder-tree {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    scrollbar-width: thin;
    scrollbar-color: #C1C1C1 #f0f0f0;
  }

  .folder-select-content .folder-tree::-webkit-scrollbar {
    width: 6px;
  }

  .folder-select-content .folder-tree::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
  }

  .folder-select-content .folder-tree::-webkit-scrollbar-thumb {
    background-color: #C1C1C1;
    border-radius: 4px;
  }

  .folder-select-content .folder-tree::-webkit-scrollbar-thumb:hover {
    background: #909090;
  }

  .folder-select-content .tree-node {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.3s;
    box-sizing: border-box;
    height: 38px;
  }

  .folder-select-content .tree-node:hover {
    background-color: #f0f7ff;
  }

  .folder-select-content .tree-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }

  .folder-select-content .folder-icon {
    color: #e6a23c;
    font-size: 18px;
  }

  .folder-select-content .node-label {
    margin-left: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }

  .folder-select-content :deep(.el-tree-node) {
    padding: 4px 0;
  }

  .folder-select-content :deep(.el-tree-node__content) {
    height: 38px;
    padding: 0 !important;
  }

  .folder-select-content :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #ecf5ff !important;
    border-radius: 6px;
  }

  .folder-select-content :deep(.el-tree-node__children) {
    padding-left: 16px;
  }

  .folder-select-content :deep(.el-tree-node__expand-icon) {
    font-size: 14px;
    color: #909399;
    transition: all 0.2s;
    margin-right: 5px;
  }

  .folder-select-content :deep(.el-tree-node__expand-icon.expanded) {
    transform: rotate(90deg);
  }

  .folder-select-content :deep(.el-tree-node__content:hover) {
    background-color: transparent;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  </style>