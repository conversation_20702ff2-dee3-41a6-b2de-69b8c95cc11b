<template>
  <div class="ui-test-layout resizable-container">
    <div class="split-pane">
      <div class="aside-wrapper" :style="{ width: leftWidth + 'px' }">
        <el-card class="tree-card" shadow="never">
          <tree-component ref="treeComponentRef" @node-click="handleNodeClick" @refresh-list="refreshList" />
        </el-card>
      </div>
      <div 
        class="resizer"
        @mousedown="startResize"
        @touchstart="startTouchResize">
        <div class="resizer-handle"></div>
      </div>
      <div class="main-container" :style="{ width: `calc(100% - ${leftWidth}px - 10px)` }">
        <el-card class="list-card" shadow="never">
          <index-list ref="indexListRef" :folder-id="currentFolderId" @refresh-tree="refreshTree" />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import TreeComponent from './tree_component.vue'
import IndexList from './index_list.vue'

interface TreeNode {
  id: number;
  type: string;
  [key: string]: any;
}

const currentFolderId = ref<number | undefined>(undefined)
const indexListRef = ref<InstanceType<typeof IndexList> | null>(null)
const treeComponentRef = ref<InstanceType<typeof TreeComponent> | null>(null)

const handleNodeClick = (node: TreeNode) => {
  // 如果是任务类型(type=0)或目录类型(type=1)，都将这个id传给列表组件
  if (node.type === '0' || node.type === '1') {
    currentFolderId.value = node.id
    if (indexListRef.value) {
      // 更新folder_id并重新加载数据
      indexListRef.value.folder_id = node.id
      // 传递节点类型
      indexListRef.value.setCurrentNodeType(node.type)
      indexListRef.value.loadTasks()
    }
  }
}

// 添加刷新树组件的方法
const refreshTree = () => {
  if (treeComponentRef.value) {
    treeComponentRef.value.loadTreeData()
  }
}

// 添加刷新列表组件的方法
const refreshList = () => {
  if (indexListRef.value) {
    indexListRef.value.loadTasks()
  }
}

// 拖拽分隔线相关逻辑
const leftWidth = ref(300) // 初始宽度300px
const minWidth = 0 // 最小宽度200px
const maxWidth = 500 // 最大宽度500px
let startX = 0
let startWidth = 0
let isDragging = false

// 鼠标拖拽开始
const startResize = (e: MouseEvent) => {
    isDragging = true
    document.body.classList.add('resizing')
    startX = e.clientX
    startWidth = leftWidth.value

    // 添加事件监听
    document.addEventListener('mousemove', resize)
    document.addEventListener('mouseup', stopResize)
    
    // 防止选中文本
    e.preventDefault()
}

// 触摸拖拽开始
const startTouchResize = (e: TouchEvent) => {
    isDragging = true
    document.body.classList.add('resizing')
    startX = e.touches[0].clientX
    startWidth = leftWidth.value

    // 添加事件监听
    document.addEventListener('touchmove', touchResize)
    document.addEventListener('touchend', stopTouchResize)
    
    // 防止选中文本
    e.preventDefault()
}

// 鼠标拖拽过程
const resize = (e: MouseEvent) => {
    if (!isDragging) return
    
    const offset = e.clientX - startX
    let newWidth = startWidth + offset
    
    // 限制最小和最大宽度
    if (newWidth < minWidth) newWidth = minWidth
    if (newWidth > maxWidth) newWidth = maxWidth
    
    leftWidth.value = newWidth
}

// 触摸拖拽过程
const touchResize = (e: TouchEvent) => {
    if (!isDragging) return
    
    const offset = e.touches[0].clientX - startX
    let newWidth = startWidth + offset
    
    // 限制最小和最大宽度
    if (newWidth < minWidth) newWidth = minWidth
    if (newWidth > maxWidth) newWidth = maxWidth
    
    leftWidth.value = newWidth
}

// 鼠标拖拽结束
const stopResize = () => {
    isDragging = false
    document.body.classList.remove('resizing')
    document.removeEventListener('mousemove', resize)
    document.removeEventListener('mouseup', stopResize)
}

// 触摸拖拽结束
const stopTouchResize = () => {
    isDragging = false
    document.body.classList.remove('resizing')
    document.removeEventListener('touchmove', touchResize)
    document.removeEventListener('touchend', stopTouchResize)
}

// 组件卸载时清理事件监听和样式
onUnmounted(() => {
    document.body.classList.remove('resizing')
    document.removeEventListener('mousemove', resize)
    document.removeEventListener('mouseup', stopResize)
    document.removeEventListener('touchmove', touchResize)
    document.removeEventListener('touchend', stopTouchResize)
})
</script>

<style scoped>
.ui-test-layout {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  overflow: hidden;
}

.split-pane {
  display: flex;
  height: 100%;
  position: relative;
}

.aside-wrapper {
  height: 100%;
  overflow: hidden;
  background-color: #ffffff;
  padding: 12px 0 12px 12px;
  box-sizing: border-box;
  flex-shrink: 0;
  transition: width 0.1s;
}

.main-container {
  height: 100%;
  padding: 12px 12px 12px 0;
  overflow: hidden;
  box-sizing: border-box;
  flex-grow: 1;
  transition: width 0.1s;
}

.tree-card,
.list-card {
  height: 100%;
  overflow: hidden;
  border-radius: 4px;
}

.tree-card {
  background-color: transparent !important;
  box-shadow: none !important;
}

.list-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.tree-card :deep(.el-card__body) {
  height: 100%;
  padding: 0;
  overflow: hidden;
  background-color: transparent;
}

.list-card :deep(.el-card__body) {
  height: 100%;
  padding: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.resizer {
  width: 10px;
  height: 100%;
  background-color: #ffffff;
  cursor: col-resize;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  flex-shrink: 0;
  z-index: 10;
}

.resizer-handle {
  width: 2px;
  height: 30px;
  background-color: #dcdfe6;
  border-radius: 1px;
}

.resizer:hover .resizer-handle {
  background-color: #409eff;
}

.resizable-container.dragging {
  cursor: col-resize;
}
</style>

<style>
/* 全局样式，在body上添加拖拽时的样式 */
body.resizing {
  cursor: col-resize !important;
  user-select: none;
}

body.resizing * {
  cursor: col-resize !important;
}
</style> 