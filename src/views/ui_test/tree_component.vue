<template>
  <div class="tree-container">
    <div class="search-container">
      <el-input v-model="filterText" placeholder="输入名称查询..." class="search-input" clearable>
        <template #prefix>
          <el-icon class="search-icon">
            <Search />
          </el-icon>
        </template>
      </el-input>
    </div>

    <el-tree v-loading="treeLoading" ref="treeRef" class="filter-tree" :data="treeData" :props="defaultProps"
      default-expand-all :filter-node-method="filterNode" :highlight-current="true" :expand-on-click-node="false"
      node-key="id" @node-click="handleNodeClick">
      <template #default="{ node, data }">
        <div class="tree-node">
          <span class="tree-icon">
            <el-icon v-if="data.type === '1'" class="folder-icon">
              <Folder />
            </el-icon>
            <el-icon v-else class="document-icon">
              <Document />
            </el-icon>
          </span>
          <span class="node-label">{{ node.label }}</span>
          <div class="dropdown-container" v-if="data.type === '1'">
            <el-dropdown trigger="click" @command="handleCommand($event, data)" placement="bottom-end">
              <span class="dropdown-trigger">
                <el-icon>
                  <More />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="data.type === '1'" command="add">
                    <el-icon class="dropdown-icon">
                      <FolderAdd />
                    </el-icon> 新建目录
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '1'" command="addAiTask">
                    <el-icon class="dropdown-icon">
                      <Plus />
                    </el-icon> 新建Agent任务
                  </el-dropdown-item>
                  <el-dropdown-item v-if="data.type === '1'" command="addPlaywrightTask">
                    <el-icon class="dropdown-icon">
                      <Plus />
                    </el-icon> 新建Playwright任务
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="data.label === '顶级目录'" command="edit">
                    <el-icon class="dropdown-icon">
                      <Edit />
                    </el-icon> 编辑
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="data.label === '顶级目录'" command="delete">
                    <el-icon class="dropdown-icon">
                      <Delete />
                    </el-icon> 删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="dropdown-container" v-else>
            <el-dropdown trigger="click" @command="handleCommand($event, data)" placement="bottom-end">
              <span class="dropdown-trigger">
                <el-icon>
                  <More />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon class="dropdown-icon">
                      <Edit />
                    </el-icon> 编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="delete">
                    <el-icon class="dropdown-icon">
                      <Delete />
                    </el-icon> 删除
                  </el-dropdown-item>
                  <el-dropdown-item command="move">
                    <el-icon class="dropdown-icon">
                      <Position />
                    </el-icon> 移动
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </el-tree>

    <!-- 添加目录对话框 -->
    <el-dialog v-model="addDialogVisible" title="新建目录" width="30%" destroy-on-close append-to-body>
      <el-form :model="folderForm" label-width="80px">
        <el-form-item label="目录名称">
          <el-input v-model="folderForm.name" autocomplete="off" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddFolder">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑目录对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑目录" width="30%" destroy-on-close append-to-body>
      <el-form :model="folderForm" label-width="80px">
        <el-form-item label="目录名称">
          <el-input v-model="folderForm.name" autocomplete="off" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditFolder">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加移动任务对话框 -->
    <el-dialog v-model="moveDialogVisible" title="移动任务" width="40%" destroy-on-close append-to-body>
      <div class="folder-select-content">
        <p class="select-tip">请选择目标目录：</p>
        <el-tree v-loading="moveTreeLoading" ref="moveTreeRef" :data="moveTreeData" :props="{
          children: 'children',
          label: 'label'
        }" node-key="id" default-expand-all highlight-current @node-click="handleMoveNodeClick" class="folder-tree">
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="tree-icon">
                <el-icon v-if="data.type === '1'" class="folder-icon">
                  <Folder />
                </el-icon>
              </span>
              <span class="node-label">{{ node.label }}</span>
            </div>
          </template>
        </el-tree>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmMoveTask" :disabled="!selectedMoveFolder">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Folder, Document, More, Plus, Edit, Delete, Search, FolderAdd, Position } from '@element-plus/icons-vue'
import { getUiTestTree } from '@/apis/ui_test/tree/get_all_tree'
import { postUiTestTree } from '@/apis/ui_test/tree/post_quick_tree'
import { putUiTestTree } from '@/apis/ui_test/tree/put_quick_tree'
import { deleteUiTestTree } from '@/apis/ui_test/tree/delete_quick_tree'
import { postMoveUiTask } from '@/apis/ui_test/tree/post_move_quick_tree'
import { getAllTask } from '@/apis/ui_test/get_all_task'
import { useStore } from '@/stores'
import { useRouter } from 'vue-router'

interface TreeNode {
  name?: string;
  label?: string;
  id: number;
  type: string;
  parent_id: number | null;
  children?: TreeNode[] | null;
  [key: string]: any;
}

interface ProjectInfo {
  id: number;
  [key: string]: any;
}

const emit = defineEmits(['node-click', 'refresh-list'])

const mainStore = useStore()
const project_id = ref<number>((mainStore.project_info as ProjectInfo).id)

const treeRef = ref<any>(null)
const filterText = ref('')
const treeData = ref<TreeNode[]>([])
const treeLoading = ref(false)
const currentNode = ref<TreeNode | null>(null)

// 新增目录相关
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const folderForm = ref({
  name: '',
  parent: 0
})

// 移动任务相关
const moveDialogVisible = ref(false)
const moveTreeData = ref<TreeNode[]>([])
const moveTreeLoading = ref(false)
const selectedMoveFolder = ref<number | null>(null)
const taskIdToMove = ref<number>(0)
const moveTreeRef = ref<any>(null)

const router = useRouter()

// 监听搜索输入
watch(filterText, (val) => {
  treeRef.value?.filter(val)
})

// 树节点过滤方法
const filterNode = (value: string, data: TreeNode) => {
  if (!value) return true
  return data.label?.toString().toLowerCase().includes(value.toLowerCase())
}

// 处理下拉菜单命令
const handleCommand = (command: string, data: TreeNode) => {
  // 先设置当前节点，确保后续操作基于正确的节点
  currentNode.value = data

  switch (command) {
    case 'add':
      addFolder(data.id)
      break
    case 'addAiTask':
      createTask(data.id, 'ai')
      break
    case 'addPlaywrightTask':
      createTask(data.id, 'playwright')
      break
    case 'edit':
      editFolder(data.id, data.label || '')
      break
    case 'delete':
      confirmDelete(data.id)
      break
    case 'move':
      moveTask(data.id)
      break
  }
}

// 转换后端数据格式为树组件需要的格式
function renameKeys(data: TreeNode[]): TreeNode[] {
  return data.map(({ name, children, ...rest }) => ({
    label: name,
    ...rest,
    children: children ? renameKeys(children) : null,
  }));
}

const defaultProps = {
  children: 'children',
  label: 'label'
}

// 加载树数据
const loadTreeData = async () => {
  treeLoading.value = true
  try {
    const response = await getUiTestTree(project_id.value)
    if (response?.data?.data) {
      const treeDataFromServer = response.data.data
      treeData.value = renameKeys([treeDataFromServer])
    }
  } catch (error) {
    console.error('加载树数据失败:', error)
  } finally {
    treeLoading.value = false
  }
}

// 节点点击处理
const handleNodeClick = (data: TreeNode) => {
  currentNode.value = data
  emit('node-click', data)
}

// 添加目录
const addFolder = (parentId: number) => {
  // 重置表单
  folderForm.value = {
    name: '',
    parent: parentId
  }
  addDialogVisible.value = true
}

// 确认添加目录
const confirmAddFolder = async () => {
  if (!folderForm.value.name.trim()) {
    ElMessage.warning('目录名称不能为空')
    return
  }

  try {
    const data = {
      name: folderForm.value.name,
      parent: folderForm.value.parent,
      type: '1', // 固定传1表示目录
      project: project_id.value // 确保传递项目ID
    }
    await postUiTestTree(data)
    ElMessage.success('目录创建成功')
    addDialogVisible.value = false
    await loadTreeData() // 重新加载树数据
    emit('refresh-list') // 通知父组件刷新列表
  } catch (error) {
    console.error('创建目录失败:', error)
  }
}

// 编辑目录
const editFolder = (id: number, name: string) => {
  // 确保currentNode已设置
  if (!currentNode.value) {
    currentNode.value = treeRef.value?.getCurrentNode() || null
  }

  folderForm.value = {
    name: name,
    parent: currentNode.value?.parent_id || 0
  }
  editDialogVisible.value = true
}

// 确认编辑目录
const confirmEditFolder = async () => {
  if (!folderForm.value.name.trim()) {
    ElMessage.warning('目录名称不能为空')
    return
  }

  // 确保有当前节点
  if (!currentNode.value) {
    ElMessage.warning('未选择目录')
    return
  }

  try {
    const data = {
      name: folderForm.value.name,
      parent: folderForm.value.parent,
      type: currentNode.value.type,
      project: project_id.value
    }
    await putUiTestTree(currentNode.value.id, data)
    ElMessage.success('目录更新成功')
    editDialogVisible.value = false
    await loadTreeData() // 重新加载树数据
    emit('refresh-list') // 通知父组件刷新列表
  } catch (error) {
    console.error('更新目录失败:', error)
  }
}

// 确认删除
const confirmDelete = (id: number) => {
  // 确保有当前节点
  if (!currentNode.value) {
    ElMessage.warning('未选择目录')
    return
  }

  ElMessageBox.confirm(
    '确定要删除该目录吗？删除后将无法恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteUiTestTree(id)
      ElMessage.success('删除成功')
      await loadTreeData() // 重新加载树数据
      emit('refresh-list') // 通知父组件刷新列表
    } catch (error) {
      console.error('删除失败:', error)
    }
  }).catch(() => {
    // 取消删除操作
  })
}

// 创建任务
const createTask = (folderId: number, taskType: string = 'ai') => {
  router.push({
    path: '/ui_test/smart_ui/create',
    query: {
      folder_id: folderId,
      task_type: taskType
    }
  })
}

// 移动任务
const moveTask = async (id: number) => {
  // 确保当前节点已设置并且是任务节点(type='0')
  if (currentNode.value && currentNode.value.type === '0') {
    // 先获取实际的任务ID
    await getTaskIdFromFolder(id)

    // 如果成功获取任务ID，则显示移动对话框
    if (taskIdToMove.value > 0) {
      selectedMoveFolder.value = null
      moveDialogVisible.value = true

      // 加载目录树数据
      loadMoveTreeData()
    }
  } else {
    ElMessage.warning('只能移动任务节点，不能移动目录')
  }
}

// 从文件夹中获取任务ID
const getTaskIdFromFolder = async (folderId: number) => {
  try {
    // 调用API获取指定目录下的任务列表
    const response = await getAllTask(10, 1, '', project_id.value, folderId)
    if (response?.data?.results?.data && response.data.results.data.length > 0) {
      // 获取任务的ID作为要移动的任务ID
      taskIdToMove.value = response.data.results.data[0].id
    } else {
      ElMessage.warning('该节点下没有找到任务')
      return false
    }
    return true
  } catch (error) {
    console.error('获取任务ID失败:', error)
    ElMessage.error('获取任务ID失败')
    return false
  }
}

// 加载移动对话框的目录树
const loadMoveTreeData = async () => {
  moveTreeLoading.value = true
  try {
    const response = await getUiTestTree(project_id.value)
    if (response?.data?.data) {
      const treeDataFromServer = response.data.data
      moveTreeData.value = renameKeys([treeDataFromServer])
    }
  } catch (error) {
    console.error('加载树数据失败:', error)
    ElMessage.warning('加载目录树失败')
  } finally {
    moveTreeLoading.value = false
  }
}

// 处理移动对话框中的节点点击
const handleMoveNodeClick = (data: TreeNode) => {
  if (data.type === '1') { // 只允许选择目录类型
    if (data.label === '顶级目录') {
      ElMessage.warning('不能选择顶级目录作为目标')
      selectedMoveFolder.value = null
      return
    }
    selectedMoveFolder.value = data.id
  } else {
    selectedMoveFolder.value = null
  }
}

// 确认移动任务
const confirmMoveTask = async () => {
  if (!selectedMoveFolder.value) {
    ElMessage.warning('请选择目标目录')
    return
  }

  try {
    const data = {
      task_id: taskIdToMove.value,
      target_folder_id: selectedMoveFolder.value
    }
    await postMoveUiTask(data)
    ElMessage.success('任务移动成功')
    moveDialogVisible.value = false
    await loadTreeData() // 重新加载树数据
    emit('refresh-list') // 通知父组件刷新列表
  } catch (error) {
    console.error('移动任务失败:', error)
  }
}

onMounted(() => {
  loadTreeData()
})

// 将loadTreeData方法暴露给父组件
defineExpose({
  loadTreeData
})
</script>

<style scoped>
.tree-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
}

.search-container {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  height: 32px;
}

.search-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-icon {
  color: #909399;
  font-size: 16px;
  margin-right: 4px;
}

.filter-tree {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  scrollbar-width: thin;
  scrollbar-color: #C1C1C1 #f0f0f0;
}

.filter-tree::-webkit-scrollbar {
  width: 6px;
}

.filter-tree::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.filter-tree::-webkit-scrollbar-thumb {
  background-color: #C1C1C1;
  border-radius: 4px;
}

.filter-tree::-webkit-scrollbar-thumb:hover {
  background: #909090;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px 8px;
  position: relative;
  border-radius: 6px;
  transition: all 0.3s;
  box-sizing: border-box;
  height: 38px;
}

.tree-node:hover {
  background-color: #f0f7ff;
}

.tree-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.folder-icon {
  color: #e6a23c;
  font-size: 18px;
}

.document-icon {
  color: #409eff;
  font-size: 18px;
}

.node-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.dropdown-container {
  margin-left: auto;
  flex-shrink: 0;
  padding: 4px;
  position: absolute;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.dropdown-trigger {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  transition: all 0.2s;
}

.dropdown-trigger:hover {
  background-color: #e7f1fd;
  color: #409eff;
}

.tree-node:hover .dropdown-container {
  opacity: 1;
}

.dropdown-icon {
  margin-right: 5px;
  font-size: 16px;
}

:deep(.el-tree-node) {
  padding: 4px 0;
}

:deep(.el-tree-node__content) {
  height: 38px;
  padding: 0 !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  border-radius: 6px;
}

/* 增强树形结构的层级感 */
:deep(.el-tree-node__children) {
  padding-left: 16px;
}

:deep(.el-tree-node__children .el-tree-node__content) {
  position: relative;
}

/* 改进展开图标样式 */
:deep(.el-tree-node__expand-icon) {
  font-size: 14px;
  color: #909399;
  transition: all 0.2s;
  margin-right: 5px;
  border-radius: 2px;
  padding: 2px;
}

:deep(.el-tree-node__expand-icon:hover) {
  background-color: #f0f0f0;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree-node__content:hover) {
  background-color: transparent;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 8px 16px;
  transition: all 0.3s;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f0f7ff;
}

:deep(.el-dropdown-menu__item.is-disabled) {
  opacity: 0.6;
}

:deep(.el-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 6px 0;
}

:deep(.el-tree) {
  --el-tree-node-hover-bg-color: transparent;
}

/* 保留对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 媒体查询适配小屏 */
@media (max-width: 768px) {
  .tree-container {
    padding: 12px;
  }

  .node-label {
    max-width: 70%;
    font-size: 13px;
  }

  .tree-icon {
    margin-right: 6px;
    width: 20px;
    height: 20px;
  }
}
</style> 