<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-button type="primary" v-permission="'GROUPS:新增'" style="margin-left: 10px;" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="name" label="用户组名" />
            <el-table-column prop="sort_order" label="排序" />
            <el-table-column v-permission="'GROUPS:编辑'" prop="if_enable" label="禁用">
                <template v-slot="scope">
                    <el-switch v-model="scope.row.if_enable" style="--el-switch-on-color: #F56C6C; "
                        @click="switchHandle(scope.row.id, scope.row.if_enable)" inline-prompt active-text="是"
                        inactive-text="否" :active-value=0 :inactive-value=1 />
                </template>
            </el-table-column>
            <el-table-column prop="desc" label="备注" />
            <el-table-column prop="creator" label="创建人" />
            <el-table-column prop="create_time" label="创建时间">
                <template v-slot="scope">
                    {{  formatDate(scope.row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center" width="220px">
                <template v-slot="scope">
                    <el-button v-permission="'GROUPS:权限'" type="success" :icon="Lock" circle @click="dialogPermissions(scope.row.id)"></el-button>
                    <el-button v-permission="'GROUPS:编辑'" type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <span style="margin-left: 10px;" v-permission="'GROUPS:删除'">
                        <group_del :groupId="scope.row.id" @deleteConfirmed="updateGroupList"></group_del>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    <group_add_dialog v-model="dialogVisible" @updateGroupList="updateGroupList"></group_add_dialog>
    <group_modify_dialog v-model="modifyDialogVisible" :modifyGroupId='modifyGroupId' :groupInfo="currentGroupInfo"
        @updateGroupList="updateGroupList"></group_modify_dialog>
    <group_permissions v-model="permissionsDialogVisible" :modifyGroupId='modifyGroupId'
        :groupMenuButtons="groupMenuButtons" :groupPermission="groupPermission" :allMenuButtons="allMenuButtons"
        @updateGroupList="updateGroupList"></group_permissions>
</template>
  
<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue'
import { getAllGroupInfo } from '@/apis/group/get_all_groupinfo'
import { Plus, Edit, Check, Lock } from '@element-plus/icons-vue';
import group_add_dialog from './components/group_add_dialog.vue';
import group_del from './components/group_del.vue';
import group_modify_dialog from './components/group_modify_dialog.vue';
import { putGroup } from '@/apis/group/put_group'
import { ElMessage } from 'element-plus'
import group_permissions from './components/group_permissions.vue';
import { getAllMenuButtons } from '@/apis/group/get_all_menu_buttons'
import { groupMenuButtonPermission } from '@/apis/get_group_menu_buttons_permission';
import { formatDate } from '@/util/format_date';


const loading = ref(true)

const queryParams = reactive({
    groupNameQuery: '',
    page_size: 10,
    page: 1
})

const total = ref(0)
const tableData = ref([])
const allMenuButtons: any = ref([])
const groupMenuButtons: any = ref([])
const groupPermission = ref([])


const dialogVisible = ref(false)
// console.log('dialogAddValue1',groupAddDialogVisible)
const modifyDialogVisible = ref(false)

const permissionsDialogVisible = ref(false)

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
    // console.log('dialogAddValue2',groupAddDialogVisible)
}

const modifyGroupId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentGroupInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyGroupId.value = id
    const group = tableData.value.find((group: any) => group.id === id) // 在 tableData 中查找用户的详细信息
    if (group) { currentGroupInfo.value = group } // 将找到的用户信息存储在 currentGroupInfo 中
}

const dialogPermissions = (id: number) => {
    modifyGroupId.value = id
    const group = tableData.value.find((group: any) => group.id === id) // 在 tableData 中查找用户的详细信息
    if (group) { currentGroupInfo.value = group } // 将找到的用户信息存储在 currentGroupInfo 中
    if (modifyGroupId.value) {
        getGroupPermissionTree()
    }
}


const getGroupList = async () => {
    const res = await getAllGroupInfo(queryParams.page_size, queryParams.page)
    if (res) {
        let groupList = res.data.results.data
        // 排序
        let sortedGroupList = groupList.sort((a: any, b: any) => a.sort_order - b.sort_order)
        tableData.value = sortedGroupList
        total.value = res.data.count
        loading.value = false
    }
    const allMenuButtosRes = await getAllMenuButtons()
    allMenuButtons.value = allMenuButtosRes?.data.data
}

getGroupList()

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getGroupList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getGroupList()
}

// 定义一个事件函数，让子组件触发，需要传递的数据作为事件的参数，这里是子组件直接传整个响应，父组件监听，并处理
// 监听 update 事件只要在上方的子组件引入中加入 @updateGroupList="updateGroupList"
const updateGroupList = (newGroups: any) => {
    if (newGroups) {
        tableData.value = newGroups.data.results.data
        total.value = newGroups.data.count
    }

}


const switchHandle = async (id: number, ifEnable: number) => {
    const data = { if_enable: ifEnable };
    const response = await putGroup(id, data)
    // console.log(id)
    if (response && response.data.code === 2000) {
        getGroupList()
        ElMessage.success('操作成功！')
    }
}

const getGroupPermissionTree = async () => {
    const groupPermissionTreeRes = await groupMenuButtonPermission(modifyGroupId.value)
    if (groupPermissionTreeRes && groupPermissionTreeRes.data.code === 2000) {
        if (groupPermissionTreeRes.data.data.length >= 0) {
            groupMenuButtons.value = groupPermissionTreeRes.data.data
            // 使用 nextTick 确保 groupMenuButtons 更新后再打开对话框
            nextTick(() => {
                permissionsDialogVisible.value = true
            })
        }
    }
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>