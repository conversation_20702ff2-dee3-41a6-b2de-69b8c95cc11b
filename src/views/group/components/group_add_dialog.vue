<template>
  <el-dialog v-model="dialogVisible" title="新增用户组" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="85px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="用户组名" prop="name" >
        <el-input v-model="ruleForm.name" placeholder="请输入用户组名"/>
      </el-form-item>
      <el-form-item label="排序" prop="sort_order">
        <el-input type="number" v-model="ruleForm.sort_order" />
      </el-form-item>
      <el-form-item label="说明" prop="desc">
        <el-input v-model="ruleForm.desc" placeholder="请输入说明"/>
      </el-form-item>
      <el-form-item label="禁用" prop="if_enable">
        <el-switch v-model="ruleForm.if_enable" style="--el-switch-on-color: #F56C6C; " :active-value=0 :inactive-value=1 />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postGroup } from '@/apis/group/post_group'
import { getAllGroupInfo } from '@/apis/group/get_all_groupinfo'
import { useStore } from '@/stores' 

interface UserInfo {
  username: string;
  // 其他属性...
}

interface MainStore {
  user_info: UserInfo;
  // 其他属性...
}

const mainStore = useStore() as MainStore;

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: Boolean
})

const dialogVisible = ref(false)

watchEffect(() => {
  dialogVisible.value = props.modelValue
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    ruleForm.name = ''
    ruleForm.sort_order = 999
    ruleForm.if_enable = 1
    ruleForm.desc = ''
  }
})

const emit = defineEmits(['update:modelValue', 'updateGroupList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  name: string
  sort_order: number
  if_enable: number
  creator: string
  desc: string
}


const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  name: '',
  sort_order: 999,
  if_enable: 1,
  creator: mainStore.user_info.username || '',
  desc: ''
})

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入用户组名', trigger: 'blur' },
  ],
  sort_order: [
    { required: true, message: '请填写排序', trigger: 'blur' },
    {
      pattern: /\b([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|0)\b/,
      message: '范围为0-999',
      trigger: 'blur'
    }
  ],
})


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await postGroup(ruleForm)
      if (response && response.data.code === 2000) {
        const groupInfoResponse = await getAllGroupInfo(10, 1)
        if (groupInfoResponse && groupInfoResponse.data.results.code === 2000) {
          ElMessage.success('操作成功！')
          // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
          emit('updateGroupList', groupInfoResponse)  // 触发 update 事件
        }
      }
    handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}

</script>
<style scoped></style>