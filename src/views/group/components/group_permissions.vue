<template>
    <el-dialog v-model="permissionsDialogVisible" title="权限分配" width="20%" :before-close="handleClose">
        <div v-if="isCheck" class="overlay">
            <el-button type="primary" @click="startEditing">开始编辑</el-button>
        </div>
        <el-tree ref="tree" :props="el_props" :data="data" node-key="code" show-checkbox default-expand-all
            :default-checked-keys="checkedCodes" :check-strictly="isCheck" @check="handleCheck" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="submit">
                    确认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>
    
<script setup lang='ts'>
import { ref, watchEffect } from 'vue'
import { ElMessage } from 'element-plus'
import { putGroupMenuButtonPermission } from '@/apis/group/put_group_menu_button_permission'
import { getAllGroupInfo } from '@/apis/group/get_all_groupinfo'


const tree: any = ref(null)
const isCheck = ref(true)


// 转换数据
function transformData(data: any) {
    return data.map((item: any) => {
        const newItem = { ...item, label: item.meta.title }
        newItem.children = []
        if (item.children && item.children !== null) {
            newItem.children.push(...transformData(item.children))
        }
        // 将按钮也显示出来 code没有这边加一个作为提交后的数据，让后端好存
        if (item.buttons) {
            newItem.children.push(...item.buttons.map((button: any) => ({ label: button, code: item.code + '|' + button })))
        }
        return newItem
    })
}

const data = ref([])
const checkedData: any = ref([])
const checkedCodes: any = ref([])


// 设置 props
const el_props = {
    label: 'label',
    children: 'children',
}
// modelValue是v-modle的默认属性名
const props = defineProps({
    modelValue: Boolean,
    modifyGroupId: Number,
    allMenuButtons: Array,
    groupMenuButtons: Array
})

const permissionsDialogVisible = ref(false)

const emit = defineEmits(['update:modelValue', 'updateGroupList']) // 定义emit函数

const handleClose = () => {
    emit('update:modelValue', false) // 更新modelValue的值
    isCheck.value = true
}

// @check只针对用户手动勾选节点复选框时触发 区别：@check-change 事件在节点选中状态发生改变时触发，不仅包括用户手动勾选，还包括通过代码改变节点的选中状态
const handleCheck = () => {
    isCheck.value = false
}

// 确保编辑能设置为联动
const startEditing = () => {
    isCheck.value = false;
}

watchEffect(() => {
    permissionsDialogVisible.value = props.modelValue

    // 将树数据给data渲染 在 Vue 3 Composition API 中，watchEffect 内部的变量不会自动暴露给模板。
    // 你需要将 data 定义为一个响应式引用（ref），然后在 watchEffect 中更新它。
    data.value = transformData(props.allMenuButtons)
    checkedData.value = props.groupMenuButtons
    checkedCodes.value = extractCodes(checkedData.value);
})


const submit = async () => {
    const checkedKeys = tree.value.getCheckedKeys()
    const halfCheckedKeys = tree.value.getHalfCheckedKeys()
    const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]

    const response = await putGroupMenuButtonPermission({ "groupId": props.modifyGroupId, "selectedMenuButton": allCheckedKeys })
    if (response && response.data.code === 2000) {
        const groupInfoResponse = await getAllGroupInfo(10, 1)
        if (groupInfoResponse && groupInfoResponse.data.results.code === 2000) {
            ElMessage.success('操作成功！')
            // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
            emit('updateGroupList', groupInfoResponse)  // 触发 update 事件
        }
    }
    handleClose()
}

interface Tree {
    name: string
}

// const handleCheckChange = (
//     data: Tree,
//     checked: boolean,
//     indeterminate: boolean
// ) => {
//     console.log(data, checked, indeterminate)

// }

// 显示后端返回的权限，并转换成el-tree能识别的code，button也要和父组件一致拼接
function extractCodes(data: any[]): string[] {
    let result: string[] = [];
    for (let item of data) {
        if (item.code) {
            result.push(item.code);
        }
        if (item.buttons) {
            for (let button of item.buttons) {
                result.push(item.code + '|' + button);
            }
        }
        if (item.children) {
            result = result.concat(extractCodes(item.children));
        }
    }
    return result;
}


</script>
<style scoped>
.overlay {
    position: absolute;
    top: 50px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; 
}
</style>