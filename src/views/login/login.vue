<template>
    <div class="login-background">
        <el-card class="box-card">
            <el-form v-loading="loading" :model="form" :rules="rules" ref="formRef" class="login-form" @keyup.enter="onSubmit">
                <div>
                    <div class="system-name">{{ systemName }}</div>
                </div>
                <div>
                    <el-form-item prop="username">
                        <el-input prefix-icon="User" placeholder="用户名" v-model="form.username" class="el-input" />
                    </el-form-item>
                </div>
                <div>
                    <el-form-item prop="password">
                        <el-input prefix-icon="Lock" show-password placeholder="密码" v-model="form.password"
                            class="el-input" />
                    </el-form-item>
                </div>
                <div>
                    <el-form-item prop="numberinput">
                        <el-input placeholder="请输入验证码" v-model="numberinput">
                            <template #suffix>
                                <div class="captcha-suffix" @click="refresh">
                                    <div class="rendom" ref="rendom">{{ numberrandom }}</div>
                                    <div class="rendomimg">
                                        <img :src="imageData">
                                    </div>
                                </div>
                            </template>
                        </el-input>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item>
                        <el-checkbox v-model="remeberMe" label="记住我" size="large" />
                    </el-form-item>
                </div>
                <div>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit" class="el-input">登 录</el-button>
                    </el-form-item>
                </div>
            </el-form>
            <div class="welcome-message">{{ welcomeMessage }}</div>
        </el-card>
    </div>
</template>

<script setup lang='ts'>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { loginApi } from '@/apis/login'
import { getUserInfo } from '@/apis/user/get_userinfo'
// import { allMenuButton } from '@/apis/get_all_menu_buttons';
import { getAllDictionary } from '@/apis/dictionary/get_all_dictionary';
import { groupMenuButtonPermission } from '@/apis/get_group_menu_buttons_permission'
import { useStore } from '@/stores'
import { usePermissionStore } from '@/stores/permission';
import { encrypt, decrypt } from "@/util/jsencrypt"
import { ElMessage } from 'element-plus'
import Cookies from 'js-cookie'
import html2canvas from 'html2canvas'

const loading = ref(false)
// 默认不记住
const remeberMe = ref(false)

const formRef: any = ref(null)

const form = reactive({
    username: '',
    password: ''
})

// 在页面加载时检查cookie
if (Cookies.get("rememberMe") === "true") {
    form.username = Cookies.get("username") || ''
    const encryptedPassword = Cookies.get("password") || ''
    // console.log('从cookie中读取的加密密码:', encryptedPassword)
    const decryptedPassword = decrypt(encryptedPassword) || ''
    // console.log('解密后的密码:', decryptedPassword)
    form.password = decryptedPassword
    remeberMe.value = true
} else {
    remeberMe.value = false
}


const systemName = ref('自动化测试平台')
const welcomeMessage = ref('用户名：工号，密码：Xyhj@123，登陆后可自行修改')
const router = useRouter()

// 在页面加载时获取字典数据并设置平台名称和欢迎消息
const loadSystemName = async () => {
    try {
        const dic_res = await getAllDictionary(100, 1, '', '');
        if (dic_res?.data?.data?.results) {
            const systemNameDict = dic_res.data.data.results.find((item: any) => item.dic_key === 'system_name');
            if (systemNameDict) {
                systemName.value = systemNameDict.dic_value || '自动化测试平台';
            }
            
            const welcomeMessageDict = dic_res.data.data.results.find((item: any) => item.dic_key === 'show_welcome_message');
            if (welcomeMessageDict) {
                welcomeMessage.value = welcomeMessageDict.dic_value || '用户名：工号，密码：Xyhj@123，登陆后可自行修改';
            }
        }
    } catch (error) {
        // 如果获取失败，保持默认值
        console.warn('获取字典数据失败，使用默认值');
    }
}


const rules = {
    username: [
        { required: true, message: '用户名不能为空', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '密码不能为空', trigger: 'blur' },
        // {min: 6, max: 14, message: '密码在6-14之间', trigger: 'blur'},
    ]
}

// 验证码部分
const numberrandom = ref()
const numberinput = ref()
const imageData = ref()
const rendom = ref()
const canvas = (data: any) => {
    nextTick(() => {
        html2canvas(data).then(canvas => {
            imageData.value = canvas.toDataURL('image/png');
        });
    })
}
const random = () => {
    numberrandom.value = Math.floor(Math.random() * 10000) % 10000
}
random()
const refresh = async () => {
    random()
    canvas(rendom.value)
    // console.log(rendom.value, 1)
}

onMounted(() => {
    canvas(rendom.value)
    loadSystemName()
})

const onSubmit = async () => {
    // 验证验证码输入框
    if (!numberinput.value) {
        ElMessage.warning('验证码不能为空');
        return;
    }

    if (numberinput.value != numberrandom.value && numberinput.value !== '8888') {
        ElMessage.error('验证码错误');
        return;
    }

    loading.value = true
    try {
        if (formRef.value) {
            const valid = await formRef.value.validate();
            // console.log(valid)
            if (!valid) {
                // console.log('验证错误');
                return;
            }
        } else {
            // console.error('formRef.value is null');
            return;
        }

        if (remeberMe.value) {
            const encryptedPassword = encrypt(form.password);
            // console.log('加密后的密码:', encryptedPassword)
            Cookies.set("username", form.username, { expires: 30 })
            Cookies.set("password", encryptedPassword || '', { expires: 30 })
            // 存入字符串 "true" or "false"
            Cookies.set("rememberMe", remeberMe.value.toString(), { expires: 30 })
        } else {
            Cookies.remove("username")
            Cookies.remove("password")
            Cookies.remove("rememberMe")
        }

        const response = await loginApi(form)

        const userStore = useStore() // 使用你的 store
        userStore.setToken(response.data.access_token) // 保存 token
        userStore.setRefreshToken(response.data.refresh_token)

        const userId = response.data.user_id
        const groupId = response.data.user_groupids[0]
        const userInfo = await getUserInfo(userId)
        userStore.setUserInfo(userInfo.data.data) // 保存用户信息

        // const allMenuButtonResponse = await allMenuButton()
        // userStore.setMenus(allMenuButtonResponse.data.data) // 保存 allMenuButton
        // console.log('存allMenuButton到pinia。。。')
        const permissionStore = usePermissionStore()
        const groupMenuButtonPermissionResponse = await groupMenuButtonPermission(groupId)
        userStore.setMenus(groupMenuButtonPermissionResponse.data.data) // 保存 allMenuButton
        permissionStore.setPermissionMenus(groupMenuButtonPermissionResponse.data.data)
        loading.value = false

        // 重定向到原来的页面或者主页
        const redirect = router.currentRoute.value.query.redirect
        if (redirect) {
            router.push({ path: redirect as string })
        } else {
            router.push('/ats/index')
        }

        const dic_res = await getAllDictionary(10, 1, '', '');
        if (dic_res) {
            // console.log("dic_res.data", dic_res.data.data.results)
            userStore.setDicList(dic_res.data.data.results);
        }

    } catch (error) {
        // console.error('catch报错' + error)
        loading.value = false
    }

}

</script>

<style lang="less" scoped>
.box-card {
    width: 380px;
    height: auto;
    padding: 28px 32px;
    position: absolute;
    top: 50%;
    left: 70%;
    transform: translate(-50%, -50%);
    border-radius: 16px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .el-input {
        margin-top: 18px;
        width: 100%;
        height: 40px;
        
        :deep(.el-input__wrapper) {
            border-radius: 6px;
        }
    }
    
    .el-button {
        height: 40px;
        border-radius: 6px;
        font-weight: 500;
    }
    
    .el-checkbox {
        margin: 8px 0;
        
        :deep(.el-checkbox__label) {
            color: #606266;
            font-size: 14px;
        }
    }
}

.login-background {
    background-image: url('../../../public/1920.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
}



.captcha-suffix {
    position: relative;
    cursor: pointer;
    margin-right: 8px;
}

.rendom {
    width: 80px;
    height: 28px;
    font-size: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: #495057;
    letter-spacing: 1px;
}

.rendomimg {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 4px;
}

.rendomimg img {
    width: 80px;
    height: 28px;
    border-radius: 4px;
}

.system-name {
    font-size: 22px;
    font-weight: 600;
    text-align: center;
    color: #2c3e50;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

// 底部欢迎消息样式优化
.welcome-message {
    margin-top: 16px;
    padding: 8px 12px;
    background: rgba(108, 117, 125, 0.03);
    border-radius: 4px;
    border-left: 2px solid #409eff;
    font-size: 12px;
    line-height: 1.4;
    color: #8590a6;
    text-align: center;
}

// 登录表单间距优化
.login-form {
    .el-form-item {
        margin-bottom: 20px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .box-card {
        width: 90%;
        max-width: 380px;
        padding: 24px 20px;
        margin: 0 20px;
    }
}
</style>
