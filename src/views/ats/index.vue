<template>
    <div class="dashboard-container">
        <div class="welcome-text">
            <h1>欢迎，{{ userName }}</h1>
            <p>选择您需要的功能开始操作</p>
        </div>
        <div class="card-container">
            <el-card
                v-for="item in cardItems"
                :key="item.title"
                class="card-item"
                :body-style="{ padding: '0px' }"
                shadow="hover"
            >
                <router-link :to="item.path" class="card-link">
                    <div class="card-content">
                        <div class="icon-wrapper">
                            <img :src="item.iconClass" alt="icon" class="card-icon" />
                        </div>
                        <div class="card-title">{{ item.title }}</div>
                    </div>
                </router-link>
            </el-card>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import SCENE from '@/assets/icon/index/SCENE.png'
import PROJECT from '@/assets/icon/index/PROJECT.png'
import API from '@/assets/icon/index/API.png'
import FAST from '@/assets/icon/index/FAST.png'
import { useStore } from '@/stores';

const mainStore = useStore()
const userName = mainStore.user_info.username

const cardItems = ref([
    { title: '接口自动化测试', iconClass: FAST, path: '/test_api/quick' },
    { title: '页面自动化测试', iconClass: API, path: '/ui_test/smart_ui' },
    { title: '新建项目', iconClass: PROJECT, path: '/project/config' },
    { title: '场景用例', iconClass: SCENE, path: '/test_api/scene' }    
]);
</script>

<style scoped>
.dashboard-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%);
}

.welcome-text {
    text-align: center;
    margin-bottom: 40px;
}

.welcome-text h1 {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.welcome-text p {
    font-size: 18px;
    color: #5e6d82;
    margin: 0;
}

.card-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.card-item {
    width: 220px;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: white;
    border: none;
}

.card-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.card-link {
    text-decoration: none;
    display: block;
}

.card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px 20px;
}

.icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #ffffff;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.card-item:hover .icon-wrapper {
    transform: scale(1.1);
}

.card-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.card-item:hover .card-title {
    color: #409EFF;
}
</style>