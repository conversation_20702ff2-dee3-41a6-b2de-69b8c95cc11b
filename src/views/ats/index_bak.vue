<template>
    <div class="stats-container">
      <el-row :gutter="20">
        <!-- 第一列，宽度较大 -->
        <el-col :span="14" class="column-bg-light">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <p class="stat-title">成员</p>
                  <p class="stat-number">100</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <p class="stat-title">环境</p>
                  <p class="stat-number">5</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <p class="stat-title">公共数据</p>
                  <p class="stat-number">10</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <!-- 第二列，宽度较小 -->
        <el-col :span="10" class="column-bg-dark">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <p class="stat-title">接口调试</p>
                  <p class="stat-number">78/100</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <p class="stat-title">接口用例</p>
                  <p class="stat-number">1</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <p class="stat-title">接口自动化用例</p>
                  <p class="stat-number">0</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
  </template>
  
  <style scoped>
.stats-container {
  background-color: #f5f5f5; /* Adjust the background color as needed */
  padding: 20px;
}

.stat-card {
  text-align: center;
  border-radius: 4px;
}

.stat-content {
  padding: 20px 0;
}

.stat-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
  
  /* 其他样式保持不变 */
  </style>