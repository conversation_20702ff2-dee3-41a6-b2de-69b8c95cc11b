<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-button type="primary" v-permission="'MENUS:新增'" style="margin-left: 10px;" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table v-loading="loading" v-if='showTable' :data="tableData" style="width: 100%" row-key="id" stripe lazy :load="load"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
            <el-table-column prop="menu_name" label="菜单名称"  />
            <el-table-column prop="menu_icon_path" label="菜单图标" >
                <template v-slot="scope">
                    <el-icon :size="20" style="margin-left: 20px;">
                        <component :is="scope.row.menu_icon_path" />
                    </el-icon>
                </template>
            </el-table-column>
            <el-table-column prop="menu_type" label="菜单类型" >
                <template v-slot="scope">
                    <div class="flex flex-wrap gap-2 my-2">
                        <el-tag :type="getTagType(scope.row.menu_type)" class="mx-1" effect="light" round>
                            {{ convertMenuType(scope.row.menu_type) }}
                        </el-tag>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="menu_order" label="排序" />
            <el-table-column prop="menu_router" label="路由地址" />
            <el-table-column prop="component" label="组件路径" />
            <el-table-column v-permission="'MENUS:编辑'" prop="if_hidden" label="隐藏" >
                <template v-slot="scope">
                    <el-switch v-model="scope.row.if_hidden" style="--el-switch-on-color: #F56C6C; "
                        @click="switchHandle(scope.row.id, scope.row.if_hidden)" inline-prompt active-text="是"
                        inactive-text="否" :active-value=0 :inactive-value=1 />
                </template>
            </el-table-column>
            <el-table-column prop="buttons" label="按钮" >
                <template v-slot="scope">
                    <span v-if="scope.row.menu_type === 2">
                        <el-tag v-for="(button, index) in scope.row.buttons" :key="index">
                            {{ button }}
                        </el-tag>
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button v-permission="'MENUS:编辑'" type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <span style="margin-left: 10px;" v-permission="'MENUS:删除'">
                        <menu_del :menuId="scope.row.id" @deleteConfirmed="updateMenuList"></menu_del>
                    </span>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <menu_add_dialog v-model="dialogVisible" :tableData='tableData' :buttons="buttonName" :menuTree="menuTree"
        @updateMenuList="updateMenuList">
    </menu_add_dialog>
    <menu_modify_dialog v-model="modifyDialogVisible" :tableData='tableData' :buttons="buttonName" :menuTree="menuTree"
        :modifyMenuId='modifyMenuId' @updateMenuList="updateMenuList">
    </menu_modify_dialog>
</template>
<script lang="ts" setup>
import { Edit, Plus } from '@element-plus/icons-vue';
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { Ref } from 'vue'
import menu_add_dialog from './components/menu_add_dialog.vue';
import { getParentMenus } from '@/apis/menu/get_parent_menu'
import { getChildrenMenus } from '@/apis/menu/get_children_menu'
import menu_modify_dialog from './components/menu_modify_dialog.vue'
import { getMenuTree } from '@/apis/menu/get_menu_tree';
import menu_del from './components/menu_del.vue';
import { putMenu } from '@/apis/menu/put_menu';
import { getButtons } from '@/apis/menu/get_buttons'

const loading = ref(true)
const showTable = ref(true)

interface Menus {
    id: number
    menu_name: string
    component: string
    menu_router: string
    menu_type: number
    menu_icon_path: string
    menu_order: number
    if_hidden: number
    hasChildren?: boolean
    children?: []
    menu_code: string
    p_menu_code?: any
    if_enable: number
    create_time: string
    creator_id: string
    update_time: string
    buttons: []
}

const dialogVisible = ref(false)

const modifyDialogVisible = ref(false)

const modifyMenuId = ref(0)


// 点击新增按钮
const dialogAdd = () => {
    dialogVisible.value = true
}


const switchHandle = async (id: number, ifHidden: number) => {
    const data = { if_hidden: ifHidden };
    const response = await putMenu(id, data)
    // console.log(id)
    if (response && response.data.code === 2000) {
        getParentMenuList()
        ElMessage.success('操作成功！')
    }
}

// 点击编辑按钮,接收id
const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyMenuId.value = id
}



// 获取子菜单，子菜单也可能有下级菜单所以hasChildren适配下
const load = async (
    row: Menus,
    treeNode: unknown,
    resolve: (data: Menus[]) => void
) => {
    const res = await getChildrenMenus(row.id, 'true')
    if (res) {
        let processedData = res.data.data.map((item: Menus) => {
            return {
                ...item,
                hasChildren: item.menu_type === 1
            }
        })
        // 排序
        processedData = processedData.sort((a:any, b:any) => a.menu_order - b.menu_order)
        resolve(processedData)
    } else {
        resolve([])
    }
}

const tableData: Ref<Menus[]> = ref([])
const menuTree = ref([])

const buttonName:any = ref([])

interface ButtonItem {
    id: number;
    button_name: string;
}

// 后端没有提供hasChildren，这里转下
const getParentMenuList = async () => {
    const res = await getParentMenus()
    if (res) {
        let processedData = res.data.results.data.map((item: Menus) => {
            return {
                ...item,
                hasChildren: item.menu_type === 1
            }
        })
        // 根据menu_order进行升序排序
        processedData = processedData.sort((a:any, b:any) => a.menu_order - b.menu_order)
        tableData.value = processedData
        loading.value = false
        const menuTreeRes = await getMenuTree()
        if (menuTreeRes) {
            // menuTree.value = menuTreeRes.data.data
            menuTree.value = transformData(menuTreeRes.data.data);
        }
        const buttonsList = await getButtons()
        if (buttonsList) {
            buttonName.value = buttonsList.data.data.map((item: ButtonItem) => ({ id: item.id, button_name: item.button_name }));
        }
    }
}

getParentMenuList()

interface Item {
    menu_name: string
    menu_code: string
    children?: Item[]
    disabled: boolean
    // 其他可能的属性...
}

// 遍历转换
function transformData(data: any) {
    return data.map((item: Item) => {
        let newItem: any = {
            label: item.menu_name,
            value: item.menu_code,
            disabled: item.disabled
        };
        if (item.children) {
            newItem.children = transformData(item.children);
        }
        return newItem;
    });
}

const updateMenuList = async (processedDataMenus: Menus[]) => {
    if (processedDataMenus) {
        tableData.value = processedDataMenus
    }
    const menuTreeRes: any = await getMenuTree()
    if (menuTreeRes) {
        // menuTree.value = menuTreeRes.data.data
        menuTree.value = transformData(menuTreeRes.data.data);
    }
    // 初始化表格，替换自动更新，让用户自己点击展开查看
    showTable.value = false
    await nextTick()
    showTable.value = true
}

const convertMenuType = (type: number) => {
    switch (type) {
        case 1:
            return '文件夹';
        case 2:
            return '子菜单';
        default:
            return '未知类型';
    }
}

const getTagType = (type: number) => {
    switch (type) {
        case 1:
            return 'success'; // 文件夹标签为绿色
        case 2:
            return 'info'; // 子菜单标签为蓝色
        default:
            return ''; // 其他情况使用默认颜色
    }
}

</script>
<style lang="less" scoped>

</style>
  