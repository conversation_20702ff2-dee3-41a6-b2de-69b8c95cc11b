<template>
  <el-dialog v-model="dialogVisible" title="编辑菜单" width="30%" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="80px" class="demo-ruleForm" :size="formSize"
      status-icon>
      <el-form-item label="菜单编码" prop="menu_code">
        <el-input v-model="ruleForm.menu_code" />
      </el-form-item>
      <el-form-item label="菜单名称" prop="menu_name">
        <el-input v-model="ruleForm.menu_name" />
      </el-form-item>
      <el-form-item label="菜单类型" prop="menu_type">
        <el-radio-group v-model="ruleForm.menu_type">
          <el-radio :label="1">文件夹</el-radio>
          <el-radio :label="2">子菜单</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="路由地址" prop="menu_router">
        <el-input v-model="ruleForm.menu_router" />
      </el-form-item>
      <el-form-item label="上级菜单" prop="p_menu_code">
        <el-tree-select v-model="ruleForm.p_menu_code" :data="menuTree" :render-after-expand="false" check-strictly />
      </el-form-item>
      <el-form-item label="组件路径" prop="component" v-if="ruleForm.menu_type === 2">
        <el-input v-model="ruleForm.component" />
      </el-form-item>
      <el-form-item label="图标" prop="menu_icon_path">
        <icon_picker v-model="ruleForm.menu_icon_path"></icon_picker>
      </el-form-item>
      <el-form-item label="排序" prop="menu_order">
        <el-input v-model="ruleForm.menu_order" />
      </el-form-item>
      <el-form-item label="按钮" prop="buttons" v-if="ruleForm.menu_type === 2">
        <el-checkbox-group v-model="ruleForm.buttons">
          <el-checkbox v-for="(btName, index) in buttonsList" :key="index" :label="btName.id">{{
            btName.button_name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, watchEffect, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { putMenu } from '@/apis/menu/put_menu'
import { getParentMenus } from '@/apis/menu/get_parent_menu'
import icon_picker from './icon_picker.vue'
import { getMenuInfo } from '@/apis/menu/get_menu'

// modelValue是v-modle的默认属性名
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  modifyMenuId: Number,
  tableData: Array,
  menuTree: Array,
  buttons: Array
})

const modifyMenuId = ref(0)
const dialogVisible = ref(false)
const parentMenus: any = ref(props.tableData)
const selectMenuTree: any = ref(props.menuTree)
const buttonsList: any = ref([])

watchEffect(() => {
  // props 是响应式的，props 的值在组件初始化时可能并未设置或者还未更新，可以使用 watch 或 watchEffect 
  // 来监听 props.tableData 的变化，这样当 props.tableData 更新时，你就可以获取到最新的值
  dialogVisible.value = props.modelValue || false
  modifyMenuId.value = props.modifyMenuId || 0
  parentMenus.value = props.tableData
  selectMenuTree.value = props.menuTree
  buttonsList.value = props.buttons
  if (props.modelValue) {
    // 每次打开dialog重置表单数据
    getMenu()
  }
})

const getMenu = async () => {
  const res = await getMenuInfo(modifyMenuId.value)
  if (res) {
    // 每次打开dialog显示历史数据
    ruleForm.menu_code = res.data.data.menu_code
    ruleForm.menu_name = res.data.data.menu_name
    ruleForm.component = res.data.data.component
    ruleForm.menu_type = res.data.data.menu_type
    ruleForm.menu_router = res.data.data.menu_router
    ruleForm.menu_icon_path = res.data.data.menu_icon_path
    ruleForm.p_menu_code = res.data.data.p_menu_code
    ruleForm.if_hidden = res.data.data.if_hidden
    ruleForm.menu_order = res.data.data.menu_order
    const buttonNames = res.data.data.buttons // 这里后端返回的是一个包含按钮名称的数组
    ruleForm.buttons = buttonNames.map((name:any) => {
      const button = buttonsList.value.find((bt:any) => bt.button_name === name)
      return button ? button.id : null
    }).filter((id:any) => id !== null) // 过滤掉找不到的按钮
  }
}

const emit = defineEmits(['update:modelValue', 'updateMenuList']) // 定义emit函数

const handleClose = () => {
  emit('update:modelValue', false) // 更新modelValue的值
}


interface RuleForm {
  menu_code: string
  menu_name: string
  component: string
  menu_type: number | null
  menu_router: string
  menu_icon_path: string
  p_menu_code: string | null
  if_hidden: number
  menu_order: number | null
  buttons: []
}

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  menu_code: '',
  menu_name: '',
  component: '#',
  menu_type: null,
  menu_router: '',
  menu_icon_path: '',
  p_menu_code: null,
  if_hidden: 1,
  menu_order: null,
  buttons: []
})

const rules = reactive<FormRules<RuleForm>>({
  menu_code: [
    { required: true, message: '请输入菜单编码', trigger: 'blur' },
    { min: 3, max: 50, message: '长度大于3小于50', trigger: 'blur' },
    {
      pattern: /^[A-Z][A-Z_]*[A-Z]$/,
      message: '仅支持大写字母和_，且字母开头结尾，如: USER_CENTER',
      trigger: 'blur'
    }
  ],
  menu_name: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fff]+$/,
      message: '请输入中文菜单名',
      trigger: 'blur'
    }
  ],
  menu_type: [
    { required: true, message: '请选择菜单类型', trigger: 'blur' },
  ],
  menu_router: [
    { required: true, message: '请输入路由地址', trigger: 'blur' },
  ],
  p_menu_code: [
    { required: true, message: '请选择上级菜单', trigger: 'blur' },
  ],
  menu_icon_path: [
    { required: true, message: '请选择一个图标', trigger: 'blur' },
  ],
})

interface Menus {
  id: number
  menu_name: string
  component: string
  menu_router: string
  menu_type: number
  menu_icon_path: string
  menu_order: number
  if_hidden: number
  hasChildren?: boolean
  children?: []
  menu_code: string
  p_menu_code?: any
  if_enable: number
  create_time: string
  creator_id: string
  update_time: string
  buttons: []
}


const submitForm = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      const response = await putMenu(modifyMenuId.value, ruleForm)
      if (response && response.data.code === 2000) {
        const parentMenuInfoResponse = await getParentMenus()
        if (parentMenuInfoResponse) {
          ElMessage.success('操作成功！')
          const processedData = parentMenuInfoResponse.data.results.data.map((item: Menus) => {
            return {
              ...item,
              hasChildren: item.menu_type === 1
            }
          })
          emit('updateMenuList', processedData)
        }
      }
      handleClose()
    } else {
      // ElMessage.error('表单验证失败！')
    }
  })
}



</script>
<style scoped></style>