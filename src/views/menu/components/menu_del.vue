<template>
  <el-popconfirm confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled" icon-color="#626AEF" title="确定删除吗？"
    @confirm="confirmEvent">
    <template #reference>
      <el-button type="danger" :icon="Delete" circle></el-button>
    </template>
  </el-popconfirm>
</template>
  
<script setup lang="ts">
import { InfoFilled, Delete, } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deleteMenuInfo } from '@/apis/menu/delete_menu'
import { getParentMenus } from '@/apis/menu/get_parent_menu'

interface Menus {
  id: number
  menu_name: string
  component: string
  menu_router: string
  menu_type: number
  menu_icon_path: string
  menu_order: number
  if_hidden: number
  hasChildren?: boolean
  children?: []
  menu_code: string
  p_menu_code?: any
  if_enable: number
  create_time: string
  creator_id: string
  update_time: string

}

// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['deleteConfirmed'])

// 定义一个 props，用于接收父组件传递的用户 id
const props = defineProps({
  menuId: {
    type: Number,
    required: true
  }
})

const confirmEvent = async () => {
  if (props.menuId) {
    const response = await deleteMenuInfo(props.menuId)
    if (response && response.data.code === 2000) {
      const parentMenuInfoResponse = await getParentMenus()
      if (parentMenuInfoResponse) {
        ElMessage.success('操作成功！')
        const processedData = parentMenuInfoResponse.data.results.data.map((item: Menus) => {
          return {
            ...item,
            hasChildren: item.menu_type === 1
          }
        })
        emit('deleteConfirmed', processedData)
      }
    }
  }
}
</script>