<template>
    <div class="app-container">
        <el-row :gutter="20" class="header">
            <el-col :span="7">
                <el-input placeholder="请输入字典名..." v-model="queryParams.dictionaryNameQuery" clearable
                    @clear="getDictionaryList"></el-input>
            </el-col>
            <el-button v-permission="'DICTIONARY:查询'" type="primary" :icon="Search" @click="getDictionaryList">搜索</el-button>
            <el-button v-permission="'DICTIONARY:新增'" type="primary" :icon="Plus" @click="dialogAdd">新增</el-button>
        </el-row>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="dic_name" label="字典名" />
            <el-table-column prop="dic_key" label="字典键" />
            <el-table-column show-overflow-tooltip prop="dic_value" label="字典值"></el-table-column>
            <el-table-column prop="create_time" label="创建时间">
                <template v-slot="scope">
                    {{ formatDate(scope.row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" align="center">
                <template v-slot="scope">
                    <el-button v-permission="'DICTIONARY:编辑'" type="primary" :icon="Edit" circle @click="dialogModify(scope.row.id)"></el-button>
                    <span style="margin-left: 10px;" v-permission="'DICTIONARY:删除'">
                        <dictionary_del :dictionaryId="scope.row.id" @deleteConfirmed="updateDictionaryList"></dictionary_del>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10, 20, 30, 50]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
    <dictionary_add_dialog v-model="dialogVisible" @updateDictionaryList="updateDictionaryList"></dictionary_add_dialog>
    <dictionary_modify_dialog v-model="modifyDialogVisible" :modifyDictionaryId='modifyDictionaryId' :dictionaryInfo="currentDictionaryInfo"
        @updateDictionaryList="updateDictionaryList"></dictionary_modify_dialog>
</template>
  
<script lang="ts" setup>
import { ref, reactive, watchEffect } from 'vue'
import { getAllDictionary } from '@/apis/dictionary/get_all_dictionary'
import { Search, Edit, Plus } from '@element-plus/icons-vue';
import dictionary_add_dialog from './dictionary_add_dialog.vue';
import dictionary_del from './dictionary_del.vue';
import dictionary_modify_dialog from './dictionary_modify_dialog.vue';
import { formatDate } from '@/util/format_date';
import { useStore } from '@/stores';

const loading = ref(true)

const queryParams = reactive({
    dictionaryNameQuery: '',
    page_size: 10,
    page: 1
})

const total = ref(0)

const tableData = ref([])

const dialogVisible = ref(false)
// console.log('dialogAddValue1',jobAddDialogVisible)
const modifyDialogVisible = ref(false)

// 控制新增对话框
const dialogAdd = () => {
    dialogVisible.value = true
    // console.log('dialogAddValue2',jobAddDialogVisible)
}

const modifyDictionaryId = ref(0) // 新增一个响应式变量用于存储要修改的用户id

const currentDictionaryInfo = ref({}) // 新增一个响应式变量用于存储当前用户的信息



const dialogModify = (id: number) => {
    modifyDialogVisible.value = true
    modifyDictionaryId.value = id
    const dictionary = tableData.value.find((dictionary: any) => dictionary.id === id) // 在 tableData 中查找字典详细信息
    if (dictionary) { currentDictionaryInfo.value = dictionary } // 将找到的字典信息存储在 currentDictionaryInfo 中
}


const getDictionaryList = async () => {
    const res = await getAllDictionary(queryParams.page_size, queryParams.page, queryParams.dictionaryNameQuery, '')
    if (res) {
        let dictionaryList = res.data.data.results
        tableData.value = dictionaryList
        total.value = res.data.data.count
        loading.value = false
    }
}

getDictionaryList()

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getDictionaryList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getDictionaryList()
}

const userStore = useStore()
// 定义一个事件函数，让子组件触发，需要传递的数据作为事件的参数，这里是子组件直接传整个响应，父组件监听，并处理
// 监听 update 事件只要在上方的子组件引入中加入 @updateJobList="updateJobList"
const updateDictionaryList = (newDictionaries: any) => {
    if (newDictionaries) {
        tableData.value = newDictionaries.data.data.results
        total.value = newDictionaries.data.data.count
        // 同步更新到store
        userStore.setDicList(tableData.value);
    }
}

</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>