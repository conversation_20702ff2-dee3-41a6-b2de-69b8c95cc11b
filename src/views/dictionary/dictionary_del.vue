<template>
    <el-popconfirm confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled" icon-color="#626AEF"
        title="确定删除吗？" @confirm="confirmEvent">
        <template #reference>
            <el-button type="danger" :icon="Delete" circle></el-button>
        </template>
    </el-popconfirm>
</template>

<script setup lang="ts">
import { InfoFilled, Delete, } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deleteDictionary } from '@/apis/dictionary/delete_dictionary'
import { getAllDictionary } from '@/apis/dictionary/get_all_dictionary'

// 定义一个 emit 函数，用于触发自定义事件
const emit = defineEmits(['deleteConfirmed'])

// 定义一个 props，用于接收父组件传递的用户 id
const props = defineProps({
    dictionaryId: {
        type: Number,
        required: true
    }
})

const confirmEvent = async () => {
    if (props.dictionaryId) {
        const response = await deleteDictionary(props.dictionaryId)
        if (response && response.data.code === 2000) {
            const dictionaryResponse = await getAllDictionary(10, 1, "", "")
            if (dictionaryResponse && dictionaryResponse.data.code === 2000) {
                ElMessage.success('操作成功！')
                // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
                emit('deleteConfirmed', dictionaryResponse)  // 触发 update 事件
            }
        }
    }
}
</script>