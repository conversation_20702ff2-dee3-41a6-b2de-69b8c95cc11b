<template>
    <el-dialog v-model="dialogVisible" title="新增字典" width="30%" :before-close="handleClose">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="65px" class="demo-ruleForm" :size="formSize"
        status-icon>
        <el-form-item label="字典名" prop="dic_name">
          <el-input v-model="ruleForm.dic_name" />
        </el-form-item>
        <el-form-item label="字典键" prop="dic_key">
          <el-input v-model="ruleForm.dic_key" />
        </el-form-item>
        <el-form-item label="字典值" prop="dic_value">
          <el-input type="textarea" v-model="ruleForm.dic_value" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitForm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang='ts'>
  import { ref, watchEffect, reactive } from 'vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import { postDictionary } from '@/apis/dictionary/post_dictionary'
  import { getAllDictionary } from '@/apis/dictionary/get_all_dictionary'
  
  
  // modelValue是v-modle的默认属性名
  const props = defineProps({
    modelValue: Boolean
  })
  
  const dialogVisible = ref(false)
  
  watchEffect(() => {
    dialogVisible.value = props.modelValue
    if (props.modelValue) {
      // 每次打开dialog重置表单数据
      ruleForm.dic_name = ''
      ruleForm.dic_key = ''
      ruleForm.dic_value = ''
    }
  })
  
  const emit = defineEmits(['update:modelValue', 'updateDictionaryList']) // 定义emit函数
  
  const handleClose = () => {
    emit('update:modelValue', false) // 更新modelValue的值
  }
  
  
  interface RuleForm {
    dic_name: string
    dic_key: string
    dic_value: string
  }
  
  
  const formSize = ref('default')
  const ruleFormRef = ref<FormInstance>()
  const ruleForm = reactive<RuleForm>({
    dic_name: '',
    dic_key: '',
    dic_value: ''
  })
  
  const rules = reactive<FormRules<RuleForm>>({
    dic_name: [
      { required: true, message: '请输入字典名', trigger: 'blur' },
    ],
    dic_key: [
      { required: true, message: '请输入字典键', trigger: 'blur' },
    ],
    dic_value: [
      { required: true, message: '请输入字典值', trigger: 'blur' },
    ]
  })
  
  
  const submitForm = async () => {
    ruleFormRef.value?.validate(async (valid) => {
      if (valid) {
        const response = await postDictionary(ruleForm)
        if (response && response.data.code === 2000) {
          const dictionaryResponse = await getAllDictionary(10, 1, "", "")
          if (dictionaryResponse && dictionaryResponse.data.code === 2000) {
            ElMessage.success('操作成功!')
            // emit事件的参数不能冲突可以在列表中定义多个对应上面的const emit
            emit('updateDictionaryList', dictionaryResponse)  // 触发 update 事件
          }
        }
      handleClose()
      } else {
        // ElMessage.error('表单验证失败！')
      }
    })
  }
  
  </script>
  <style scoped></style>