<template>
    <div class="app-container">
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="user_name" label="用户名" />
            <el-table-column prop="login_ip" label="登录IP" />
            <el-table-column prop="login_status" label="登录状态">
                <template v-slot="{ row }">
                    <el-tag :type="row.login_status === 1 ? 'success' : 'danger'">{{ row.login_status === 1 ? '登录成功' : '登录失败' }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="login_time" label="登录时间">
                <template v-slot="scope">
                    {{  formatDate(scope.row.login_time) }}
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
                :page-sizes="[10]" layout="total, sizes, ->, prev, pager, next, jumper" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getAllLoginLogs } from '@/apis/ats_logs/get_all_login_logs'
import { formatDate } from '@/util/format_date';


const loading = ref(true)

const queryParams = reactive({
    page_size: 10,
    page: 1
})

const total = ref(0)
const tableData = ref([])
const getLoginLogList = async () => {
    const res = await getAllLoginLogs(queryParams.page_size, queryParams.page)
    if (res) {
        let loginLogList = res.data.data.results
        tableData.value = loginLogList
        total.value = res.data.data.count
        loading.value = false
    }
}

getLoginLogList()

const handleSizeChange = (pageSize: number) => {
    queryParams.page = 1
    queryParams.page_size = pageSize
    getLoginLogList()
}

const handleCurrentChange = (pageNum: number) => {
    queryParams.page = pageNum
    getLoginLogList()
}


</script>
<style lang="less" scoped>
.demo-pagination-block {
    margin-top: 5px;
    margin-bottom: 11px;
}
</style>