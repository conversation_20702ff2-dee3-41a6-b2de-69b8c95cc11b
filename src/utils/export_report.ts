import { formatDate } from '@/util/format_date'

interface ApiInfo {
  api_id: number
  api_name: string
  request_method: string
  request_url: string
  folder_id?: number
}

interface ExecutionResult {
  res_url: string
  folder_id: number
  iteration: number
  error_info: string | null
  res_content: string
  res_headers: Record<string, string>
  res_status_code: number
  res_elapsed: number
  response_size: {
    total: number
    body_size: number
    headers_size: number
  }
  assertion_result: Array<{
    title: string
    status: string
    error_log: string
  }>
  execution_status: string
  res_json_data?: any
}

interface ReportData {
  scene_api_info: ApiInfo[]
  scene_api_result: {
    execution_time: string
    assertion_count: number
    total_elapsed_time: number
    api_elapsed_time: number
    avg_api_elapsed_time: number
    execution_result: ExecutionResult[]
    echart_data: {
      fail_count: number
      success_count: number
    }
    iterations: number
  }
}

export const generateHtmlReport = (data: ReportData, sceneName?: string) => {
  const { scene_api_info, scene_api_result } = data
  const { execution_result, echart_data } = scene_api_result
  
  // 匹配API信息和执行结果（result.folder_id 匹配 api.folder_id）
  const apiResults = scene_api_info.map(api => {
    const result = execution_result.find(r => r.folder_id === api.folder_id)
    return {
      ...api,
      execution_result: result || null
    }
  })

  const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 15px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .title { font-size: 24px; font-weight: bold; color: #409EFF; margin-bottom: 15px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); gap: 12px; margin-top: 15px; }
        .summary-item { background: #f8f9fa; padding: 10px 12px; border-radius: 6px; text-align: center; }
        .summary-item .label { font-size: 11px; color: #666; margin-bottom: 3px; }
        .summary-item .value { font-size: 16px; font-weight: bold; color: #409EFF; }
        .stats { display: flex; gap: 15px; margin-top: 15px; }
        .stat-success { background: #f0f9ff; border: 1px solid #46D6A0; color: #46D6A0; padding: 8px 16px; border-radius: 6px; font-size: 14px; }
        .stat-fail { background: #fff5f5; border: 1px solid #FC97AF; color: #FC97AF; padding: 8px 16px; border-radius: 6px; font-size: 14px; }
        .api-item { background: white; margin-bottom: 15px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .api-header { padding: 15px 20px; background: #fafafa; border-bottom: 1px solid #eee; cursor: pointer; user-select: none; display: flex; align-items: center; justify-content: space-between; }
        .api-header:hover { background: #f0f0f0; }
        .api-header .left { display: flex; align-items: center; gap: 10px; flex: 1; min-width: 0; }
        .method-tag { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; color: white; }
        .GET { background: #67C23A; }
        .POST { background: #E6A23C; }
        .PUT { background: #409EFF; }
        .DELETE { background: #F56C6C; }
        .PATCH { background: #909399; }
        .api-name { font-weight: 500; }
        .api-url { 
            color: #666; 
            font-size: 14px; 
            margin-left: 10px; 
            max-width: 300px; 
            overflow: hidden; 
            text-overflow: ellipsis; 
            white-space: nowrap; 
            cursor: help;
        }
        .status-info { display: flex; align-items: center; gap: 15px; font-size: 12px; }
        .status-success { color: #46D6A0; }
        .status-fail { color: #FC97AF; }
        .toggle-icon { transition: transform 0.3s; }
        .toggle-icon.expanded { transform: rotate(90deg); }
        .api-details { display: none; padding: 20px; }
        .api-details.show { display: block; }
        .detail-section { margin-bottom: 20px; }
        .detail-title { font-weight: bold; margin-bottom: 10px; color: #409EFF; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        .json-container { background: #f8f9fa; padding: 15px; border-radius: 6px; border: 1px solid #eee; max-height: 300px; overflow-y: auto; }
        .json-content { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 12px; white-space: pre-wrap; }
        .assertion-item { background: #f8f9fa; margin: 5px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #ddd; }
        .assertion-pass { border-left-color: #46D6A0; }
        .assertion-fail { border-left-color: #FC97AF; }
        .assertion-title { font-weight: bold; margin-bottom: 5px; }
        .assertion-status { font-size: 12px; }
        .assertion-error { color: #F56C6C; font-size: 12px; margin-top: 5px; }
        .no-data { text-align: center; color: #999; padding: 20px; }
        .error-info { background: #fff5f5; color: #F56C6C; padding: 15px; border-radius: 6px; border: 1px solid #fecaca; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">API测试报告 ${sceneName ? `- ${sceneName}` : ''}</div>
            <div class="summary">
                <div class="summary-item">
                    <div class="label">总耗时</div>
                    <div class="value">${scene_api_result.total_elapsed_time}s</div>
                </div>
                <div class="summary-item">
                    <div class="label">接口请求耗时</div>
                    <div class="value">${scene_api_result.api_elapsed_time}s</div>
                </div>
                <div class="summary-item">
                    <div class="label">平均接口请求耗时</div>
                    <div class="value">${scene_api_result.avg_api_elapsed_time}s</div>
                </div>
                <div class="summary-item">
                    <div class="label">循环数</div>
                    <div class="value">${scene_api_result.iterations}</div>
                </div>
                <div class="summary-item">
                    <div class="label">断言数</div>
                    <div class="value">${scene_api_result.assertion_count}</div>
                </div>
                <div class="summary-item">
                    <div class="label">执行时间</div>
                    <div class="value" style="font-size: 12px; line-height: 1.2;">${formatDate(scene_api_result.execution_time)}</div>
                </div>
            </div>
            <div class="stats">
                <div class="stat-success">成功: ${echart_data.success_count}</div>
                <div class="stat-fail">失败: ${echart_data.fail_count}</div>
            </div>
        </div>

        <div class="api-list">
            ${apiResults.map((api, index) => {
              const result = api.execution_result
              if (!result) {
                return `
                <div class="api-item">
                    <div class="api-header" onclick="toggleDetails(${index})">
                        <div class="left">
                            <span class="toggle-icon" id="icon-${index}">▶</span>
                            <span class="method-tag ${api.request_method}">${api.request_method}</span>
                            <span class="api-name">${api.api_name}</span>
                            <span class="api-url" title="${api.request_url}">${api.request_url}</span>
                        </div>
                        <div class="status-info">
                            <span>未执行</span>
                        </div>
                    </div>
                    <div class="api-details" id="details-${index}">
                        <div class="no-data">该接口未执行</div>
                    </div>
                </div>
                `
              }

              return `
                <div class="api-item">
                    <div class="api-header" onclick="toggleDetails(${index})">
                        <div class="left">
                            <span class="toggle-icon" id="icon-${index}">▶</span>
                            <span class="method-tag ${api.request_method}">${api.request_method}</span>
                            <span class="api-name">${api.api_name}</span>
                            <span class="api-url" title="${result.res_url}">${result.res_url}</span>
                        </div>
                        <div class="status-info">
                            <span class="${result.execution_status.includes('success') ? 'status-success' : 'status-fail'}">
                                ${result.execution_status}
                            </span>
                            <span>状态: ${result.res_status_code}</span>
                            <span>耗时: ${result.res_elapsed}ms</span>
                            <span>大小: ${result.response_size.total}B</span>
                        </div>
                    </div>
                    <div class="api-details" id="details-${index}">
                        ${result.error_info ? `<div class="error-info">${result.error_info}</div>` : ''}
                        
                        <div class="detail-section">
                            <div class="detail-title">响应头</div>
                            <div class="json-container">
                                <div class="json-content">${JSON.stringify(result.res_headers, null, 2)}</div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <div class="detail-title">响应体</div>
                            <div class="json-container">
                                <div class="json-content">${result.res_json_data ? JSON.stringify(result.res_json_data, null, 2) : result.res_content}</div>
                            </div>
                        </div>

                        ${result.assertion_result && result.assertion_result.length > 0 ? `
                        <div class="detail-section">
                            <div class="detail-title">断言结果</div>
                            ${result.assertion_result.map(assertion => `
                                <div class="assertion-item ${assertion.status === '通过' ? 'assertion-pass' : 'assertion-fail'}">
                                    <div class="assertion-title">${assertion.title}</div>
                                    <div class="assertion-status">状态: ${assertion.status}</div>
                                    ${assertion.error_log ? `<div class="assertion-error">错误信息: ${assertion.error_log}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                        ` : ''}
                    </div>
                </div>
              `
            }).join('')}
        </div>
    </div>

    <script>
        function toggleDetails(index) {
            const details = document.getElementById('details-' + index);
            const icon = document.getElementById('icon-' + index);
            
            if (details.classList.contains('show')) {
                details.classList.remove('show');
                icon.classList.remove('expanded');
            } else {
                details.classList.add('show');
                icon.classList.add('expanded');
            }
        }
    </script>
</body>
</html>
  `

  return htmlContent
}

export const downloadHtmlReport = (htmlContent: string, filename?: string) => {
  const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename || `API测试报告_${new Date().getTime()}.html`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
} 