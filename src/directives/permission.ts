import type { Directive } from 'vue';
import { usePermissionStore } from '../stores/permission';

const permissionDirective: Directive = {
    mounted(el, binding) {
        const store = usePermissionStore();
        const [menuCode, buttonName] = binding.value.split(':');
        const buttons = store.getPermissionButtons(menuCode);
        // console.log('Menu Code:', menuCode); // 打印菜单代码
        // console.log('Button Name:', buttonName); // 打印按钮名称
        // console.log('Available Buttons:', buttons); // 打印可用按钮列表
        if (!buttons.includes(buttonName)) {
            el.parentNode?.removeChild(el);
        }
    }
}

export default permissionDirective;