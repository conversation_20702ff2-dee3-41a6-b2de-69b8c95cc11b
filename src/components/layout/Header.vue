<template>
  <div class="navbar">
    <div class="hamburger-container" @click="toggleSideBar">
      <el-icon>
        <Fold v-if="isCollapse" />
        <Expand v-else />
      </el-icon>
    </div>
    <BreadCrumb />
    <div class="switch_project">
      <switch_project />
    </div>
    <div class="navbar-right">
      <Avatar />
    </div>
  </div>
</template>
<script setup lang='ts'>
import switch_project from './Header/switch_project.vue'
import BreadCrumb from '../BreadCrumb.vue'
import Avatar from './Header/avatar.vue'
import { ref } from 'vue'
import { Fold, Expand } from '@element-plus/icons-vue'

// 折叠状态（默认从父组件props获取）
const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false
  }
})

// 定义emit
const emit = defineEmits(['toggle'])

// 切换侧边栏
const toggleSideBar = () => {
  emit('toggle')
}

</script>

<style lang="less" scoped>
.navbar {
  width: 100%;
  height: 60px;
  overflow: hidden;
  background-color: #F5F5F5;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 15px;
    padding: 0 15px;
    transition: background 0.3s;
    
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }

    .el-icon {
      font-size: 20px;
      
      &.is-active {
        transform: rotate(180deg);
      }
    }
  }

  .switch_project {
    position: absolute;
    right: 0;
    padding-right: 150px;
  }

  .navbar-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    :deep(.navbar-item) {
      display: inline-block;
      margin-left: 18px;
      font-size: 22px;
      color: #5a5e66;
      box-sizing: border-box;
      cursor: pointer;
    }
  }
}
</style>