<template>
  <div class="tabs-container">
    <el-tabs v-model="editableTabsValue" type="card" class="modern-tabs" closable @tab-remove="removeTab" @tab-click="clickTab">
      <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name">
        <!-- element自带的目前不用 -->
        <!-- {{ item.content }} -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useTabsStore } from '@/stores/tabs'
import { useRouter } from 'vue-router'
const router = useRouter()
const tabStore = useTabsStore()
const editableTabsValue = ref(tabStore.editableTabsValue)
const editableTabs = ref(tabStore.editableTabs)

const removeTab = (targetName: string) => {
  if (targetName==='/ats/index'){return}
  const tabs = editableTabs.value
  let activeName = editableTabsValue.value

  if (activeName === targetName) {
    tabs.forEach((tab, index) => {
      if (tab.name === targetName) {
        const nextTab = tabs[index + 1] || tabs[index - 1]
        if (nextTab) {
          activeName = nextTab.name
        }
      }
    })
  }

  editableTabsValue.value = activeName
  editableTabs.value = tabs.filter((tab) => tab.name !== targetName)

  tabStore.editableTabsValue = editableTabsValue.value
  tabStore.editableTabs = editableTabs.value

  router.push(tabStore.editableTabsValue)
}

const refreshTabs = () => {
  editableTabsValue.value = tabStore.editableTabsValue
  editableTabs.value = tabStore.editableTabs
}

const clickTab = (targetName: any) => {
  // Element封装的对象
  // 点击tab页后页更新一下tab状态
  tabStore.editableTabsValue = targetName.props.name
  router.push(targetName.props.name)
  
}

//监听refreshTabs关联的tabStore响应式的数据发生变化
watch(tabStore, () => {
  refreshTabs()
}, { deep: true, immediate: true })

</script>
<style lang='less' scoped>
.tabs-container {
  padding: 4px 0 0px 0;
  margin: 0 0 4px 0;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 1px 1px rgba(255, 255, 255, 0.03);
}

:deep(.modern-tabs) {
  .el-tabs__header {
    margin-bottom: 0;
    padding-left: 12px;
  }

  .el-tabs__nav {
    border: none;
    border-radius: 3px;
  }

  .el-tabs__item {
    height: 36px;
    line-height: 36px;
    font-size: 13px;
    border: none;
    margin: 2px 4px 0 0;
    padding: 0 12px;
    border-radius: 4px 4px 0 0;
    background-color: #f9fafc;
    transition: all 0.2s;
    
    &:hover {
      color: #409EFF;
    }
    
    .el-icon-close {
      font-size: 12px;
      height: 12px;
      width: 12px;
      line-height: 12px;
      margin-left: 6px;
      border-radius: 50%;
      
      &:hover {
        background-color: #c0c4cc;
        color: #fff;
      }
    }
  }

  .el-tabs__item.is-active {
    color: #409EFF;
    background-color: #ecf5ff;
    border-bottom: 2px solid #409EFF;
  }
}
</style>
