<template>
    <el-dropdown trigger="hover">
      <div class="project-selector">
        <el-icon><Folder /></el-icon>
        <span class="project-name">{{ currentProject }}</span>
        <el-icon class="el-icon--right">
          <arrow-down />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu class="custom-dropdown">
          <div class="dropdown-search">
            <el-input 
              v-model="searchQuery" 
              placeholder="搜索项目" 
              prefix-icon="Search"
              clearable
            />
          </div>
          <div class="dropdown-content">
            <el-dropdown-item 
              v-for="item in filteredProjects" 
              :key="item.id" 
              @click="updateProject(item.name, item)"
            >
              {{ item.name }}
            </el-dropdown-item>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
</template>
  
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ArrowDown, Folder, Search } from '@element-plus/icons-vue'
import { getAllProjectInfo } from '@/apis/project/get_all_projectinfo'
import { useStore } from '@/stores';
import { getDefaultProject } from '@/apis/project/default_project/get_default_project'
import { postDefaultProject } from '@/apis/project/default_project/post_default_project'

const currentProject = ref('请选择项目')
const projectList = ref<Array<{ name: string, id: number }>>([])
const searchQuery = ref('')

// 前端搜索过滤
const filteredProjects = computed(() => {
  if (!searchQuery.value) {
    return projectList.value
  }
  return projectList.value.filter(project => 
    project.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

type mainStore = {
    user_info: {
        id: number
        username: string
        group: []
    },
    setProjectInfo: (project: any) => void
}

const mainStore = useStore() as mainStore

const groupId = mainStore.user_info.group
const userId = mainStore.user_info.id
const userName = mainStore.user_info.username

// 名字给页面显示，整体对象给pinia做关联
async function updateProject(projectName:any, project:any) {
  const data = {
    "username": userName,
    "project": project.id
  }
  currentProject.value = projectName
  mainStore.setProjectInfo(project)
  const switch_project_res = await postDefaultProject(data)
  if ( switch_project_res && switch_project_res.data.code === 2000 ) {
    window.location.reload()
  }
}


const queryParams = reactive({
    projectNameQuery: '',
    page_size: 100, // 增加获取数量
    page: 1,
    groupId: groupId,
    userId: userId
})

const getProjectList = async () => {
    const res = await getAllProjectInfo(queryParams.page_size, queryParams.page, queryParams.groupId, queryParams.userId, queryParams.projectNameQuery)
    if (res) {
        projectList.value = res.data.results.data
    }

    const default_project_res = await getDefaultProject(userName)
    if (default_project_res && default_project_res.data.code === 2000) {
      const projectId = default_project_res.data.data.project
      const project = projectList.value.find(item => item.id  === projectId)
      if (project) {
        currentProject.value = project.name
        mainStore.setProjectInfo(project)
      }
    }
}

getProjectList()


</script>
<style scoped>
.project-selector {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  height: 32px;
  border-radius: 4px;
  background-color: #F5F5F5;
  transition: background 0.3s;
}

.project-selector:hover {
  background-color: #ebeef5;
}

.project-name {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.custom-dropdown) {
  width: 240px;
  padding: 0;
}

.dropdown-search {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.dropdown-content {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 14px;
}
</style>