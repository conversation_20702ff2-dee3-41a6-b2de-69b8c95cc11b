<template>
    <el-dropdown trigger="hover">
        <div class="avatar-container">
            <el-avatar shape="circle" :size="32" :src="squareUrl" />
            <div class="user-info">
                <span class="username">{{ currentUserName }}</span>
                <el-icon class="el-icon--right">
                    <arrow-down />
                </el-icon>
            </div>
        </div>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item @click="openTab(menuItem)">
                    <el-icon><User /></el-icon>
                    <span>个人中心</span>
                </el-dropdown-item>
                <el-dropdown-item @click="logout">
                    <el-icon><SwitchButton /></el-icon>
                    <span>安全退出</span>
                </el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script lang="ts" setup>
import { ArrowDown, User, SwitchButton } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { Logout } from '@/apis/logout'
import { useRouter } from 'vue-router'
import { useStore } from '@/stores'
import Cookies from 'js-cookie'
import { useTabsStore } from '@/stores/tabs'

const mainStore = useStore()
const router = useRouter()
const tabStore = useTabsStore()

interface UserInfo {
  avatar: string,
  username: string
}
// 头像
const squareUrl = (mainStore.user_info as UserInfo).avatar
const currentUserName = (mainStore.user_info as UserInfo).username

const refresh_token = mainStore.refresh_token

const logout = async () => {
  try {
    const response = await Logout({ refresh_token })
    ElMessage.success('退出成功')

    // 清除缓存
    mainStore.setToken('')
    mainStore.setRefreshToken('')
    mainStore.setMenus([])
    mainStore.setRoutes([])
    mainStore.setUserInfo({})
    mainStore.setProjectInfo({})

    localStorage.clear()
    Cookies.remove('token')

    router.push('/login')

  } catch (error) {
    ElMessage.error('退出失败')
  }
}

// 定义个人中心菜单项
const menuItem = {
  path: '/system/user/user_center',
  meta: {
    title: '个人中心',
    icon: 'User'
  }
}

const openTab = (item: any) => {
  // 添加 Tab
  tabStore.addTab(item)
  // 确保点击历史的也会切换
  tabStore.editableTabsValue = item.path
  // 立即导航到对应路由，确保页面显示
  router.push(item.path)
}
</script>
<style scoped>
.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  height: 48px;
  border-radius: 4px;
  transition: background 0.3s;
}

.avatar-container:hover {
  background: rgba(0, 0, 0, 0.025);
}

.user-info {
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.username {
  font-size: 14px;
  color: #303133;
  margin-right: 4px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}
</style>