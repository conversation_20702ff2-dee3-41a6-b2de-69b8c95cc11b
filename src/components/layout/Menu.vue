<template>
    <div class="menu-container">
        <el-menu active-text-color="#ffd04b" background-color="#2d3a4b" class="el-menu-vertical-demo" text-color="#fff" router
            :default-active="$route.path" :collapse="isCollapse">
            <!-- 路由标签都跳 -->
            <el-menu-item index="/ats/index" @click="$router.push('/ats/index'); openTab(homeItem)">
                <el-icon>
                    <HomeFilled />
                </el-icon>
                <template #title>首页</template>
            </el-menu-item>
            <template v-for="menu in sortedMenuList">
                <el-sub-menu :index="menu.path" v-if="menu.meta.if_hidden !== 0">
                    <template #title>
                        <el-icon>
                            <component :is="menu.meta.icon" />
                        </el-icon>
                        <span>{{ menu.meta.title }}</span>
                    </template>
                    <template v-for="item in menu.children">
                        <el-menu-item v-if="item.meta.if_hidden !== 0 && !item.children" :index="item.path" @click="openTab(item)">
                            <el-icon>
                                <component :is="item.meta.icon" />
                            </el-icon>
                            <template #title>{{ item.meta.title }}</template>
                        </el-menu-item>
                        <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
                            <template #title>
                                <el-icon>
                                    <component :is="item.meta.icon" />
                                </el-icon>
                                <span>{{ item.meta.title }}</span>
                            </template>
                            <el-menu-item v-for="subItem in item.children" :index="subItem.path" @click="openTab(subItem)">
                                <el-icon>
                                    <component :is="subItem.meta.icon" />
                                </el-icon>
                                <template #title>{{ subItem.meta.title }}</template>
                            </el-menu-item>
                        </el-sub-menu>
                    </template>
                </el-sub-menu>
            </template>
        </el-menu>
    </div>
</template>
<script setup lang='ts'>
import { useStore } from '@/stores'
import { useTabsStore } from '@/stores/tabs'
import { ref } from 'vue'
import { Fold, Expand } from '@element-plus/icons-vue'

// 添加折叠控制变量（从父组件获取）
const props = defineProps({
    isCollapse: {
        type: Boolean,
        default: false
    }
})

// 定义emit
const emit = defineEmits(['collapse-change'])

const homeItem = {
    path: '/ats/index',
    meta: {
        title: '首页'
    }
}

const menuStore = useStore()
const tabStore = useTabsStore()

// Define the type of menu
interface Menu {
    path: String
    children: any[];
    meta: {
        icon: string;
        title: string;
        if_hidden: number;
    };
}

// 后端菜单数据
let menuList: Menu[] = menuStore.menus

function sortMenus(menus: any[]): any[] {
    // 首先，对当前级别的菜单进行排序
    menus.sort((a, b) => a.menuOrder - b.menuOrder);

    // 然后，对每个菜单的子菜单进行排序
    menus.forEach(menu => {
        if (menu.children) {
            menu.children = sortMenus(menu.children);
        }
    });

    return menus;
}

// 函数排序菜单数据
const sortedMenuList: Menu[] = sortMenus(menuList);

const openTab = (item: any) => {
    tabStore.addTab(item)
    // 确保点击历史的也会切换
    tabStore.editableTabsValue = item.path
}

</script>

<style scoped>
.menu-container {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 200px;
    flex: 1;
}

.el-menu-vertical-demo.el-menu--collapse {
    flex: 1;
}

.menu-footer {
    background-color: #263445;
    color: #bfcbd9;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-top: 1px solid #1f2d3d;
    transition: all 0.3s;
}

.toggle-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    padding: 0 15px;
    height: 100%;
    transition: all 0.3s;
}

.toggle-button:hover {
    background-color: #1f2d3d;
}

.toggle-button .el-icon {
    margin-right: 8px;
    font-size: 18px;
}

.expand-icon, .collapse-icon {
    transition: transform 0.3s;
}
</style>