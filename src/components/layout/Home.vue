<template>
    <div class="app-wrapper">
      <el-container>
        <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar-container"><Menu :is-collapse="isCollapse"/></el-aside>
        <el-container>
          <el-header><Header :is-collapse="isCollapse" @toggle="toggleSideBar"/></el-header>
          <el-main>
            <Tabs/>
            <router-view />
          </el-main>
          <el-footer><Footer/></el-footer>
        </el-container>
      </el-container>
    </div>
  </template>
  

<script setup lang='ts'>
import { ref, reactive } from 'vue'
import Menu from '@/components/layout/Menu.vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import Tabs from '@/components/layout/Tabs.vue'

// 添加折叠状态
const isCollapse = ref(false)

// 处理Header组件的折叠按钮点击
const toggleSideBar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style lang='less' scoped>
.app-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
}

.sidebar-container {
  background-color: #2d3a4b;
  height: 100%;
}

.el-container{
  height:100%
}

.el-header{
  padding-left: 0px;
  padding-right: 0px;
}

:deep(ul.el-menu){
  border-right-width: 0px
}

</style>