<template>
  <div class="playwright-script-editor">
    <div class="editor-header">
      <span class="editor-title">Playwright 脚本编辑器</span>
      <el-button size="small" type="info" @click="insertTemplate">插入模板</el-button>
    </div>
    <code-mirror
      v-model="code_val"
      wrap
      basic
      :lang="lang"
      style="height: 400px;"
      :extensions="extensions"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRaw } from 'vue';
import CodeMirror from 'vue-codemirror6'
import { oneDark } from '@codemirror/theme-one-dark'
import { python } from '@codemirror/lang-python';
import { indentUnit } from '@codemirror/language'

const emit = defineEmits(['updateScriptData'])

const props = defineProps({
  scriptData: String
})

// Playwright + pytest 模板
const playwright_template = `import pytest
from playwright.sync_api import Page, expect

def test_login_functionality(page: Page):
    """
    登录功能自动化测试
    使用 Playwright 编写的登录功能测试脚本
    """
    # 访问登录页面
    page.goto("https://example.com/login")

    # 等待页面加载完成
    page.wait_for_load_state("networkidle")

    # 填写用户名
    page.fill('input[name="username"]', "testuser")

    # 填写密码
    page.fill('input[name="password"]', "testpass")

    # 点击登录按钮
    page.click('button[type="submit"]')

    # 等待页面跳转
    page.wait_for_url("**/dashboard")

    # 验证登录成功
    expect(page.locator('.welcome-message')).to_be_visible()
    expect(page.locator('.user-info')).to_contain_text("testuser")

def test_form_validation(page: Page):
    """
    表单验证测试
    """
    page.goto("https://example.com/login")

    # 不填写任何信息直接提交
    page.click('button[type="submit"]')

    # 验证错误提示
    expect(page.locator('.error-message')).to_be_visible()
    expect(page.locator('.error-message')).to_contain_text("用户名不能为空")

def test_navigation(page: Page):
    """
    页面导航测试
    """
    page.goto("https://example.com")

    # 点击导航链接
    page.click('a[href="/about"]')

    # 验证页面跳转
    expect(page).to_have_url("**/about")
    expect(page.locator('h1')).to_contain_text("关于我们")
`

// 初始化代码值
const code_val = ref(playwright_template);

const lang = python();
const extensions = [oneDark, indentUnit.of("    ")];

// 插入模板
const insertTemplate = () => {
  code_val.value = playwright_template
}

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
  emit('updateScriptData', toRaw(code_val.value))
}

// 监听代码变化，发送给父组件
watch(code_val, () => {
  updateData()
}, { deep: true })

// 监听从父组件传入的数据
watch(() => props.scriptData, (newValue) => {
  if (newValue && newValue.trim() !== '') {
    code_val.value = newValue
  } else {
    code_val.value = playwright_template
  }
}, { immediate: true })
</script>

<style scoped>
.playwright-script-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.editor-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* required! */
:deep(.cm-editor) {
  height: 100%;
}
</style>