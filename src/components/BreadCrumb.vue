<template>
    <el-icon size="15px" style="margin-right: 5px; margin-bottom: 2px;"><HomeFilled /></el-icon>
    <el-breadcrumb separator="/">
        <el-breadcrumb-item 
            v-for="(item, index) in breadCrumbList" 
            :key="index" 
            :class="{ 'bold-text': index !== breadCrumbList.length - 1 }"
        >
            {{item.meta.title}}
        </el-breadcrumb-item>
    </el-breadcrumb>
</template>
  

<script setup lang='ts'>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteLocationMatched } from 'vue-router'


const route = useRoute()

const breadCrumbList = ref<RouteLocationMatched[]>([])

const initbreadCrumbList=()=>{
    breadCrumbList.value = route.matched
}

watch(route,()=>{
    initbreadCrumbList()
},{deep:true, immediate:true})

</script>

<style scoped>
.bold-text {
    font-weight: bold;

}
</style>