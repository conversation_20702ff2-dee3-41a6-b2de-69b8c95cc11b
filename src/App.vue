<template>
    <router-view></router-view>
</template>

<script setup lang='ts'>
import { watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTabsStore } from '@/stores/tabs'



const route = useRoute()
const router = useRouter()
const tabStore = useTabsStore()

// console.log('进入APP.VUE')
const whitePath = ['/login', '/ats/index', '/']

// 新的非白名单页面时，会在标签管理器中添加一个新的标签
watch(route, (to, from) => {
        // console.log('to', to.name)
        // console.log('to.path', to.path)
    if (whitePath.indexOf(to.path) === -1) {
        // console.log("to.path=", to.path)
        let obj = {
            path: to.path,
            meta: {
                title: to.meta.title as any
            }
        }
        // console.log('objobj',  obj)
        tabStore.addTab(obj)
    }
})


</script>

<style scoped>
</style>
