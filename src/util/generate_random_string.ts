export function generateRandomString(minLength: number = 8): string {
    // 确保生成的字符串至少有8个字符
    const length = Math.max(minLength, 8);
    // 定义字符集
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let result = letters.charAt(Math.floor(Math.random() * letters.length)); // 确保第一个字符是字母
  
    // 生成剩余的字符
    for (let i = 1; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
  
    return result;
  }
  
  // 使用示例
//   console.log(generateRandomString()); // 输出示例：G1j2K3l4
//   console.log(generateRandomString(10)); // 输出示例：H5m6N7o8P9