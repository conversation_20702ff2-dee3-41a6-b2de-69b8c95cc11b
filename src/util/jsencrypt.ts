import JSEncrypt from "jsencrypt"

// 密钥对生成 http://web.chacuo.net/netrsakeypair
// 如果密码未保存的可能密钥对有问题

const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIM4UfP98WeUEpY9EmrN8RK5gsNRTeUq\n' + 
'YlnVJDcduI+pzccPkeZ/uokaqj7B28YHhU+1zxyzY9rsffy2TwiMVosCAwEAAQ=='

const privateKey = 'MIIBOgIBAAJBAIM4UfP98WeUEpY9EmrN8RK5gsNRTeUqYlnVJDcduI+pzccPkeZ/\n' +
'uokaqj7B28YHhU+**************************************+WZMr1Db6RB\n' +
'HPcKBZpKiOTYKKXQ2Kc9gUOEeMLQ7x2b5jdmSbvWpxRTBxz810fnnNlPAvnw2kmR\n' +
'AQIhAIQ7BBfZUEZb9AgVzBSzIoZzpRn/tC+vmQJ2aWsfueuLAiEA/gspWHqqgB5T\n' +
'zKnnw0aD0X/dGZ9b/0FWd7pVyCkQoQECIEdGgHhSfUDPxASvqyflP1D1+SLAgGHL\n' +
'51A6f6xsl4ztAiEAnSmKipJYqKoabaMvHj+hjRntTsynvWJOfANqzkmZYAECIAcv\n' +
'6KscWhHZsotZTRk5vqnJXfgR0lWbtpAqT/ORwiFO'


// 加密
export function encrypt(txt:string) {
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(publicKey) // 设置公钥
    return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt:string) {
    const encryptor = new JSEncrypt()
    encryptor.setPrivateKey(privateKey) // 设置私钥
    return encryptor.decrypt(txt) // 对数据进行解密
}
