import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import permissionDirective from './directives/permission'
import '@/assets/style/border.css'
import '@/assets/style/reset.css'


//引入汉化语言包
import zhCN from "element-plus/dist/locale/zh-cn.mjs";

import App from './App.vue'
import router from './router'
import { useStore } from './stores' 
import Cookies from 'js-cookie'
import ECharts from 'vue-echarts' 
import "echarts";

const app = createApp(App)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 全局挂载和注册 element-plus 的所有 icon
app.config.globalProperties.$icons = []
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.config.globalProperties.$icons.push(key)
    app.component(key, component)
}


// 挂载ECharts   
app.component('ECharts',ECharts) 
app.use(pinia)

//下面逻辑与路由守卫中的一致，防止刷新白屏的

interface Menu {
  path: string;
  name: string;
  component: string;
  meta: {
    title: string;
    if_hidden: number
  };
  children?: Menu[];
}

interface Route {
  path: string;
  name: string;
  meta: {
    title: string;
  };
  component?: () => Promise<typeof import("*.vue")>;
  props?: boolean;
  children?: Route[];
}

function createRoute(menu: Menu): Route {
  let route: Route = {
    path: menu.path,
    name: menu.name,
    meta: {
      title: menu.meta.title,
    }
  }
  // console.log("menu.component",menu.component)
  const modules = import.meta.glob('@/views/**/*.vue');
  if (menu.component !== '#') {
    route.component = modules[`/src/${menu.component}.vue`] as () => Promise<typeof import("*.vue")>;
  }

  // 适配路由参数传递
  if (menu.path.includes('_id')) {
    route.props = true
    // console.log('route.props has been set to true');
  }

  if (menu.children) {
    route.children = menu.children.map(createRoute)
  }

  return route
}

const userStore = useStore()
let hasToken = userStore.token
const hasMenus: Menu[] = userStore.menus

if (!hasToken) {
  hasToken = Cookies.get('token') || ''
  if (hasToken) {
    userStore.token = hasToken
  }
}

if (hasToken && !userStore.routes.length) {
  const routes = hasMenus.map(createRoute)

  routes.forEach(route => {
    router.addRoute('ats', route as any)
  })

  userStore.routes = routes as any
}

app.use(router)
app.directive('permission', permissionDirective)
app.use(ElementPlus,{locale:zhCN})
app.mount('#app')
