// // 1. 创建一个Vue路由器实例，定义了一些静态路由。
// // 2. 定义一个白名单数组，包含了不需要验证的路由。
// // 3. 在每次路由改变之前，执行一个函数来检查用户的登录状态和权限。
// // 4. 在这个函数中，首先从Pinia的store中获取token，如果没有token，那么从Cookies中获取。
// // 5. 如果有token，那么检查当前的路由是否是登录页面，如果是，那么重定向到主页。如果不是，那么检查是否已经有了动态路由，如果没有，那么根据menus生成动态路由并添加到路由器中，然后存储到userStore中。
// // 6. 如果没有token，那么检查当前的路由是否在白名单中，如果在，那么直接进入，如果不在，那么重定向到登录页面。
// // 7. 使用Vue的watch函数，当token发生改变时，将新的token保存到Cookies中。

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useStore } from '@/stores'
import Cookies from 'js-cookie'
import { watch } from 'vue'
import { close, start } from '@/util/nprogress';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/login.vue')
    },
    {
      path: '/ats',
      name: 'ats',
      component: () => import('@/components/layout/Home.vue'),
      meta: {
        title: 'ATS'
      },
      children: [
        {
          path: '/ats/index',
          name: '首页',
          component: () => import('@/views/ats/index.vue'),
          meta: {
            title: '首页'
          }
        }
      ]
    },
  ]
})

const whiteList = ['/login']

interface Menu {
  path: string;
  name: string;
  component: string;
  meta: {
    title: string;
    if_hidden: number
  };
  children?: Menu[];
}

interface Route {
  path: string;
  name: string;
  meta: {
    title: string;
  };
  component?: () => Promise<typeof import("*.vue")>;
  props?: boolean;
  children?: Route[];
}

function createRoute(menu: Menu): Route {
  let route: Route = {
    path: menu.path,
    name: menu.name,
    meta: {
      title: menu.meta.title,
    }
  }

  // vite语法让我们可以使用import.meta.glob来获取所有的.vue文件,此时@可以识别
  const modules = import.meta.glob('@/views/**/*.vue');

  // 当代码运行时，尝试从modules对象中使用带有@的路径来获取模块，这时@已经没有特殊含义了，因为它是在构建时解析的。所以以/src开始而不能用@
  if (menu.component !== '#') {
    route.component = modules[`/src/${menu.component}.vue`] as () => Promise<typeof import("*.vue")>;
  }

  // 适配路由参数传递
  if (menu.path.includes('_id')) {
    route.props = true
    // console.log('route.props has been set to true');
  }

  if (menu.children) {
    route.children = menu.children.map(createRoute)
  }

  return route
}

router.beforeEach((to, from, next) => {
  start();

  const userStore = useStore()
  let hasToken = userStore.token
  const hasMenus: Menu[] = userStore.menus
  const hasRoutes = userStore.routes

  if (!hasToken) {
    hasToken = Cookies.get('token') || ''
    if (hasToken) {
      userStore.token = hasToken
    }
  }

  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/ats/index' })
    } else {
      if (!hasRoutes.length) {
        const routes = hasMenus.map(createRoute)

        routes.forEach(route => {
          router.addRoute('ats', route as RouteRecordRaw)
        })

        userStore.routes = routes as any
      }
      next()
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next('/login')
    }
  }

  router.afterEach(() => {
    close()
  })

  watch(() => userStore.token, (newToken) => {
    if (newToken) {
      Cookies.set('token', newToken)
    } else {
      Cookies.remove('token')
    }
  }, { immediate: true })
})

export default router