/// <reference types="vite/client" />
declare module "*.vue" {
    import type { DefineComponent } from "vue";
       const vueComponent: DefineComponent<{}, {}, any>;
       export default vueComponent;
  }

declare module 'element-plus/dist/locale/zh-cn.mjs'
// declarations.d.ts
declare module 'nprogress';

// 将VueJsoneditor设为any
declare module 'vue3-ts-jsoneditor' {
  const VueJsoneditor: any;
  export default VueJsoneditor;
}

